BOOL CGameData::LoadFestivalActWarOrderLevelClientCfg()
{
    std::string DataPath = "data/FestivalActWarOrderLevelClient.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        GSLogError(":LoadFestivalActWarOrderLevelCfg Failed!  [%s]", DataPath.c_str());
        return FALSE;
    }
    
    m_mapFestivalActWarOrderLevelCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stFestivalActWarOrderLevelClientCfg cfg;
        fread(&cfg, sizeof(stFestivalActWarOrderLevelClientCfg), 1, fp);

        m_mapFestivalActWarOrderLevelCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}