BOOL CGameData::LoadFestivalActClientCfg()
{
    std::string DataPath = "data/FestivalActClient.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        GSLogError(":LoadFestivalActCfg Failed!  [%s]", DataPath.c_str());
        return FALSE;
    }
    
    m_mapFestivalActCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stFestivalActClientCfg cfg;
        fread(&cfg, sizeof(stFestivalActClientCfg), 1, fp);

        m_mapFestivalActCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}