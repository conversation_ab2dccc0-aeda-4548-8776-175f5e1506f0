Windows环境下编译指令:
====================

# 使用Visual Studio 2008命令行编译
## 打开VS2008命令提示符: 开始菜单 -> Microsoft Visual Studio 2008 -> Visual Studio Tools -> Visual Studio 2008 命令提示符
cl test_binary_reader.cpp

# 或者使用Visual Studio 2008的IDE
1. 打开Visual Studio 2008
2. 文件 -> 新建 -> 项目
3. 选择Visual C++ -> Win32 -> Win32控制台应用程序
4. 添加项目名称，例如"BinaryReader"，点击"确定"
5. 在"应用程序设置"页面，选择"控制台应用程序"和"空项目"，点击"完成"
6. 右键点击"源文件"文件夹 -> 添加 -> 现有项... -> 选择test_binary_reader.cpp
7. 按F7编译或F5运行

# 较新版本Visual Studio (VS2010及以上)
cl /EHsc test_binary_reader.cpp

# MinGW/GCC编译
g++ -o test_binary_reader test_binary_reader.cpp

运行指令:
====================

# 使用默认文件名运行
test_binary_reader

# 指定二进制文件路径运行
test_binary_reader 路径\FestivalQuestClient.SData

注意事项:
====================

1. 确保二进制文件与可执行文件在同一目录下，或者提供正确的路径
2. 本程序假设数据使用小端序(little-endian)存储，与Python struct模块默认格式一致
3. 如果读取出现乱码，可能需要检查字符编码问题
4. VS2008和较老版本的编译器可能不支持UTF-8编码，可能需要将字符串保存为ASCII或GB2312格式

VS2008特别说明:
====================

1. 程序已经修改为C++98兼容格式，能在VS2008下正常编译
2. 使用了C标准库函数代替C++异常处理
3. 修改了格式化指示符以兼容VS2008
4. 变量声明放在了函数开头，符合C++98标准

故障排除:
====================

如果遇到字符串乱码：
- VS2008默认使用系统本地编码，中文Windows通常是GB2312或GBK编码
- 确认Python端使用相同的字符编码生成二进制文件
- 尝试使用WriteConsoleW等API来正确显示Unicode字符

如果数据结构解析错误：
- 比较二进制文件与结构体定义，确保字段顺序和大小一致
- 检查指针和整数类型的大小，确保32位/64位兼容性
- 已添加#pragma pack(1)以确保紧凑对齐 