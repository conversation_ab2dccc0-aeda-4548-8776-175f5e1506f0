struct stSheet2ClientCfg
{
    DWORD    dwGridId;                  // 棋盘序号key
    DWORD    dwBoardId;                 // 对应棋盘id/自身组id
    DWORD    dwRow;                     // 行
    DWORD    dwCol;                     // 列
    BYTE     byGridType;                // 棋盘类型
    DWORD    dwNextId;                  // 前进序号key
    DWORD    dwRefreshId;               // 刷新编号
    char     szTest[36];                // 测试
    char     szTest2[2][36];            // 测试2
};
