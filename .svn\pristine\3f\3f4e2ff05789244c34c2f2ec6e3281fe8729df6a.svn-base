BOOL CGameData::LoadFestivalQuestClientCfg()
{
    std::string DataPath = "data/FestivalQuestClient.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        GSLogError(":LoadFestivalQuestCfg Failed!  [%s]", DataPath.c_str());
        return FALSE;
    }
    
    m_mapFestivalQuestCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stFestivalQuestClientCfg cfg;
        fread(&cfg, sizeof(stFestivalQuestClientCfg), 1, fp);

        m_mapFestivalQuestCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}