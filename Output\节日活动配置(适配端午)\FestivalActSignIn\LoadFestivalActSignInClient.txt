BOOL CGameData::LoadFestivalActSignInClientCfg()
{
    std::string DataPath = "data/FestivalActSignInClient.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        GSLogError(":LoadFestivalActSignInCfg Failed!  [%s]", DataPath.c_str());
        return FALSE;
    }
    
    m_mapFestivalActSignInCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stFestivalActSignInClientCfg cfg;
        fread(&cfg, sizeof(stFestivalActSignInClientCfg), 1, fp);

        m_mapFestivalActSignInCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}