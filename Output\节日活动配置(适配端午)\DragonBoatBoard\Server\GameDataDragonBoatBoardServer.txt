struct stDragonBoatBoardServerCfg
{
    DWORD    dwBoardId;                 // 棋盘id
    DWORD    dwActivityId;              // 对应活动id/棋盘组id
    DWORD    dwItemId1;                 // 消耗道具id-1
    DWORD    dwItem2;                   // 指定道具id-2
    DWORD    dwEmptyEventThreshold;     // 空事件总阈值
    DWORD    dwEmptyEventConsecutive;   // 空事件连续上限值
    DWORD    dwBackEventThreshold;      // 棋盘步数后退事件阈值上限
    DWORD    dwForwardEventThreshold;   // 棋盘步数前进事件阈值上限
    DWORD    dwSpecialEventThreshold;   // 特殊奖励事件阈值上限
};
