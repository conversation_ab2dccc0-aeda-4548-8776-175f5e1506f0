#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import struct
import re
import ast
from core.type_converter import TypeConverter
import json

class SDataParser:
    def __init__(self):
        """初始化SData解析器"""
        self.type_converter = TypeConverter()
    
    def parse_sdata_file(self, sdata_path, struct_path=None):
        """
        解析SData二进制文件，返回可读的文本表示
        
        Args:
            sdata_path: SData文件路径
            struct_path: 可选的结构体定义文件路径，如果提供则根据结构体定义解析
            
        Returns:
            str: 解析后的文本内容
        """
        if not os.path.exists(sdata_path):
            return f"文件不存在: {sdata_path}"
        
        # 尝试找到对应的结构体定义文件
        if struct_path is None:
            # 根据SData文件路径推断结构体文件路径
            dir_path = os.path.dirname(sdata_path)
            file_name = os.path.basename(sdata_path)
            file_base = os.path.splitext(file_name)[0]
            potential_struct_path = os.path.join(dir_path, f"GameData{file_base}.txt")
            
            if os.path.exists(potential_struct_path):
                struct_path = potential_struct_path
        
        # 如果有结构体定义文件，读取字段信息
        fields = []
        if struct_path and os.path.exists(struct_path):
            fields = self._parse_struct_file(struct_path)
        
        # 解析二进制文件
        try:
            with open(sdata_path, 'rb') as f:
                # 读取数据条数
                count_data = f.read(4)
                if not count_data or len(count_data) < 4:
                    return "文件格式错误或为空"
                
                count = struct.unpack('<I', count_data)[0]
                result = [f"数据条数: {count}"]
                
                # 如果没有结构体信息，只能以十六进制方式显示内容
                if not fields:
                    # 简单显示十六进制内容
                    f.seek(0)  # 回到文件开头
                    hex_data = f.read().hex()
                    formatted_hex = ' '.join(hex_data[i:i+2] for i in range(0, len(hex_data), 2))
                    
                    # 只显示前1000个字节，避免过大
                    if len(formatted_hex) > 3000:
                        result.append(f"二进制数据 (前1000字节):\n{formatted_hex[:3000]}...\n")
                    else:
                        result.append(f"二进制数据:\n{formatted_hex}\n")
                    
                    # 补充说明
                    result.append("\n注意: 无法找到结构体定义文件，只能以十六进制显示文件内容。")
                    result.append("请确保结构体定义文件 (GameData*.txt) 存在于同一目录。")
                    
                    return '\n'.join(result)
                
                # 使用结构体信息解析每条数据
                for i in range(count):
                    result.append(f"记录 #{i+1}:")
                    row_data = self._parse_row(f, fields)
                    
                    # 格式化输出每个字段
                    for field, value in row_data.items():
                        # 对于字符串数组，使用json.dumps确保使用双引号
                        if isinstance(value, list) and all(isinstance(item, str) for item in value):
                            result.append(f"  {field}: {json.dumps(value, ensure_ascii=False)}")
                        elif isinstance(value, str):
                            # 对于普通字符串，在两端添加双引号，包括空字符串
                            result.append(f'  {field}: "{value}"')
                        else:
                            result.append(f"  {field}: {value}")
                
                return '\n'.join(result)
        
        except Exception as e:
            return f"解析文件出错: {str(e)}"
    
    def parse_sdata_file_structured(self, sdata_path, struct_path=None, auto_detect_count=False):
        """
        解析SData二进制文件，返回结构化的数据，用于表格显示
        
        Args:
            sdata_path: SData文件路径
            struct_path: 可选的结构体定义文件路径，如果提供则根据结构体定义解析
            
        Returns:
            dict: 包含以下键的字典:
                'success': 是否成功解析
                'message': 如果不成功，错误消息
                'count': 记录数量
                'fields': 字段列表，包含字段名和注释
                'rows': 行数据列表，每行是一个字典，键为字段名，值为字段值
        """
        result = {
            'success': False,
            'message': '',
            'count': 0,
            'fields': [],
            'rows': []
        }
        
        if not os.path.exists(sdata_path):
            result['message'] = f"文件不存在: {sdata_path}"
            return result
        
        # 尝试找到对应的结构体定义文件
        if struct_path is None:
            # 根据SData文件路径推断结构体文件路径
            dir_path = os.path.dirname(sdata_path)
            file_name = os.path.basename(sdata_path)
            file_base = os.path.splitext(file_name)[0]
            potential_struct_path = os.path.join(dir_path, f"GameData{file_base}.txt")
            
            if os.path.exists(potential_struct_path):
                struct_path = potential_struct_path
        
        # 如果有结构体定义文件，读取字段信息
        fields = []
        if struct_path and os.path.exists(struct_path):
            fields = self._parse_struct_file(struct_path)
        
        # 如果没有找到字段信息，返回错误
        if not fields:
            result['message'] = "无法找到结构体定义文件，或结构体定义为空"
            return result
        
        # 提取字段信息
        result['fields'] = [{
            'name': field['name'],
            'comment': field['comment'] or field['name'],
            'type': field['type']
        } for field in fields]
        
        # 解析二进制文件
        try:
            with open(sdata_path, 'rb') as f:
                # 读取数据条数
                count_data = f.read(4)
                if not count_data or len(count_data) < 4:
                    result['message'] = "文件格式错误或为空"
                    return result

                count = struct.unpack('<I', count_data)[0]

                # 如果启用自动检测，验证并可能修正数据行数
                if auto_detect_count:
                    count = self._auto_detect_record_count(f, fields, count)

                result['count'] = count
                
                # 使用结构体信息解析每条数据
                for i in range(count):
                    row_data = self._parse_row(f, fields)
                    
                    # 格式化输出每个字段的值
                    formatted_row = {}
                    for field_name, value in row_data.items():
                        # 获取对应字段的类型信息
                        field_type = None
                        for field in fields:
                            if field['name'] == field_name:
                                field_type = field['type']
                                break

                        if isinstance(value, list) and all(isinstance(item, str) for item in value):
                            # 对于字符串数组
                            non_empty_values = [v for v in value if v]
                            if non_empty_values:
                                formatted_row[field_name] = str(non_empty_values)
                            else:
                                formatted_row[field_name] = "[]"
                        elif isinstance(value, list):
                            # 检查是否是二维数组（列表的列表）
                            if value and isinstance(value[0], list):
                                # 二维数组：显示为 [[a, b], [c, d]] 格式
                                formatted_row[field_name] = str(value)
                            else:
                                # 一维数组：显示为 [a, b, c] 格式
                                formatted_row[field_name] = str(value)
                        elif isinstance(value, str):
                            # 对于字符串类型，不在这里添加双引号，让界面层处理
                            formatted_row[field_name] = value
                        else:
                            # 对于简单类型
                            formatted_row[field_name] = value
                    
                    result['rows'].append(formatted_row)
                
                result['success'] = True
                return result
        
        except Exception as e:
            result['message'] = f"解析文件出错: {str(e)}"
            return result
    
    def _parse_struct_file(self, struct_path):
        """
        解析结构体定义文件，提取字段信息
        
        Args:
            struct_path: 结构体文件路径
            
        Returns:
            list: 字段信息列表
        """
        fields = []
        
        try:
            with open(struct_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 预处理内容，修复注释中的换行问题
                # 先找到所有的字段定义行
                field_lines = re.findall(r'(\s*\w+\s+\w+(?:\[\d+\])?(?:\[\d+\])?;\s*//\s*.*?)(?=\s*\w+\s+\w+|$)', content, re.DOTALL)
                
                # 处理每个字段定义，将注释部分的换行替换为空格
                for original_line in field_lines:
                    # 提取字段定义部分和注释部分
                    field_def_match = re.match(r'(\s*\w+\s+\w+(?:\[\d+\])?(?:\[\d+\])?;\s*//\s*)(.*)', original_line, re.DOTALL)
                    if field_def_match:
                        field_def = field_def_match.group(1)
                        comment = field_def_match.group(2)
                        
                        # 将注释中的换行替换为空格
                        fixed_comment = re.sub(r'\s*\n\s*', ' ', comment)
                        fixed_line = field_def + fixed_comment
                        
                        # 替换原内容中的字段定义
                        content = content.replace(original_line, fixed_line)
                
                # 查找结构体定义
                struct_match = re.search(r'struct\s+(\w+)\s*{(.*?)};', content, re.DOTALL)

                # 如果没有找到完整的struct定义，尝试直接解析字段
                if not struct_match:
                    # 尝试查找是否有大括号包围的内容
                    brace_match = re.search(r'{(.*?)}', content, re.DOTALL)
                    if brace_match:
                        struct_body = brace_match.group(1)
                    else:
                        # 如果没有大括号，直接使用整个内容作为结构体主体
                        struct_body = content
                else:
                    struct_body = struct_match.group(2)
                
                # 提取每个字段定义
                field_pattern = r'\s*(\w+)\s+(\w+)(?:\[(\d+)\])?(?:\[(\d+)\])?;(?:\s*//\s*(.*))?'
                field_matches = re.finditer(field_pattern, struct_body)
                
                for match in field_matches:
                    cpp_type = match.group(1)
                    name = match.group(2)
                    array_size1 = match.group(3)
                    array_size2 = match.group(4)
                    comment = match.group(5)
                    
                    # 转换C++类型为对应的Python类型
                    py_type = self._cpp_to_python_type(cpp_type)
                    
                    # 构建类型字符串
                    if array_size1 and array_size2:
                        type_str = f"{py_type}[{array_size1}][{array_size2}]"
                    elif array_size1:
                        type_str = f"{py_type}[{array_size1}]"
                    else:
                        type_str = py_type
                    
                    fields.append({
                        'name': name,
                        'type': type_str,
                        'comment': comment,
                        'cpp_type': cpp_type
                    })
        
        except Exception as e:
            print(f"解析结构体文件出错: {str(e)}")
            print(f"文件内容: {content[:500]}...")  # 打印前500个字符用于调试

        return fields
    
    def _cpp_to_python_type(self, cpp_type):
        """
        将C++类型转换为对应的Python类型
        
        Args:
            cpp_type: C++类型名
            
        Returns:
            str: Python类型名
        """
        cpp_to_py = {
            'DWORD': 'uint',
            'BYTE': 'byte',
            'WORD': 'ushort',
            'FLOAT': 'float',
            'INT': 'int',
            'double': 'double',
            'bool': 'bool',
            'char': 'char',
            'short': 'short',
            'float': 'float',
            'ULONGLONG': 'ulonglong'
        }
        
        return cpp_to_py.get(cpp_type, 'int')  # 默认为int
    
    def _parse_row(self, file_handle, fields):
        """
        从二进制文件中解析一行数据

        Args:
            file_handle: 已打开的文件句柄
            fields: 字段信息列表

        Returns:
            dict: 解析后的数据行
        """
        row_data = {}

        for field in fields:
            name = field['name']
            field_type = field['type']

            # 根据字段类型解析二进制数据
            value = self._parse_field(file_handle, field_type)
            row_data[name] = value

        return row_data

    def _get_field_size(self, field_type):
        """计算字段的大小（字节数）"""
        # 处理数组类型
        if '[' in field_type:
            # 解析数组类型，如 "uint[5]" 或 "char[32]"
            base_type = field_type.split('[')[0]

            # 计算数组大小
            array_parts = field_type.split('[')[1:]
            total_size = 1
            for part in array_parts:
                size = int(part.rstrip(']'))
                total_size *= size

            # 获取基础类型大小
            base_size = self._get_simple_type_size(base_type)
            return base_size * total_size
        else:
            return self._get_simple_type_size(field_type)

    def _get_simple_type_size(self, type_name):
        """获取简单类型的大小"""
        size_map = {
            'byte': 1,
            'char': 1,
            'short': 2,
            'ushort': 2,
            'int': 4,
            'uint': 4,
            'long': 4,
            'ulong': 4,
            'float': 4,
            'double': 8
        }
        return size_map.get(type_name.lower(), 4)  # 默认4字节

    def _get_field_alignment(self, field_type):
        """计算字段的对齐要求"""
        # 处理数组类型
        if '[' in field_type:
            base_type = field_type.split('[')[0]
            return self._get_simple_type_alignment(base_type)
        else:
            return self._get_simple_type_alignment(field_type)

    def _get_simple_type_alignment(self, type_name):
        """获取简单类型的对齐要求"""
        # 在大多数系统上，对齐要求等于类型大小，但有最大限制
        size = self._get_simple_type_size(type_name)
        return min(size, 4)  # 最大4字节对齐

    def _parse_field(self, file_handle, field_type):
        """
        解析单个字段的二进制数据
        
        Args:
            file_handle: 已打开的文件句柄
            field_type: 字段类型
            
        Returns:
            解析后的字段值
        """
        # 检查是否是二维数组
        two_dim_array_match = re.match(r'(\w+)\[(\d+)\]\[(\d+)\]', field_type)
        if two_dim_array_match:
            base_type = two_dim_array_match.group(1)
            rows = int(two_dim_array_match.group(2))
            cols = int(two_dim_array_match.group(3))
            
            # 特殊处理char二维数组
            if base_type == 'char':
                result = []
                for i in range(rows):
                    # 读取每行字符串
                    char_array = file_handle.read(cols)
                    
                    # 找到第一个空字符的位置
                    try:
                        null_pos = char_array.index(0)
                        # 只有含有有效数据的才解析
                        if null_pos > 0:
                            # 截取到空字符前的内容并使用GB2312解码
                            string_value = char_array[:null_pos].decode('gb2312', errors='replace')
                            result.append(string_value)
                        else:
                            # 全零字节，表示空字符串
                            result.append("")
                    except (ValueError, UnicodeDecodeError):
                        # 如果没有空字符或解码失败，尝试解码整个数组
                        try:
                            string_value = char_array.decode('gb2312', errors='replace').rstrip('\0')
                            if string_value:
                                result.append(string_value)
                            else:
                                result.append("")
                        except:
                            # GB2312解码失败，尝试其他编码
                            try:
                                string_value = char_array.decode('utf-8', errors='replace').rstrip('\0')
                                if string_value:
                                    result.append(string_value)
                                else:
                                    result.append("")
                            except:
                                result.append("")
                
                # 过滤掉所有空字符串，除非整个数组都是空的
                non_empty_strings = [s for s in result if s]
                if non_empty_strings:
                    return non_empty_strings
                else:
                    return []
            else:
                # 其他类型的二维数组
                result = []
                item_size = self._get_type_size(base_type)
                for i in range(rows):
                    row_data = []
                    for j in range(cols):
                        value = self._parse_simple_type(file_handle, base_type)
                        row_data.append(value)
                    result.append(row_data)
                return result
        
        # 检查是否是数组类型
        array_match = re.match(r'(\w+)\[(\d+)\]', field_type)
        if array_match:
            base_type = array_match.group(1)
            size = int(array_match.group(2))
            
            # 特殊处理char数组（字符串）
            if base_type == 'char':
                # 读取字符串
                char_array = file_handle.read(size)
                
                # 找到第一个空字符的位置
                try:
                    null_pos = char_array.index(0)
                    # 截取到空字符前的内容并使用GB2312解码
                    return char_array[:null_pos].decode('gb2312', errors='replace')
                except ValueError:
                    # 如果没有空字符，解码整个数组
                    return char_array.decode('gb2312', errors='replace').rstrip('\0')
                except UnicodeDecodeError:
                    # GB2312解码失败时尝试其他编码
                    try:
                        # 尝试UTF-8编码
                        return char_array.decode('utf-8', errors='replace').rstrip('\0')
                    except:
                        # 如果还是解码失败，使用Latin-1（可以处理任何字节）
                        return char_array.decode('latin-1', errors='replace').rstrip('\0')
            else:
                # 其他类型的数组
                result = []
                for i in range(size):
                    value = self._parse_simple_type(file_handle, base_type)
                    result.append(value)
                return result
        else:
            # 简单类型
            return self._parse_simple_type(file_handle, field_type)
    
    def _parse_simple_type(self, file_handle, type_str):
        """
        解析简单类型的二进制数据
        
        Args:
            file_handle: 已打开的文件句柄
            type_str: 类型字符串
            
        Returns:
            解析后的值
        """
        # 类型对应的struct格式字符
        format_map = {
            'uint': '<I',      # 无符号整数 (4字节)
            'ulong': '<I',     # 无符号长整型 (4字节)
            'byte': '<B',      # 无符号字符 (1字节)
            'ushort': '<H',    # 无符号短整型 (2字节)
            'ufloat': '<f',    # 浮点数 (4字节)
            'ulonglong': '<Q', # 无符号长长整型 (8字节)
            'int': '<i',       # 有符号整数 (4字节)
            'double': '<d',    # 双精度浮点数 (8字节)
            'bool': '<?',      # 布尔值 (1字节)
            'char': '<c',      # 字符 (1字节)
            'short': '<h',     # 有符号短整型 (2字节)
            'float': '<f'      # 浮点数 (4字节)
        }
        
        # 获取格式字符
        format_char = format_map.get(type_str, '<i')  # 默认为整型
        format_size = struct.calcsize(format_char)
        
        # 读取数据
        data = file_handle.read(format_size)
        if len(data) < format_size:
            raise ValueError(f"文件数据不足，无法读取{type_str}类型")
        
        # 解析数据
        value = struct.unpack(format_char, data)[0]
        
        # 特殊处理字符类型
        if type_str == 'char':
            try:
                # 尝试使用GB2312解码
                return value.decode('gb2312')
            except (UnicodeDecodeError, AttributeError):
                # GB2312解码失败，尝试UTF-8
                try:
                    return value.decode('utf-8')
                except:
                    # 所有尝试失败，使用字符串表示
                    return str(value)
        
        return value 

    def _get_type_size(self, type_str):
        """
        获取类型的字节大小
        
        Args:
            type_str: 类型字符串
            
        Returns:
            int: 字节大小
        """
        # 类型对应的struct格式字符
        format_map = {
            'uint': '<I',      # 无符号整数 (4字节)
            'ulong': '<I',     # 无符号长整型 (4字节)
            'byte': '<B',      # 无符号字符 (1字节)
            'ushort': '<H',    # 无符号短整型 (2字节)
            'ufloat': '<f',    # 浮点数 (4字节)
            'ulonglong': '<Q', # 无符号长长整型 (8字节)
            'int': '<i',       # 有符号整数 (4字节)
            'double': '<d',    # 双精度浮点数 (8字节)
            'bool': '<?',      # 布尔值 (1字节)
            'char': '<c',      # 字符 (1字节)
            'short': '<h',     # 有符号短整型 (2字节)
            'float': '<f'      # 浮点数 (4字节)
        }
        
        # 获取格式字符
        format_char = format_map.get(type_str, '<i')  # 默认为整型
        return struct.calcsize(format_char)

    def _decode_bytes(self, byte_data):
        """
        尝试各种编码方式解码字节数据为字符串
        
        Args:
            byte_data: 字节数据
            
        Returns:
            str: 解码后的字符串
        """
        if not byte_data:
            return ""
        
        # 首先尝试GB2312编码（与C++端项目设置一致）
        try:
            return byte_data.decode('gb2312').rstrip('\0')
        except UnicodeDecodeError:
            pass
        
        # 然后尝试UTF-8编码
        try:
            return byte_data.decode('utf-8').rstrip('\0')
        except UnicodeDecodeError:
            pass
        
        # 尝试更通用的cp936编码（中文Windows的编码）
        try:
            return byte_data.decode('cp936').rstrip('\0')
        except UnicodeDecodeError:
            pass
        
        # 尝试latin-1编码（可以解码任何字节）
        try:
            result = byte_data.decode('latin-1').rstrip('\0')
            # 检查结果是否包含可读字符
            if any(c.isprintable() for c in result):
                return result
        except:
            pass
            
        # 所有解码方法失败，返回十六进制表示
        hex_repr = ' '.join(f'{b:02x}' for b in byte_data[:min(len(byte_data), 20)])
        if len(byte_data) > 20:
            hex_repr += ' ...'
        return f"[HEX: {hex_repr}]"

    def _auto_detect_record_count(self, file_handle, fields, initial_count):
        """
        自动检测记录数量

        Args:
            file_handle: 文件句柄
            fields: 字段定义列表
            initial_count: 从文件头读取的初始数量

        Returns:
            int: 推导出的实际记录数量
        """
        try:
            # 计算单条记录的大小
            record_size = 0
            for field in fields:
                field_type = field['type']
                record_size += self._calculate_field_size(field_type)

            if record_size <= 0:
                return initial_count

            # 获取文件总大小
            current_pos = file_handle.tell()
            file_handle.seek(0, 2)  # 移动到文件末尾
            file_size = file_handle.tell()
            file_handle.seek(current_pos)  # 恢复原位置

            # 计算数据部分的大小（文件总大小 - 4字节的数量头）
            data_size = file_size - 4

            # 根据记录大小推导记录数量
            calculated_count = data_size // record_size

            # 如果计算出的数量与初始数量差异很大，使用计算出的数量
            if abs(calculated_count - initial_count) > initial_count * 0.1:  # 差异超过10%
                print(f"自动检测：文件大小={file_size}, 记录大小={record_size}, 计算数量={calculated_count}, 初始数量={initial_count}")
                return calculated_count

            return initial_count

        except Exception as e:
            print(f"自动检测记录数量失败: {str(e)}")
            return initial_count

    def _calculate_field_size(self, field_type):
        """
        计算字段大小

        Args:
            field_type: 字段类型字符串

        Returns:
            int: 字段大小（字节）
        """
        # 检查是否是二维数组
        two_dim_array_match = re.match(r'(\w+)\[(\d+)\]\[(\d+)\]', field_type)
        if two_dim_array_match:
            base_type = two_dim_array_match.group(1)
            rows = int(two_dim_array_match.group(2))
            cols = int(two_dim_array_match.group(3))
            base_size = self._get_type_size(base_type)
            return base_size * rows * cols

        # 检查是否是一维数组
        array_match = re.match(r'(\w+)\[(\d+)\]', field_type)
        if array_match:
            base_type = array_match.group(1)
            size = int(array_match.group(2))
            base_size = self._get_type_size(base_type)
            return base_size * size

        # 简单类型
        return self._get_type_size(field_type)
