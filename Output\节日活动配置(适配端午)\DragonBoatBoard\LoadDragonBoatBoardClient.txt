BOOL CGameData::LoadDragonBoatBoardClientCfg()
{
    std::string DataPath = "data/DragonBoatBoardClient.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        GSLogError(":LoadDragonBoatBoardCfg Failed!  [%s]", DataPath.c_str());
        return FALSE;
    }
    
    m_mapDragonBoatBoardCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stDragonBoatBoardClientCfg cfg;
        fread(&cfg, sizeof(stDragonBoatBoardClientCfg), 1, fp);

        m_mapDragonBoatBoardCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}