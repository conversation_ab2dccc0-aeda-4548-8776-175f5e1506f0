{"config_path": "C:\\MyFileAudit\\导表工具\\Config", "output_path": "C:\\MyFileAudit\\导表工具\\Output", "theme": "light", "sdata_paths": {"test1_Sheet1_Server": "C:/MyFileAudit/NewProject", "test1_Sheet1_Client": "C:/MyFileAudit/NewProject", "test1_Sheet2_Server": "C:/MyFileAudit/NewProject", "test1_Sheet2_Client": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_CurrencySystem_Server": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_CurrencySystem_Client": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_FestivalQuestActive_Server": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_FestivalQuestActive_Client": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_DragonBoatGrid_Server": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_DragonBoatGrid_Client": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_DragonBoatEvent_Server": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_DragonBoatEvent_Client": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_DragonBoatBoard_Server": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_DragonBoatBoard_Client": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_DragonBoatRankReward_Server": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_DragonBoatRankReward_Client": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_FestivalAct_Server": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_FestivalAct_Client": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_FestivalActController_Server": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_FestivalActController_Client": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_FestivalShop_Server": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_FestivalShop_Client": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_FestivalActSignIn_Server": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_FestivalActSignIn_Client": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_FestivalQuest_Server": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_FestivalQuest_Client": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_FestivalActWarOrderLevel_Server": "C:/MyFileAudit/NewProject", "节日活动配置(适配端午)_FestivalActWarOrderLevel_Client": ""}, "export_sdata_custom": false, "window": {"x": 124, "y": 92, "width": 1484, "height": 809, "splitter_sizes": [853, 599]}}