BOOL CGameData::LoadSheet2ServerCfg()
{
    std::string DataPath = "data/Sheet2Server.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        ::g_pClientToLog->PrintFileDirct("CDataFile::LoadSheet2Cfg fopen error");
        return FALSE;
    }
    
    m_mapSheet2Cfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stSheet2ServerCfg cfg;
        fread(&cfg, sizeof(stSheet2ServerCfg), 1, fp);

        m_mapSheet2Cfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}