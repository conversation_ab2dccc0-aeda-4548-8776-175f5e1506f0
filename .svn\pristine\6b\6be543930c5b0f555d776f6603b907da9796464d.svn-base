struct stSheet1ServerCfg
{
    DWORD    dwGridId;                  // 棋盘序号key
    DWORD    dwBoardId;                 // 对应棋盘id/自身组id
    DWORD    dwNextId;                  // 前进序号key
    DWORD    dwRefreshId;               // 刷新编号
    DWORD    dwEmptyEventRate;          // 空事件概率 (万分比)
    DWORD    dwBackEventRate;           // 步数后退事件概率 (万分比)
    DWORD    dwBackEventIds[10];        // 后退事件库
    DWORD    dwBackEventWeights[10];    // 后退事件对应权重
    DWORD    dwForwardEventRate;        // 步数前进事件概率 (万分比)
    DWORD    dwForwardEventIds[10];     // 前进事件库
    DWORD    dwForwardEventWeights[10]; // 前进事件对应权重
    DWORD    dwSpecialEventRate;        // 特殊奖励事件概率 (万分比)
    DWORD    dwSpecialEventWeights[10]; // 特殊奖励事件概率
    DWORD    dwRewardEventIds[10];      // 宝箱道具奖励事件库
    DWORD    dwRewardEventWeights[10];  // 宝箱道具奖励事件对应权重
};
