BOOL CGameData::LoadFestivalActWarOrderLevelServerCfg()
{
    std::string DataPath = "data/FestivalActWarOrderLevelServer.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        ::g_pClientToLog->PrintFileDirct("CDataFile::LoadFestivalActWarOrderLevelCfg fopen error");
        return FALSE;
    }
    
    m_mapFestivalActWarOrderLevelCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stFestivalActWarOrderLevelServerCfg cfg;
        fread(&cfg, sizeof(stFestivalActWarOrderLevelServerCfg), 1, fp);

        m_mapFestivalActWarOrderLevelCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}