#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PIL import Image, ImageDraw
import os

def create_directory(directory):
    if not os.path.exists(directory):
        os.makedirs(directory)

def create_triangle_icon(output_file, points, bg_color=(255, 255, 255, 0), triangle_color=(85, 85, 85)):
    # 创建透明背景的图像
    img = Image.new('RGBA', (16, 16), bg_color)
    draw = ImageDraw.Draw(img)
    
    # 绘制三角形
    draw.polygon(points, fill=triangle_color)
    
    # 保存图像
    img.save(output_file, 'PNG')
    print(f"Created icon: {output_file}")

# 创建图标目录
icons_dir = os.path.join("resources", "icons")
create_directory(icons_dir)

# 创建浅色主题图标
# 创建关闭状态的图标（指向右侧的三角形）
closed_points = [(5, 4), (11, 8), (5, 12)]
create_triangle_icon(os.path.join(icons_dir, "branch-closed.png"), closed_points)

# 创建打开状态的图标（指向下方的三角形）
open_points = [(4, 5), (12, 5), (8, 11)]
create_triangle_icon(os.path.join(icons_dir, "branch-open.png"), open_points)

# 创建深色主题图标 - 使用浅色三角形
# 创建关闭状态的图标（指向右侧的三角形）
closed_points_dark = [(5, 4), (11, 8), (5, 12)]
create_triangle_icon(os.path.join(icons_dir, "branch-closed-dark.png"), closed_points_dark, triangle_color=(200, 200, 200))

# 创建打开状态的图标（指向下方的三角形）
open_points_dark = [(4, 5), (12, 5), (8, 11)]
create_triangle_icon(os.path.join(icons_dir, "branch-open-dark.png"), open_points_dark, triangle_color=(200, 200, 200))

print("Icons created successfully!") 