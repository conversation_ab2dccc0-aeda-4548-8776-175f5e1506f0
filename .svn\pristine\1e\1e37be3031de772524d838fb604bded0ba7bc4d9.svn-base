BOOL CGameData::LoadFestivalQuestActiveClientCfg()
{
    std::string DataPath = "data/FestivalQuestActiveClient.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        GSLogError(":LoadFestivalQuestActiveCfg Failed!  [%s]", DataPath.c_str());
        return FALSE;
    }
    
    m_mapFestivalQuestActiveCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stFestivalQuestActiveClientCfg cfg;
        fread(&cfg, sizeof(stFestivalQuestActiveClientCfg), 1, fp);

        m_mapFestivalQuestActiveCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}