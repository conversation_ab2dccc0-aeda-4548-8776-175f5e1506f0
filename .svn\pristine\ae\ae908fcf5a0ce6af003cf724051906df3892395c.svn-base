数据条数: 56
dwId (uint):1004001
wLevel (uint):1
wAccumulatedExp (uint):1000
dwNeedStamps (uint):50
dwReward (uint[2]):[1001, 1]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1001, 3]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004002
wLevel (uint):2
wAccumulatedExp (uint):2000
dwNeedStamps (uint):50
dwReward (uint[2]):[1002, 2]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1002, 6]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004003
wLevel (uint):3
wAccumulatedExp (uint):3000
dwNeedStamps (uint):50
dwReward (uint[2]):[0, 0]
dwCurrency (uint[2]):[1001, 1]
dwPayReward (uint[2]):[0, 0]
dwPayCurrency (uint[2]):[1001, 5]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004004
wLevel (uint):4
wAccumulatedExp (uint):4000
dwNeedStamps (uint):50
dwReward (uint[2]):[1004, 4]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1004, 12]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004005
wLevel (uint):5
wAccumulatedExp (uint):5000
dwNeedStamps (uint):50
dwReward (uint[2]):[1005, 5]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1005, 15]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004006
wLevel (uint):6
wAccumulatedExp (uint):6000
dwNeedStamps (uint):50
dwReward (uint[2]):[0, 0]
dwCurrency (uint[2]):[1001, 1]
dwPayReward (uint[2]):[0, 0]
dwPayCurrency (uint[2]):[1001, 5]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004007
wLevel (uint):7
wAccumulatedExp (uint):7000
dwNeedStamps (uint):50
dwReward (uint[2]):[1007, 7]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1007, 21]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004008
wLevel (uint):8
wAccumulatedExp (uint):8000
dwNeedStamps (uint):50
dwReward (uint[2]):[0, 0]
dwCurrency (uint[2]):[1001, 1]
dwPayReward (uint[2]):[0, 0]
dwPayCurrency (uint[2]):[1001, 5]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004009
wLevel (uint):9
wAccumulatedExp (uint):9000
dwNeedStamps (uint):50
dwReward (uint[2]):[1009, 9]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1009, 27]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004010
wLevel (uint):10
wAccumulatedExp (uint):10000
dwNeedStamps (uint):50
dwReward (uint[2]):[1010, 10]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1010, 30]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004011
wLevel (uint):11
wAccumulatedExp (uint):11000
dwNeedStamps (uint):50
dwReward (uint[2]):[0, 0]
dwCurrency (uint[2]):[1001, 1]
dwPayReward (uint[2]):[0, 0]
dwPayCurrency (uint[2]):[1001, 5]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004012
wLevel (uint):12
wAccumulatedExp (uint):12000
dwNeedStamps (uint):50
dwReward (uint[2]):[1012, 12]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1012, 36]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004013
wLevel (uint):13
wAccumulatedExp (uint):13000
dwNeedStamps (uint):50
dwReward (uint[2]):[1013, 13]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1013, 39]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004014
wLevel (uint):14
wAccumulatedExp (uint):14000
dwNeedStamps (uint):50
dwReward (uint[2]):[0, 0]
dwCurrency (uint[2]):[1001, 1]
dwPayReward (uint[2]):[0, 0]
dwPayCurrency (uint[2]):[1001, 5]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004015
wLevel (uint):15
wAccumulatedExp (uint):15000
dwNeedStamps (uint):50
dwReward (uint[2]):[1015, 15]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1015, 45]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004016
wLevel (uint):16
wAccumulatedExp (uint):16000
dwNeedStamps (uint):50
dwReward (uint[2]):[1016, 16]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1016, 48]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004017
wLevel (uint):17
wAccumulatedExp (uint):17000
dwNeedStamps (uint):50
dwReward (uint[2]):[1017, 17]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1017, 51]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004018
wLevel (uint):18
wAccumulatedExp (uint):18000
dwNeedStamps (uint):50
dwReward (uint[2]):[0, 0]
dwCurrency (uint[2]):[1001, 1]
dwPayReward (uint[2]):[0, 0]
dwPayCurrency (uint[2]):[1001, 5]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004019
wLevel (uint):19
wAccumulatedExp (uint):19000
dwNeedStamps (uint):50
dwReward (uint[2]):[1019, 19]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1019, 57]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004020
wLevel (uint):20
wAccumulatedExp (uint):20000
dwNeedStamps (uint):50
dwReward (uint[2]):[0, 0]
dwCurrency (uint[2]):[1001, 1]
dwPayReward (uint[2]):[0, 0]
dwPayCurrency (uint[2]):[1001, 5]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004021
wLevel (uint):21
wAccumulatedExp (uint):21000
dwNeedStamps (uint):50
dwReward (uint[2]):[1021, 21]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1021, 63]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004022
wLevel (uint):22
wAccumulatedExp (uint):22000
dwNeedStamps (uint):50
dwReward (uint[2]):[0, 0]
dwCurrency (uint[2]):[1001, 1]
dwPayReward (uint[2]):[0, 0]
dwPayCurrency (uint[2]):[1001, 5]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004023
wLevel (uint):23
wAccumulatedExp (uint):23000
dwNeedStamps (uint):50
dwReward (uint[2]):[1023, 23]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1023, 69]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004024
wLevel (uint):24
wAccumulatedExp (uint):24000
dwNeedStamps (uint):50
dwReward (uint[2]):[1024, 24]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1024, 72]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004025
wLevel (uint):25
wAccumulatedExp (uint):25000
dwNeedStamps (uint):50
dwReward (uint[2]):[1025, 25]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1025, 75]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004026
wLevel (uint):26
wAccumulatedExp (uint):26000
dwNeedStamps (uint):50
dwReward (uint[2]):[0, 0]
dwCurrency (uint[2]):[1001, 1]
dwPayReward (uint[2]):[0, 0]
dwPayCurrency (uint[2]):[1002, 5]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004027
wLevel (uint):27
wAccumulatedExp (uint):27000
dwNeedStamps (uint):50
dwReward (uint[2]):[1027, 27]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1027, 81]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):1004028
wLevel (uint):28
wAccumulatedExp (uint):28000
dwNeedStamps (uint):50
dwReward (uint[2]):[1028, 28]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[0, 0]
dwPayCurrency (uint[2]):[1002, 100]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004001
wLevel (uint):1
wAccumulatedExp (uint):1000
dwNeedStamps (uint):50
dwReward (uint[2]):[1001, 1]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1001, 3]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004002
wLevel (uint):2
wAccumulatedExp (uint):2000
dwNeedStamps (uint):50
dwReward (uint[2]):[1002, 2]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1002, 6]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004003
wLevel (uint):3
wAccumulatedExp (uint):3000
dwNeedStamps (uint):50
dwReward (uint[2]):[0, 0]
dwCurrency (uint[2]):[1001, 1]
dwPayReward (uint[2]):[0, 0]
dwPayCurrency (uint[2]):[1002, 10]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004004
wLevel (uint):4
wAccumulatedExp (uint):4000
dwNeedStamps (uint):50
dwReward (uint[2]):[1004, 4]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1004, 12]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004005
wLevel (uint):5
wAccumulatedExp (uint):5000
dwNeedStamps (uint):50
dwReward (uint[2]):[1005, 5]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1005, 15]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004006
wLevel (uint):6
wAccumulatedExp (uint):6000
dwNeedStamps (uint):50
dwReward (uint[2]):[0, 0]
dwCurrency (uint[2]):[1001, 1]
dwPayReward (uint[2]):[0, 0]
dwPayCurrency (uint[2]):[1002, 10]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004007
wLevel (uint):7
wAccumulatedExp (uint):7000
dwNeedStamps (uint):50
dwReward (uint[2]):[1007, 7]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1007, 21]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004008
wLevel (uint):8
wAccumulatedExp (uint):8000
dwNeedStamps (uint):50
dwReward (uint[2]):[0, 0]
dwCurrency (uint[2]):[1001, 1]
dwPayReward (uint[2]):[0, 0]
dwPayCurrency (uint[2]):[1002, 10]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004009
wLevel (uint):9
wAccumulatedExp (uint):9000
dwNeedStamps (uint):50
dwReward (uint[2]):[1009, 9]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1009, 27]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004010
wLevel (uint):10
wAccumulatedExp (uint):10000
dwNeedStamps (uint):50
dwReward (uint[2]):[1010, 10]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1010, 30]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004011
wLevel (uint):11
wAccumulatedExp (uint):11000
dwNeedStamps (uint):50
dwReward (uint[2]):[0, 0]
dwCurrency (uint[2]):[1001, 1]
dwPayReward (uint[2]):[0, 0]
dwPayCurrency (uint[2]):[1002, 10]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004012
wLevel (uint):12
wAccumulatedExp (uint):12000
dwNeedStamps (uint):50
dwReward (uint[2]):[1012, 12]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1012, 36]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004013
wLevel (uint):13
wAccumulatedExp (uint):13000
dwNeedStamps (uint):50
dwReward (uint[2]):[1013, 13]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1013, 39]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004014
wLevel (uint):14
wAccumulatedExp (uint):14000
dwNeedStamps (uint):50
dwReward (uint[2]):[0, 0]
dwCurrency (uint[2]):[1001, 1]
dwPayReward (uint[2]):[0, 0]
dwPayCurrency (uint[2]):[1002, 10]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004015
wLevel (uint):15
wAccumulatedExp (uint):15000
dwNeedStamps (uint):50
dwReward (uint[2]):[1015, 15]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1015, 45]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004016
wLevel (uint):16
wAccumulatedExp (uint):16000
dwNeedStamps (uint):50
dwReward (uint[2]):[1016, 16]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1016, 48]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004017
wLevel (uint):17
wAccumulatedExp (uint):17000
dwNeedStamps (uint):50
dwReward (uint[2]):[1017, 17]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1017, 51]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004018
wLevel (uint):18
wAccumulatedExp (uint):18000
dwNeedStamps (uint):50
dwReward (uint[2]):[0, 0]
dwCurrency (uint[2]):[1001, 1]
dwPayReward (uint[2]):[0, 0]
dwPayCurrency (uint[2]):[1002, 10]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004019
wLevel (uint):19
wAccumulatedExp (uint):19000
dwNeedStamps (uint):50
dwReward (uint[2]):[1019, 19]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1019, 57]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004020
wLevel (uint):20
wAccumulatedExp (uint):20000
dwNeedStamps (uint):50
dwReward (uint[2]):[0, 0]
dwCurrency (uint[2]):[1001, 1]
dwPayReward (uint[2]):[0, 0]
dwPayCurrency (uint[2]):[1002, 10]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004021
wLevel (uint):21
wAccumulatedExp (uint):21000
dwNeedStamps (uint):50
dwReward (uint[2]):[1021, 21]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1021, 63]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004022
wLevel (uint):22
wAccumulatedExp (uint):22000
dwNeedStamps (uint):50
dwReward (uint[2]):[0, 0]
dwCurrency (uint[2]):[1001, 1]
dwPayReward (uint[2]):[0, 0]
dwPayCurrency (uint[2]):[1002, 10]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004023
wLevel (uint):23
wAccumulatedExp (uint):23000
dwNeedStamps (uint):50
dwReward (uint[2]):[1023, 23]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1023, 69]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004024
wLevel (uint):24
wAccumulatedExp (uint):24000
dwNeedStamps (uint):50
dwReward (uint[2]):[1024, 24]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1024, 72]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004025
wLevel (uint):25
wAccumulatedExp (uint):25000
dwNeedStamps (uint):50
dwReward (uint[2]):[1025, 25]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1025, 75]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004026
wLevel (uint):26
wAccumulatedExp (uint):26000
dwNeedStamps (uint):50
dwReward (uint[2]):[0, 0]
dwCurrency (uint[2]):[1001, 1]
dwPayReward (uint[2]):[0, 0]
dwPayCurrency (uint[2]):[1002, 10]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004027
wLevel (uint):27
wAccumulatedExp (uint):27000
dwNeedStamps (uint):50
dwReward (uint[2]):[1027, 27]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[1027, 81]
dwPayCurrency (uint[2]):[0, 0]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

dwId (uint):2004028
wLevel (uint):28
wAccumulatedExp (uint):28000
dwNeedStamps (uint):50
dwReward (uint[2]):[1028, 28]
dwCurrency (uint[2]):[0, 0]
dwPayReward (uint[2]):[0, 0]
dwPayCurrency (uint[2]):[1002, 100]
dwTest (uint[2][2]):[[0, 0], [0, 0]]

