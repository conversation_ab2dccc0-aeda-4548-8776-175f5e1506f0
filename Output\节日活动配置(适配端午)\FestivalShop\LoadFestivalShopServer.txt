BOOL CGameData::LoadFestivalShopServerCfg()
{
    std::string DataPath = "data/FestivalShopServer.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        ::g_pClientToLog->PrintFileDirct("CDataFile::LoadFestivalShopCfg fopen error");
        return FALSE;
    }
    
    m_mapFestivalShopCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stFestivalShopServerCfg cfg;
        fread(&cfg, sizeof(stFestivalShopServerCfg), 1, fp);

        m_mapFestivalShopCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}