# 策划/运营专用：配置导表工具使用指南

## 什么是配置导表工具？

配置导表工具是将策划编写的Excel表格自动转换为游戏可直接使用的数据文件的工具。简单来说，你在Excel中填写的游戏配置数据，通过这个工具可以直接转换成游戏能读取的格式。

## 基本使用步骤

1. **启动工具**：双击`配置导表工具.exe`运行程序
2. **选择配置文件夹**：默认为当前目录下的`config`文件夹，可通过"浏览..."按钮更改
3. **查看Excel文件**：工具会自动显示配置文件夹中的所有Excel配置表
4. **导出数据**：
   - 选中需要导出的Excel文件，点击"导出选中"
   - 或点击"导出全部"一次性处理所有文件
5. **查看结果**：导出过程和结果会在日志区域显示

## 功能界面说明

- **主界面**：分为Excel文件列表、导出结构和导出SData三个标签页
- **Excel文件列表**：显示可用的Excel配置表
- **导出结构**：显示生成的结构体定义文件
- **导出SData**：显示生成的二进制数据文件
- **日志区域**：显示处理过程和结果信息

## Excel表格格式规范

每个Excel配置表必须按照以下格式设置：

| 行号 | 内容         | 说明                                   |
|------|--------------|----------------------------------------|
| 第1行 | 字段说明     | 对该字段的中文说明，方便策划理解      |
| 第2行 | 字段类型     | 如uint、int、char[32]等（见下方类型表）|
| 第3行 | 字段名       | 程序使用的变量名，必须按命名规则填写  |
| 第4行 | 生成类型     | server(仅服务端)、client(仅客户端)、all(两端都生成) |
| 第5行+ | 数据内容     | 从第5行开始是实际的游戏数据          |

## 详细数据类型说明

### 基本数据类型

| 配置中使用的类型 | 对应C++类型 | 数据范围 | 适用场景 | 命名前缀 | 示例 |
|----------------|------------|---------|---------|---------|------|
| uint           | DWORD      | 0 ~ 4,294,967,295 | 正整数/ID等 | dw | dwId |
| byte           | BYTE       | 0 ~ 255 | 小范围非负整数 | by | byLevel |
| ushort         | WORD       | 0 ~ 65,535 | 中等范围非负整数 | w | wCount |
| ufloat         | FLOAT      | 单精度浮点数 | 非负小数 | f | fSpeed |
| ulong          | DWORD      | 0 ~ 4,294,967,295 | 大范围非负整数 | dw | dwUserId |
| ulonglong      | ULONGLONG  | 0 ~ 18,446,744,073,709,551,615 | 超大范围非负整数 | l | lUniqueId |
| int            | INT        | -2,147,483,648 ~ 2,147,483,647 | 可为负数的整数 | i | iValue |
| double         | double     | 双精度浮点数 | 高精度小数 | d | dRatio |
| bool           | bool       | true/false | 是/否标记 | b | bIsOpen |
| char           | char       | 单个字符 | 字符标识 | c | cType |
| short          | short      | -32,768 ~ 32,767 | 小范围可负整数 | s | sOffset |
| float          | float      | 单精度浮点数 | 浮点数/小数 | f | fHeight |

### 数组类型

| 配置中使用的类型 | 对应C++类型 | 说明 | 命名前缀 | 示例 |
|----------------|------------|------|---------|------|
| uint[n]        | DWORD[n]   | n个正整数的数组 | dw | dwRewardIds[5] |
| int[n]         | INT[n]     | n个整数的数组 | i | iValues[3] |
| float[n]       | float[n]   | n个浮点数的数组 | f | fWeights[4] |
| char[n]        | char[n]    | 字符串，最大长度为n-1 | sz | szName[32] |
| bool[n]        | bool[n]    | n个布尔值的数组 | b | bFlags[10] |

> **重要说明**：数组长度n必须是具体数字，不能是变量。例如uint[10]表示10个uint类型的数组。

### 二维数组类型

支持二维数组，格式为`类型[行数][列数]`，例如：

| 配置中使用的类型 | 对应C++类型 | 说明 | 命名前缀 | 示例 |
|----------------|------------|------|---------|------|
| uint[m][n]     | DWORD[m][n] | m行n列的uint数组 | dw | dwMatrix[3][4] |
| char[m][n]     | char[m][n] | m个最大长度为n-1的字符串 | sz | szNames[5][32] |

## 特殊标记和功能

### 字段名忽略功能（~前缀）

在字段名前添加`~`符号，该字段将被导表工具**忽略**，不会包含在最终生成的数据中。

**使用场景**：
- 临时注释某个字段
- 添加仅供策划参考的辅助列
- 用于计算的中间数据列

**示例**：
```
第1行: 物品ID | 物品名称 | 策划备注
第2行: uint   | char[32] | char[64]
第3行: dwId   | szName   | ~szNote
第4行: all    | all      | all
```
在这个例子中，"策划备注"列将不会被导出到最终数据中。

### 唯一性检查功能（*前缀）

在第2行的字段类型前添加`*`符号，导表工具将对该字段进行**唯一性检查**，确保该列中没有重复值。

**使用场景**：
- 确保ID字段唯一
- 防止名称字段重复
- 验证关键标识符的唯一性

**示例**：
```
第1行: 物品ID | 物品名称
第2行: *uint  | char[32]
第3行: dwId   | szName
第4行: all    | all
```
在这个例子中，导表工具会检查"物品ID"列中的值是否有重复，如有重复将报错并停止导出。

### 索引列标记（#前缀）

在第3行的字段名前添加`#`符号，该字段将被标记为**索引列**，用于快速查找数据。

**使用场景**：
- 主键字段
- 频繁查询的字段

**示例**：
```
第1行: 物品ID | 物品名称
第2行: uint   | char[32]
第3行: #dwId  | szName
第4行: all    | all
```

### 多重唯一性检查（字段组合）

可以对多个字段组合进行唯一性检查，在多个字段类型前都添加`*`符号。

**示例**：
```
第1行: 任务ID | 阶段ID | 描述
第2行: *uint  | *uint  | char[64]
第3行: dwTaskId | dwPhaseId | szDesc
第4行: all    | all    | all
```
在这个例子中，系统会检查每个(任务ID, 阶段ID)组合是否唯一。

## 常见操作指南

### 如何创建新的配置表？

1. 创建新的Excel文件(.xlsx格式)
2. 设置前4行为字段说明、类型、名称和生成目标
3. 从第5行开始填入实际数据
4. 保存Excel文件到配置文件夹中
5. 使用导表工具导出

### 如何修改已有配置？

1. 打开对应的Excel文件
2. 修改数据内容（注意不要改变前4行的表头结构）
3. 保存文件
4. 使用导表工具重新导出

### 常见字段命名示例

| 数据内容 | 推荐类型 | 推荐命名 |
|---------|---------|---------|
| 物品ID   | uint    | dwItemId |
| 物品名称 | char[32] | szName |
| 数量     | int     | iCount |
| 概率(万分比) | uint | dwRate |
| 坐标X    | float   | fPosX |
| 怪物ID列表 | uint[10] | dwMonsterIds |
| 是否激活 | bool    | bActive |
| 名称列表 | char[5][32] | szNameList |

## 数据填写规范

| 数据类型 | 有效值示例 | 无效值示例 | 说明 |
|---------|-----------|-----------|------|
| uint/byte/ushort | 123, 0, 1000 | -1, 1.5, "123" | 只能是非负整数 |
| int/short | -10, 0, 100 | 1.5, "100" | 可以是负数或正数的整数 |
| float/double | 1.5, 0.01, -2.5 | "1.5" | 可以是小数或整数 |
| bool | true, false, 0, 1 | "yes", 2 | 只能是true/false或0/1 |
| char[n] | "物品名称" | 超过长度限制的文本 | 字符串长度不能超过n-1 |
| uint[n] | [1,2,3] | [1.5, -1, "2"] | 数组内每个元素都必须是有效的uint值 |

## 注意事项

1. **不要修改前4行的结构**：前4行是表格的定义部分，修改可能导致导出失败
2. **保存文件格式**：必须保存为.xlsx格式
3. **字段名唯一**：同一个表中的字段名不能重复
4. **数据类型匹配**：确保填写的数据与定义的类型一致
5. **字符串长度限制**：char[n]类型的字符串长度不能超过定义的n值
6. **导出查看**："导出结构"和"导出SData"页可以查看生成的文件
7. **特殊字符**：字符串中避免使用`"`和`\`等特殊字符，必要时请使用`\"`和`\\`进行转义

## 快速排查问题

| 问题 | 可能原因 | 解决方法 |
|------|---------|---------|
| 导出失败 | Excel格式不正确 | 检查前4行格式是否符合要求 |
| 数据类型错误 | 填写的值与类型不匹配 | 修改数据或调整类型定义 |
| 没有生成文件 | 可能被跳过(未修改) | 勾选"强制生成"重新导出 |
| 数组越界 | 填写的数组元素超过定义长度 | 减少数组元素或增加定义长度 |
| 唯一性检查失败 | 标记为唯一的字段有重复值 | 检查并修改重复的数据 |
| 字符串过长 | 字符串超过了定义的长度 | 缩短字符串或增加char[n]中的n值 |

## 生成文件说明

程序会按照以下目录结构生成文件：

```
配置文件夹/
  └── Excel文件名/
      └── Sheet名/
          ├── GameData{Sheet名}.txt      # C++结构体定义文件
          ├── Server/                    # 服务端文件夹
          │   ├── {Sheet名}Server.SData  # 服务端二进制数据文件
          │   └── {Sheet名}Server.txt    # 服务端数据可视化文件
          └── Client/                    # 客户端文件夹
              ├── {Sheet名}Client.SData  # 客户端二进制数据文件
              └── {Sheet名}Client.txt    # 客户端数据可视化文件
```

## 实用技巧

1. **拖放导入**：可以直接拖拽Excel文件到工具窗口进行导入
2. **快捷键**：Ctrl+S导出选中，Ctrl+Shift+S导出全部
3. **增量生成**：默认只会处理有修改的文件，提高效率
4. **强制生成**：勾选"强制生成"可忽略时间戳，重新处理所有文件
5. **表格备份**：建议定期备份重要的配置表，避免意外数据丢失
6. **数据验证**：可以在Excel中设置数据验证规则，防止输入错误的值

如有任何使用问题，请联系技术二组-寇晓晨。 