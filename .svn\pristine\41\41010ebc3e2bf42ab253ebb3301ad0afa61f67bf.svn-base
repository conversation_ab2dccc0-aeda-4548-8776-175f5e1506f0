BOOL CGameData::LoadSheet1ServerCfg()
{
    std::string DataPath = "data/Sheet1Server.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        ::g_pClientToLog->PrintFileDirct("CDataFile::LoadSheet1Cfg fopen error");
        return FALSE;
    }
    
    m_mapSheet1Cfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stSheet1ServerCfg cfg;
        fread(&cfg, sizeof(stSheet1ServerCfg), 1, fp);

        m_mapSheet1Cfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}