BOOL CGameData::LoadDragonBoatEventClientCfg()
{
    std::string DataPath = "data/DragonBoatEventClient.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        GSLogError(":LoadDragonBoatEventCfg Failed!  [%s]", DataPath.c_str());
        return FALSE;
    }
    
    m_mapDragonBoatEventCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stDragonBoatEventClientCfg cfg;
        fread(&cfg, sizeof(stDragonBoatEventClientCfg), 1, fp);

        m_mapDragonBoatEventCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}