BOOL CGameData::LoadFestivalActControllerServerCfg()
{
    std::string DataPath = "data/FestivalActControllerServer.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        ::g_pClientToLog->PrintFileDirct("CDataFile::LoadFestivalActControllerCfg fopen error");
        return FALSE;
    }
    
    m_mapFestivalActControllerCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stFestivalActControllerServerCfg cfg;
        fread(&cfg, sizeof(stFestivalActControllerServerCfg), 1, fp);

        m_mapFestivalActControllerCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}