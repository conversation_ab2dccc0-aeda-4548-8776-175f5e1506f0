#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import QApplication, QStyleFactory, QToolTip
from PyQt5.QtGui import QFont
from PyQt5.QtCore import QTimer
import os

class StyleManager:
    """
    样式管理类，负责管理应用的主题和样式
    将样式相关的代码从MainWindow类中提取出来，实现样式的集中管理
    """
    
    # 浅色主题配色方案
    LIGHT_THEME = {
        "background": "#F8F9FA",
        "main_text": "#212529",
        "secondary_text": "#6C757D",
        "accent": "#4285F4",
        "accent_hover": "#5294FF",
        "accent_pressed": "#3367D6",
        "success": "#34A853",
        "warning": "#FBBC05",
        "error": "#EA4335",
        "border": "#DEE2E6",
        "selected_bg": "#E8F0FE",
        "widget_bg": "#FFFFFF",
        "disabled_bg": "#F0F0F0",
        "disabled_text": "#999999",
        "status_bar_bg": "#F0F0F0",
        "status_bar_text": "#333333",
        "status_bar_border": "#DDDDDD",
        "log_bg": "#f8f8f8",
        "log_border": "#ddd",
        "tab_bg": "#EEEEEE",
        "tab_selected_bg": "#4285F4",
        "tab_selected_text": "#FFFFFF",
        "menu_item_selected_bg": "#E5E5E5",
        "menu_item_selected_text": "#333333",
        "progress_bg": "#FFFFFF",
        "splitter_handle": "#DDDDDD",
        "alternate_row": "#F8F8F8",
        "header_bg": "#F0F0F0",
        "header_text": "#333333",
        "header_border": "#DDDDDD",
        "error_status_bg": "#FFEBEB",
        "error_status_text": "#CC0000",
        "error_status_border": "#FFCCCC",
        "warning_status_bg": "#FFF9E6",
        "warning_status_text": "#AA6600",
        "warning_status_border": "#FFE8AA",
        "success_status_bg": "#E6F4FF",
        "success_status_text": "#0066CC",
        "success_status_border": "#BEDEFF",
        "struct_content_bg": "#E8F4FF",
        "struct_content_border": "#BEDEFF",
        "struct_content_text": "#333333"
    }
    
    # 深色主题配色方案
    DARK_THEME = {
        "background": "#202124",
        "main_text": "#E8EAED",
        "secondary_text": "#9AA0A6",
        "accent": "#4285F4",
        "accent_hover": "#5294FF",
        "accent_pressed": "#3367D6",
        "success": "#34A853",
        "warning": "#FBBC05",
        "error": "#EA4335",
        "border": "#3C4043",
        "selected_bg": "#1A73E8",
        "widget_bg": "#2D2D30",
        "disabled_bg": "#333333",
        "disabled_text": "#656565",
        "status_bar_bg": "#252526",
        "status_bar_text": "#E6E6E6",
        "status_bar_border": "#3F3F46",
        "log_bg": "#1E1E1E",
        "log_border": "#3F3F46",
        "tab_bg": "#2D2D30",
        "tab_selected_bg": "#007ACC",
        "tab_selected_text": "#FFFFFF",
        "menu_item_selected_bg": "#3F3F46",
        "menu_item_selected_text": "#FFFFFF",
        "progress_bg": "#1E1E1E",
        "splitter_handle": "#3F3F46",
        "alternate_row": "#2A2A2A",
        "header_bg": "#3F3F46",
        "header_text": "#E6E6E6",
        "header_border": "#555555",
        "error_status_bg": "#4A0F0F",
        "error_status_text": "#FFCCCC",
        "error_status_border": "#5A2828",
        "warning_status_bg": "#3A3000",
        "warning_status_text": "#FFDD99",
        "warning_status_border": "#5A4D10",
        "success_status_bg": "#0A3D5A",
        "success_status_text": "#99DDFF",
        "success_status_border": "#1A5D8A",
        "struct_content_bg": "#1E3246",
        "struct_content_border": "#3A6A9F",
        "struct_content_text": "#E6E6E6"
    }
    
    def __init__(self):
        self.current_theme = "light"  # 默认使用浅色主题
        self.colors = self.LIGHT_THEME.copy()  # 默认使用浅色主题的颜色
    
    def setup_style(self, main_window):
        """设置应用的样式和字体"""
        # 设置全局字体
        app_font = QFont("Microsoft YaHei UI", 9)
        QApplication.setFont(app_font)
        
        # 设置工具提示样式
        QToolTip.setFont(QFont('Microsoft YaHei UI', 9))
        
        # 应用当前主题
        self.apply_theme(main_window, self.current_theme)
    
    def apply_theme(self, main_window, theme_name):
        """应用主题样式"""
        # 保存旧主题用于比较
        old_theme = self.current_theme
        self.current_theme = theme_name
        
        # 更新颜色方案
        self.colors = self.DARK_THEME.copy() if theme_name == "dark" else self.LIGHT_THEME.copy()
        
        # 重置状态栏样式（包括计数标签）
        if hasattr(main_window, 'statusBar') and hasattr(main_window, 'excel_count_label'):
            self.reset_statusbar_style(main_window)
        else:
            # 如果状态栏或计数标签尚未初始化，记录一条调试信息
            print("状态栏或计数标签尚未初始化，跳过样式更新")
        
        # 获取图标资源路径
        branch_closed_dark = self.get_resource_path(main_window, "resources/icons/branch-closed-dark.png")
        branch_open_dark = self.get_resource_path(main_window, "resources/icons/branch-open-dark.png")
        branch_closed = self.get_resource_path(main_window, "resources/icons/branch-closed.png")
        branch_open = self.get_resource_path(main_window, "resources/icons/branch-open.png")
        
        if theme_name == "dark":
            # 深色主题
            main_window.setStyleSheet(f"""
                QMainWindow, QDialog {{
                    background-color: {self.colors['widget_bg']};
                    color: {self.colors['main_text']};
                }}
                QWidget {{
                    background-color: {self.colors['widget_bg']};
                    color: {self.colors['main_text']};
                }}
                QFrame {{
                    background-color: {self.colors['background']};
                    border: 1px solid {self.colors['border']};
                    border-radius: 3px;
                }}
                QTabWidget::pane {{
                    border: 1px solid {self.colors['border']};
                    background-color: {self.colors['widget_bg']};
                    border-radius: 5px;
                    border-top-left-radius: 0px;
                    margin-top: -1px;
                }}
                QTabBar::tab {{
                    background-color: {self.colors['tab_bg']};
                    color: {self.colors['main_text']};
                    padding: 8px 16px;
                    border: 1px solid {self.colors['border']};
                    border-bottom: none;
                    border-top-left-radius: 5px;
                    border-top-right-radius: 5px;
                    margin-right: 4px;
                    font-weight: normal;
                }}
                QTabBar::tab:hover {{
                    background-color: {self.colors['tab_hover_bg'] if 'tab_hover_bg' in self.colors else self.colors['tab_bg']};
                    color: {self.colors['accent']};
                    padding-top: 6px;
                    border-top: 2px solid {self.colors['accent']};
                }}
                QTabBar::tab:selected {{
                    background-color: {self.colors['tab_selected_bg']};
                    color: {self.colors['tab_selected_text']};
                }}
                QTabBar::tab:!selected {{
                    margin-top: 3px;
                }}
                QLineEdit, QTextEdit {{
                    background-color: {self.colors['log_bg']};
                    color: {self.colors['main_text']};
                    border: 1px solid {self.colors['border']};
                    border-radius: 3px;
                    padding: 2px;
                    font-family: 'Consolas', 'Courier New', monospace;
                }}
                
                QListWidget, QTreeWidget {{
                    background-color: {self.colors['log_bg']};
                    color: {self.colors['main_text']};
                    border: 1px solid {self.colors['border']};
                    border-radius: 3px;
                    padding: 4px;
                    font-family: 'Microsoft YaHei UI', sans-serif;
                }}
                
                QListWidget::item, QTreeWidget::item {{
                    padding: 6px 8px;
                    border-bottom: 1px solid {self.colors['border_light'] if 'border_light' in self.colors else self.colors['border']};
                    margin: 2px 0px;
                }}
                
                QListWidget::item:hover, QTreeWidget::item:hover {{
                    background-color: {self.colors['item_hover_bg'] if 'item_hover_bg' in self.colors else self.colors['tab_bg']};
                }}
                
                QListWidget::item:selected, QTreeWidget::item:selected {{
                    background-color: {self.colors['selected_bg']};
                    color: {self.colors['selected_text'] if 'selected_text' in self.colors else 'white'};
                    border-left: 3px solid {self.colors['accent']};
                }}
                QLineEdit:read-only {{
                    background-color: {self.colors['disabled_bg']};
                    color: {self.colors['secondary_text']};
                }}
                QLineEdit:disabled {{
                    background-color: {self.colors['disabled_bg']};
                    color: {self.colors['disabled_text']};
                }}
                QPushButton {{
                    background-color: {self.colors['accent']};
                    color: white;
                    border: none;
                    padding: 6px 12px;
                    border-radius: 3px;
                }}
                QPushButton:hover {{
                    background-color: {self.colors['accent_hover']};
                }}
                QPushButton:pressed {{
                    background-color: {self.colors['accent_pressed']};
                }}
                QPushButton:disabled {{
                    background-color: {self.colors['disabled_bg']};
                    color: {self.colors['disabled_text']};
                }}
                QCheckBox {{
                    color: {self.colors['main_text']};
                }}
                QCheckBox::indicator {{
                    width: 14px;
                    height: 14px;
                    background-color: {self.colors['log_bg']};
                    border: 1px solid {self.colors['border']};
                    border-radius: 2px;
                }}
                QCheckBox::indicator:checked {{
                    background-color: {self.colors['accent']};
                    border: 1px solid {self.colors['accent']};
                }}
                QMenuBar {{
                    background-color: {self.colors['background']};
                    color: {self.colors['main_text']};
                }}
                QMenuBar::item {{
                    background-color: transparent;
                    padding: 4px 10px;
                }}
                QMenuBar::item:selected {{
                    background-color: {self.colors['border']};
                    color: {self.colors['main_text']};
                }}
                QMenu {{
                    background-color: {self.colors['log_bg']};
                    color: {self.colors['main_text']};
                    border: 1px solid {self.colors['border']};
                }}
                QMenu::item {{
                    padding: 6px 25px 6px 20px;
                }}
                QMenu::item:selected {{
                    background-color: {self.colors['menu_item_selected_bg']};
                    color: {self.colors['menu_item_selected_text']};
                }}
                QProgressBar {{
                    border: 1px solid {self.colors['border']};
                    background-color: {self.colors['progress_bg']};
                    color: {self.colors['main_text']};
                    border-radius: 3px;
                    text-align: center;
                }}
                QProgressBar::chunk {{
                    background-color: {self.colors['accent']};
                    border-radius: 2px;
                }}
                QSplitter::handle {{
                    background-color: {self.colors['splitter_handle']};
                }}
                QSplitter::handle:horizontal {{
                    width: 2px;
                }}
                QSplitter::handle:vertical {{
                    height: 2px;
                }}
                QListWidget::item:alternate {{
                    background-color: {self.colors['alternate_row']};
                }}
                QListWidget::item:selected {{
                    background-color: {self.colors['selected_bg']};
                    color: white;
                }}
                QTreeView::branch:has-children:!has-siblings:closed,
                QTreeView::branch:closed:has-children:has-siblings {{
                    border-image: none;
                    image: url({branch_closed_dark});
                }}
                QTreeView::branch:open:has-children:!has-siblings,
                QTreeView::branch:open:has-children:has-siblings {{
                    border-image: none;
                    image: url({branch_open_dark});
                }}
                QHeaderView::section {{
                    background-color: {self.colors['header_bg']};
                    color: {self.colors['header_text']};
                    padding: 4px;
                    border: 1px solid {self.colors['header_border']};
                }}
            """)
        else:  # light theme (default)
            # 浅色主题
            main_window.setStyleSheet(f"""
                QMainWindow, QDialog {{
                    background-color: {self.colors['background']};
                    color: {self.colors['main_text']};
                }}
                QWidget {{
                    background-color: {self.colors['background']};
                    color: {self.colors['main_text']};
                }}
                QFrame {{
                    background-color: {self.colors['widget_bg']};
                    border: 1px solid {self.colors['border']};
                    border-radius: 3px;
                }}
                QTabWidget::pane {{
                    border: 1px solid {self.colors['border']};
                    background-color: {self.colors['widget_bg']};
                    border-radius: 5px;
                    border-top-left-radius: 0px;
                    margin-top: -1px;
                }}
                QTabWidget {{
                    padding: 2px;
                }}
                QTabBar::tab {{
                    background-color: {self.colors['tab_bg']};
                    color: {self.colors['main_text']};
                    padding: 8px 16px;
                    border: 1px solid {self.colors['border']};
                    border-bottom: none;
                    border-top-left-radius: 5px;
                    border-top-right-radius: 5px;
                    margin-right: 4px;
                    font-weight: normal;
                }}
                QTabBar::tab:hover {{
                    background-color: {self.colors['tab_hover_bg'] if 'tab_hover_bg' in self.colors else self.colors['tab_bg']};
                    color: {self.colors['accent']};
                    padding-top: 6px;
                    border-top: 2px solid {self.colors['accent']};
                }}
                QTabBar::tab:selected {{
                    background-color: {self.colors['tab_selected_bg']};
                    color: {self.colors['tab_selected_text']};
                    font-weight: bold;
                    border-bottom: none;
                    border-top: 2px solid {self.colors['accent']};
                    padding-top: 10px;
                }}
                QTabBar::tab:!selected {{
                    margin-top: 3px;
                }}
                QTabBar::scroller {{
                    width: 20px;
                }}
                QTabBar QToolButton {{
                    background-color: {self.colors['widget_bg']};
                    border: 1px solid {self.colors['border']};
                    border-radius: 3px;
                }}
                QLineEdit, QTextEdit {{
                    background-color: {self.colors['widget_bg']};
                    color: {self.colors['main_text']};
                    border: 1px solid {self.colors['border']};
                    border-radius: 3px;
                    padding: 2px;
                    font-family: 'Consolas', 'Courier New', monospace;
                }}
                
                QListWidget, QTreeWidget {{
                    background-color: {self.colors['widget_bg']};
                    color: {self.colors['main_text']};
                    border: 1px solid {self.colors['border']};
                    border-radius: 4px;
                    padding: 2px;
                    font-family: 'Consolas', 'Courier New', monospace;
                }}
                
                QScrollBar:vertical {{
                    border: none;
                    background: {self.colors['widget_bg']};
                    width: 10px;
                    margin: 0px;
                }}
                
                QScrollBar::handle:vertical {{
                    background: {self.colors['border']};
                    min-height: 20px;
                    border-radius: 5px;
                }}
                
                QScrollBar::handle:vertical:hover {{
                    background: {self.colors['accent']};
                }}
                
                QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                    height: 0px;
                }}
                
                QScrollBar:horizontal {{
                    border: none;
                    background: {self.colors['widget_bg']};
                    height: 10px;
                    margin: 0px;
                }}
                
                QScrollBar::handle:horizontal {{
                    background: {self.colors['border']};
                    min-width: 20px;
                    border-radius: 5px;
                }}
                
                QScrollBar::handle:horizontal:hover {{
                    background: {self.colors['accent']};
                }}
                
                QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
                    width: 0px;
                }}
                QLineEdit:read-only {{
                    background-color: {self.colors['disabled_bg']};
                }}
                QLineEdit:disabled {{
                    background-color: {self.colors['disabled_bg']};
                    color: {self.colors['disabled_text']};
                }}
                QPushButton {{
                    background-color: {self.colors['accent']};
                    color: white;
                    border: none;
                    padding: 6px 12px;
                    border-radius: 3px;
                }}
                QPushButton:hover {{
                    background-color: {self.colors['accent_hover']};
                }}
                QPushButton:pressed {{
                    background-color: {self.colors['accent_pressed']};
                }}
                QPushButton:disabled {{
                    background-color: {self.colors['disabled_bg']};
                    color: {self.colors['disabled_text']};
                }}
                QCheckBox {{
                    color: {self.colors['main_text']};
                }}
                QCheckBox::indicator {{
                    width: 14px;
                    height: 14px;
                    background-color: {self.colors['widget_bg']};
                    border: 1px solid {self.colors['border']};
                    border-radius: 2px;
                }}
                QCheckBox::indicator:checked {{
                    background-color: {self.colors['accent']};
                    border: 1px solid {self.colors['accent']};
                }}
                QMenuBar {{
                    background-color: {self.colors['widget_bg']};
                    color: {self.colors['main_text']};
                }}
                QMenuBar::item {{
                    background-color: transparent;
                    padding: 4px 10px;
                }}
                QMenuBar::item:selected {{
                    background-color: {self.colors['menu_item_selected_bg']};
                    color: {self.colors['main_text']};
                }}
                QMenu {{
                    background-color: {self.colors['widget_bg']};
                    color: {self.colors['main_text']};
                    border: 1px solid {self.colors['border']};
                }}
                QMenu::item {{
                    padding: 6px 25px 6px 20px;
                }}
                QMenu::item:selected {{
                    background-color: {self.colors['menu_item_selected_bg']};
                    color: {self.colors['menu_item_selected_text']};
                }}
                QProgressBar {{
                    border: 1px solid {self.colors['border']};
                    background-color: {self.colors['progress_bg']};
                    color: {self.colors['main_text']};
                    border-radius: 3px;
                    text-align: center;
                }}
                QProgressBar::chunk {{
                    background-color: {self.colors['accent']};
                    border-radius: 2px;
                }}
                QSplitter::handle {{
                    background-color: {self.colors['splitter_handle']};
                }}
                QSplitter::handle:horizontal {{
                    width: 2px;
                }}
                QSplitter::handle:vertical {{
                    height: 2px;
                }}
                QListWidget::item {{
                    padding: 4px 8px;
                    border-bottom: 1px solid {self.colors['border']};
                }}
                QListWidget::item:alternate {{
                    background-color: {self.colors['alternate_row']};
                }}
                QListWidget::item:hover {{
                    background-color: {self.colors['widget_bg']};
                    border-left: 2px solid {self.colors['accent']};
                }}
                QListWidget::item:selected {{
                    background-color: {self.colors['selected_bg']};
                    color: {self.colors['main_text']};
                    border-left: 3px solid {self.colors['accent']};
                }}
                QListWidget, QTreeWidget {{
                    show-decoration-selected: 1;
                    outline: none;
                }}
                QTreeView::branch:has-children:!has-siblings:closed,
                QTreeView::branch:closed:has-children:has-siblings {{
                    border-image: none;
                    image: url({branch_closed});
                    padding-left: 2px;
                }}
                QTreeView::branch:open:has-children:!has-siblings,
                QTreeView::branch:open:has-children:has-siblings {{
                    border-image: none;
                    image: url({branch_open});
                    padding-left: 2px;
                }}
                QTreeView::item {{
                    padding: 4px 0px;
                    border-bottom: 1px solid {self.colors['border']};
                }}
                QTreeView::item:hover {{
                    background-color: {self.colors['widget_bg']};
                    border-left: 2px solid {self.colors['accent']};
                }}
                QTreeView::item:selected {{
                    background-color: {self.colors['selected_bg']};
                    color: {self.colors['main_text']};
                    border-left: 3px solid {self.colors['accent']};
                }}
                QHeaderView::section {{
                    background-color: {self.colors['header_bg']};
                    color: {self.colors['header_text']};
                    padding: 6px;
                    border: 1px solid {self.colors['header_border']};
                    font-weight: bold;
                }}
            """)
        
        # 在设置完全局样式后，再应用特定控件的样式
        if hasattr(main_window, 'log_text') and hasattr(main_window, 'path_edit') and hasattr(main_window, 'output_path_edit'):
            self.update_theme_specific_styles(main_window)
        
        # 如果是主题切换（不是初始加载），则重新格式化日志内容
        if hasattr(main_window, 'log_text') and old_theme != theme_name:
            # 直接重新生成日志内容（简单粗暴但有效的方法）
            # 保存日志原始内容
            plain_text = main_window.log_text.toPlainText()
            
            # 清空日志并重新显示
            main_window.log_text.clear()
            
            # 按行分割并重新应用样式
            for line in plain_text.split('\n'):
                if line.strip():  # 跳过空行
                    main_window.log_output(line)
        
        # 通知用户主题已更改
        if hasattr(main_window, 'statusBar'):  # 确保statusBar已初始化
            main_window.statusBar().showMessage(f"已切换到{'深色' if theme_name == 'dark' else '浅色'}主题", 3000)
    
    def update_theme_specific_styles(self, main_window):
        """根据当前主题更新特定控件的样式"""
        if self.current_theme == "dark":
            # 深色主题样式
            # 设置日志文本框样式
            main_window.log_text.setStyleSheet(f"""
                QTextEdit {{
                    background-color: {self.colors['log_bg']};
                    color: {self.colors['main_text']};
                    border: 1px solid {self.colors['border']};
                    font-family: 'Consolas', 'Courier New', monospace;
                }}
            """)
            
            # 设置路径文本框样式 - 强调使用深色背景和浅色文本
            main_window.path_edit.setStyleSheet(f"background-color: {self.colors['disabled_bg']}; color: {self.colors['secondary_text']};")
            main_window.output_path_edit.setStyleSheet(f"background-color: {self.colors['disabled_bg']}; color: {self.colors['secondary_text']};")
            
            # 确保文件列表和其他控件也使用深色主题样式
            list_widget_dark_style = f"""
                QListWidget {{
                    background-color: {self.colors['log_bg']};
                    color: {self.colors['main_text']};
                    border: 1px solid {self.colors['border']};
                }}
                QListWidget::item:selected {{
                    background-color: {self.colors['selected_bg']};
                    color: white;
                }}
            """
            main_window.file_list.setStyleSheet(list_widget_dark_style)
            main_window.server_struct_list.setStyleSheet(list_widget_dark_style)
            main_window.client_struct_list.setStyleSheet(list_widget_dark_style)
            main_window.server_load_function_list.setStyleSheet(list_widget_dark_style)
            main_window.client_load_function_list.setStyleSheet(list_widget_dark_style)
            main_window.server_sdata_list.setStyleSheet(list_widget_dark_style)
            main_window.client_sdata_list.setStyleSheet(list_widget_dark_style)
            
            # 设置状态栏样式 - 深色主题
            main_window.statusBar().setStyleSheet(f"""
                QStatusBar {{
                    background-color: {self.colors['status_bar_bg']};
                    color: {self.colors['status_bar_text']};
                    border-top: 1px solid {self.colors['status_bar_border']};
                    padding: 3px 6px;
                    font-weight: normal;
                }}
                QStatusBar::item {{
                    border: none;
                    border-radius: 3px;
                }}
                QStatusBar QLabel {{
                    padding: 2px 6px;
                }}
            """)
        else:
            # 浅色主题样式
            # 设置日志文本框样式
            main_window.log_text.setStyleSheet(f"""
                QTextEdit {{
                    background-color: {self.colors['log_bg']};
                    border: 1px solid {self.colors['log_border']};
                    font-family: 'Consolas', 'Courier New', monospace;
                }}
            """)
            
            # 设置路径文本框样式
            main_window.path_edit.setStyleSheet(f"background-color: {self.colors['disabled_bg']};")
            main_window.output_path_edit.setStyleSheet(f"background-color: {self.colors['disabled_bg']};")
            
            # 恢复文件列表和其他控件的浅色主题样式
            list_widget_light_style = f"""
                QListWidget::item:selected {{
                    background-color: {self.colors['selected_bg']};
                    color: {self.colors['main_text']};
                }}
            """
            main_window.file_list.setStyleSheet(list_widget_light_style)
            main_window.server_struct_list.setStyleSheet(list_widget_light_style)
            main_window.client_struct_list.setStyleSheet(list_widget_light_style)
            main_window.server_load_function_list.setStyleSheet(list_widget_light_style)
            main_window.client_load_function_list.setStyleSheet(list_widget_light_style)
            main_window.server_sdata_list.setStyleSheet(list_widget_light_style)
            main_window.client_sdata_list.setStyleSheet(list_widget_light_style)
            
            # 设置状态栏样式 - 浅色主题
            main_window.statusBar().setStyleSheet(f"""
                QStatusBar {{
                    background-color: {self.colors['status_bar_bg']};
                    color: {self.colors['status_bar_text']};
                    border-top: 1px solid {self.colors['status_bar_border']};
                    padding: 3px 6px;
                    font-weight: normal;
                }}
                QStatusBar::item {{
                    border: none;
                    border-radius: 3px;
                }}
                QStatusBar QLabel {{
                    padding: 2px 6px;
                }}
            """)
        
        # 强制触发UI更新
        main_window.repaint()
    
    def reset_statusbar_style(self, main_window):
        """重置状态栏样式为当前主题的默认样式，同时保留计数标签的样式"""
        # 保存计数标签的引用，以便在重置后恢复它们的样式
        excel_label = main_window.excel_count_label
        struct_label = main_window.struct_count_label
        sdata_label = main_window.sdata_count_label
        
        # 设置状态栏基本样式
        if self.current_theme == "dark":
            main_window.statusBar().setStyleSheet(f"""
                QStatusBar {{
                    background-color: {self.colors['status_bar_bg']};
                    color: {self.colors['status_bar_text']};
                    border-top: 1px solid {self.colors['status_bar_border']};
                }}
            """)
            
            # 为暗色主题更新计数标签样式
            excel_label.setStyleSheet("margin-right: 15px; padding: 2px 8px; background-color: #3a3a3a; border-radius: 4px; font-weight: bold; color: #ffffff;")
            struct_label.setStyleSheet("margin-right: 15px; padding: 2px 8px; background-color: #003a70; border-radius: 4px; font-weight: bold; color: #ffffff;")
            sdata_label.setStyleSheet("margin-right: 15px; padding: 2px 8px; background-color: #1d3712; border-radius: 4px; font-weight: bold; color: #ffffff;")
        else:
            main_window.statusBar().setStyleSheet(f"""
                QStatusBar {{
                    background-color: {self.colors['status_bar_bg']};
                    color: {self.colors['status_bar_text']};
                    border-top: 1px solid {self.colors['status_bar_border']};
                }}
            """)
            
            # 为亮色主题更新计数标签样式
            excel_label.setStyleSheet("margin-right: 15px; padding: 2px 8px; background-color: #f0f0f0; border-radius: 4px; font-weight: bold;")
            struct_label.setStyleSheet("margin-right: 15px; padding: 2px 8px; background-color: #e6f7ff; border-radius: 4px; font-weight: bold;")
            sdata_label.setStyleSheet("margin-right: 15px; padding: 2px 8px; background-color: #f6ffed; border-radius: 4px; font-weight: bold;")
        
        main_window.repaint()
    
    def set_error_status_style(self, main_window):
        """设置错误状态栏样式，保留计数标签的样式"""
        error_status_bar = main_window.statusBar()
        
        # 保存计数标签的引用
        excel_label = main_window.excel_count_label
        struct_label = main_window.struct_count_label
        sdata_label = main_window.sdata_count_label
        
        # 保存当前计数标签的样式
        excel_style = excel_label.styleSheet()
        struct_style = struct_label.styleSheet()
        sdata_style = sdata_label.styleSheet()
        
        # 根据主题设置不同的错误样式
        if self.current_theme == "dark":
            error_status_bar.setStyleSheet(f"""
                QStatusBar {{
                    background-color: {self.colors['error_status_bg']};
                    color: {self.colors['error_status_text']};
                    border-top: 1px solid {self.colors['error_status_border']};
                    padding: 3px 6px;
                    font-weight: bold;
                }}
                QStatusBar::item {{
                    border: none;
                    border-radius: 3px;
                }}
                QStatusBar QLabel {{
                    padding: 2px 6px;
                }}
            """)
        else:
            error_status_bar.setStyleSheet(f"""
                QStatusBar {{
                    background-color: {self.colors['error_status_bg']};
                    color: {self.colors['error_status_text']};
                    border-top: 1px solid {self.colors['error_status_border']};
                    padding: 3px 6px;
                    font-weight: bold;
                }}
                QStatusBar::item {{
                    border: none;
                    border-radius: 3px;
                }}
                QStatusBar QLabel {{
                    padding: 2px 6px;
                }}
            """)
        
        # 恢复计数标签的样式
        excel_label.setStyleSheet(excel_style)
        struct_label.setStyleSheet(struct_style)
        sdata_label.setStyleSheet(sdata_style)
        
        error_status_bar.showMessage("发生错误！", 3000)
        
        # 使用线程安全的方式恢复样式
        QTimer.singleShot(3000, lambda: self.reset_statusbar_style(main_window))
    
    def set_warning_status_style(self, main_window):
        """设置警告状态栏样式，保留计数标签的样式"""
        warning_status_bar = main_window.statusBar()
        
        # 保存计数标签的引用
        excel_label = main_window.excel_count_label
        struct_label = main_window.struct_count_label
        sdata_label = main_window.sdata_count_label
        
        # 保存当前计数标签的样式
        excel_style = excel_label.styleSheet()
        struct_style = struct_label.styleSheet()
        sdata_style = sdata_label.styleSheet()
        
        # 根据主题设置不同的警告样式
        if self.current_theme == "dark":
            warning_status_bar.setStyleSheet(f"""
                QStatusBar {{
                    background-color: {self.colors['warning_status_bg']};
                    color: {self.colors['warning_status_text']};
                    border-top: 1px solid {self.colors['warning_status_border']};
                    padding: 3px 6px;
                    font-weight: bold;
                }}
                QStatusBar::item {{
                    border: none;
                    border-radius: 3px;
                }}
                QStatusBar QLabel {{
                    padding: 2px 6px;
                }}
            """)
        else:
            warning_status_bar.setStyleSheet(f"""
                QStatusBar {{
                    background-color: {self.colors['warning_status_bg']};
                    color: {self.colors['warning_status_text']};
                    border-top: 1px solid {self.colors['warning_status_border']};
                    padding: 3px 6px;
                    font-weight: bold;
                }}
                QStatusBar::item {{
                    border: none;
                    border-radius: 3px;
                }}
                QStatusBar QLabel {{
                    padding: 2px 6px;
                }}
            """)
        
        # 恢复计数标签的样式
        excel_label.setStyleSheet(excel_style)
        struct_label.setStyleSheet(struct_style)
        sdata_label.setStyleSheet(sdata_style)
        
        warning_status_bar.showMessage("警告信息", 3000)
        
        # 使用统一的方法恢复样式
        QTimer.singleShot(3000, lambda: self.reset_statusbar_style(main_window))
    
    def set_success_status_style(self, main_window, has_failures=False, only_skipped=False):
        """设置成功状态栏样式，保留计数标签的样式"""
        success_status_bar = main_window.statusBar()
        
        # 保存计数标签的引用
        excel_label = main_window.excel_count_label
        struct_label = main_window.struct_count_label
        sdata_label = main_window.sdata_count_label
        
        # 保存当前计数标签的样式
        excel_style = excel_label.styleSheet()
        struct_style = struct_label.styleSheet()
        sdata_style = sdata_label.styleSheet()
        
        if has_failures:
            # 有失败，使用错误样式
            if self.current_theme == "dark":
                success_status_bar.setStyleSheet(f"""
                    QStatusBar {{
                        background-color: {self.colors['error_status_bg']};
                        color: {self.colors['error_status_text']};
                        border-top: 1px solid {self.colors['error_status_border']};
                    }}
                """)
            else:
                success_status_bar.setStyleSheet(f"""
                    QStatusBar {{
                        background-color: {self.colors['error_status_bg']};
                        color: {self.colors['error_status_text']};
                        border-top: 1px solid {self.colors['error_status_border']};
                    }}
                """)
            
            success_status_bar.showMessage("导出完成，但有失败项", 3000)
        elif only_skipped:
            # 只有跳过的文件，使用默认状态栏样式
            success_status_bar.showMessage("导出完成，未发现修改", 3000)
        else:
            # 成功导出，使用成功样式
            if self.current_theme == "dark":
                success_status_bar.setStyleSheet(f"""
                    QStatusBar {{
                        background-color: {self.colors['success_status_bg']};
                        color: {self.colors['success_status_text']};
                        border-top: 1px solid {self.colors['success_status_border']};
                        padding: 3px 6px;
                        font-weight: bold;
                    }}
                    QStatusBar::item {{
                        border: none;
                        border-radius: 3px;
                    }}
                    QStatusBar QLabel {{
                        padding: 2px 6px;
                    }}
                """)
            else:
                success_status_bar.setStyleSheet(f"""
                    QStatusBar {{
                        background-color: {self.colors['success_status_bg']};
                        color: {self.colors['success_status_text']};
                        border-top: 1px solid {self.colors['success_status_border']};
                        padding: 3px 6px;
                        font-weight: bold;
                    }}
                    QStatusBar::item {{
                        border: none;
                        border-radius: 3px;
                    }}
                    QStatusBar QLabel {{
                        padding: 2px 6px;
                    }}
                """)
            
            # 恢复计数标签的样式
            excel_label.setStyleSheet(excel_style)
            struct_label.setStyleSheet(struct_style)
            sdata_label.setStyleSheet(sdata_style)
            
            success_status_bar.showMessage("导出成功完成！", 3000)
        
        # 使用统一的方法恢复样式
        QTimer.singleShot(3000, lambda: self.reset_statusbar_style(main_window))
    
    def get_log_message_style(self, message):
        """根据消息内容和当前主题返回适当的样式"""
        # if "开始处理" in message:
        #     return f'<span style="color: {self.colors["success"]}; font-weight: bold;">{message}</span>'
        # el
        if "页签" in message and "处理成功" in message:
            # 页签处理成功，使用绿色不加粗显示
            return f'<span style="color: {self.colors["success"]};">{message}</span>'
        elif "跳过未修改的文件" in message:
            return f'<span style="color: {self.colors["secondary_text"]};">{message}</span>'
        elif "错误" in message or "[错误]" in message:
            return f'<span style="color: {self.colors["error"]};">{message}</span>'
        elif "警告" in message or "[警告]" in message:
            return f'<span style="color: {self.colors["warning"]};">{message}</span>'
        elif "导出完成" in message:
            if "失败: 0" not in message:
                # 有失败，使用红色显示
                return f'<span style="color: {self.colors["error"]}; font-weight: bold;">{message}</span>'
            elif "成功: 0" in message and "跳过: " in message and "失败: 0" in message:
                # 只有跳过的文件，使用灰色显示
                return f'<span style="color: {self.colors["secondary_text"]};">{message}</span>'
            else:
                # 成功导出，使用蓝色显示
                return f'<span style="color: {self.colors["accent"]}; font-weight: bold;">{message}</span>'
        elif "找到" in message and ("个结构体文件" in message or "个SData文件" in message or "个TXT数据文件" in message or "个Excel配置文件" in message):
            # 文件扫描结果，使用蓝色显示
            return f'<span style="color: {self.colors["accent"]};">{message}</span>'
        else:
            # 默认颜色 - 确保在深色模式下可见
            if self.current_theme == "dark":
                # 深色主题使用浅色文字
                return f'<span style="color: {self.colors["main_text"]};">{message}</span>'
            else:
                # 浅色主题使用深色文字
                return f'<span style="color: {self.colors["main_text"]};">{message}</span>'
    
    def get_struct_content_style(self, content_type="struct"):
        """获取内容显示的样式
        
        Args:
            content_type: 内容类型，可选值为 "struct"（结构体内容）、"load_function"（加载函数文件内容）或 "sdata"（SData文件解析结果）
        """
        if content_type == "struct":
            return {
                "bg_color": self.colors["struct_content_bg"],
                "border_color": self.colors["struct_content_border"],
                "text_color": self.colors["struct_content_text"]
            }
        elif content_type == "load_function":
            if self.current_theme == "dark":
                return {
                    "bg_color": "#1C1C1C",
                    "border_color": "#3E3E3E",
                    "text_color": "#D4D4D4"
                }
            else:
                return {
                    "bg_color": "#FCFCFC",
                    "border_color": "#E0E0E0",
                    "text_color": "#333333"
                }
        elif content_type == "sdata":
            if self.current_theme == "dark":
                return {
                    "bg_color": "#1E3440",  # 深蓝色背景（深色模式）
                    "border_color": "#4C566A",
                    "text_color": "#E5E9F0"  # 浅色文字
                }
            else:
                return {
                    "bg_color": "#E5EDF5",  # 浅蓝色背景（浅色模式）
                    "border_color": "#88C0D0",
                    "text_color": "#2E3440"  # 深色文字
                }
        else:
            # 默认返回结构体内容样式
            return {
                "bg_color": self.colors["struct_content_bg"],
                "border_color": self.colors["struct_content_border"],
                "text_color": self.colors["struct_content_text"]
            }
    
    def get_resource_path(self, main_window, relative_path):
        """获取资源文件的绝对路径，兼容打包后的路径"""
        if hasattr(main_window, 'base_dir'):
            return os.path.join(main_window.base_dir, relative_path)
        else:
            # 如果没有base_dir属性，则使用当前目录
            return os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', relative_path)
            
    def reset_log_text_colors(self, main_window):
        """重置日志文本颜色以匹配当前主题"""
        if hasattr(main_window, 'log_text'):
            # 获取日志的纯文本内容
            plain_text = main_window.log_text.toPlainText()
            
            # 清空日志
            main_window.log_text.clear()
            
            # 按行分割并重新应用样式
            for line in plain_text.split('\n'):
                if line.strip():  # 跳过空行
                    main_window.log_output(line)
            
            # 通知用户
            main_window.statusBar().showMessage("已重置日志文本颜色", 2000)
