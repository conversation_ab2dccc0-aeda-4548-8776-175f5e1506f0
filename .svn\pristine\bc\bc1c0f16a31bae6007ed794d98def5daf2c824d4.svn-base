数据条数: 12
dwEventId (uint):10001
byEventType (byte):1
szEventDesc (char[128]):"遭遇礁石后退1步"
iStep (int):-1
dwReward (int[4][2]):[[0, 0], [0, 0], [0, 0], [0, 0]]
dwCurrency (int[2]):[0, 0]
szIcon (char[64]):

dwEventId (uint):10002
byEventType (byte):1
szEventDesc (char[128]):"遭遇礁石后退2步"
iStep (int):-2
dwReward (int[4][2]):[[0, 0], [0, 0], [0, 0], [0, 0]]
dwCurrency (int[2]):[0, 0]
szIcon (char[64]):

dwEventId (uint):10003
byEventType (byte):1
szEventDesc (char[128]):"遭遇礁石后退3步"
iStep (int):-3
dwReward (int[4][2]):[[0, 0], [0, 0], [0, 0], [0, 0]]
dwCurrency (int[2]):[0, 0]
szIcon (char[64]):

dwEventId (uint):20001
byEventType (byte):2
szEventDesc (char[128]):"顺风顺水前进1步"
iStep (int):1
dwReward (int[4][2]):[[0, 0], [0, 0], [0, 0], [0, 0]]
dwCurrency (int[2]):[0, 0]
szIcon (char[64]):

dwEventId (uint):20002
byEventType (byte):2
szEventDesc (char[128]):"顺风顺水前进2步"
iStep (int):2
dwReward (int[4][2]):[[0, 0], [0, 0], [0, 0], [0, 0]]
dwCurrency (int[2]):[0, 0]
szIcon (char[64]):

dwEventId (uint):20003
byEventType (byte):2
szEventDesc (char[128]):"顺风顺水前进3步"
iStep (int):3
dwReward (int[4][2]):[[0, 0], [0, 0], [0, 0], [0, 0]]
dwCurrency (int[2]):[0, 0]
szIcon (char[64]):

dwEventId (uint):30001
byEventType (byte):3
szEventDesc (char[128]):"发现宝箱获得"
iStep (int):0
dwReward (int[4][2]):[[201, 1], [0, 0], [0, 0], [0, 0]]
dwCurrency (int[2]):[0, 0]
szIcon (char[64]):

dwEventId (uint):30002
byEventType (byte):3
szEventDesc (char[128]):"路遇海盗成功击退，获得骰子*1"
iStep (int):0
dwReward (int[4][2]):[[101, 1], [0, 0], [0, 0], [0, 0]]
dwCurrency (int[2]):[0, 0]
szIcon (char[64]):

dwEventId (uint):30003
byEventType (byte):3
szEventDesc (char[128]):"路遇海盗成功击退，获得骰子*1"
iStep (int):0
dwReward (int[4][2]):[[101, 1], [0, 0], [0, 0], [0, 0]]
dwCurrency (int[2]):[0, 0]
szIcon (char[64]):

dwEventId (uint):40001
byEventType (byte):3
szEventDesc (char[128]):"发现宝箱获得"
iStep (int):0
dwReward (int[4][2]):[[0, 0], [0, 0], [0, 0], [0, 0]]
dwCurrency (int[2]):[1001, 100]
szIcon (char[64]):

dwEventId (uint):40002
byEventType (byte):3
szEventDesc (char[128]):"发现宝箱获得"
iStep (int):0
dwReward (int[4][2]):[[0, 0], [0, 0], [0, 0], [0, 0]]
dwCurrency (int[2]):[1001, 50]
szIcon (char[64]):

dwEventId (uint):40003
byEventType (byte):3
szEventDesc (char[128]):"发现宝箱获得"
iStep (int):0
dwReward (int[4][2]):[[0, 0], [0, 0], [0, 0], [0, 0]]
dwCurrency (int[2]):[1001, 30]
szIcon (char[64]):

