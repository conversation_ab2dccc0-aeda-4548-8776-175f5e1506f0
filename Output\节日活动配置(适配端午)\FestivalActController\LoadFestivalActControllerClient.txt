BOOL CGameData::LoadFestivalActControllerClientCfg()
{
    std::string DataPath = "data/FestivalActControllerClient.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        GSLogError(":LoadFestivalActControllerCfg Failed!  [%s]", DataPath.c_str());
        return FALSE;
    }
    
    m_mapFestivalActControllerCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stFestivalActControllerClientCfg cfg;
        fread(&cfg, sizeof(stFestivalActControllerClientCfg), 1, fp);

        m_mapFestivalActControllerCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}