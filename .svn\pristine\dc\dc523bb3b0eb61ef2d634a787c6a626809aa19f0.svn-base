#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import logging
from datetime import datetime

class Logger:
    def __init__(self):
        self.output_callback = None
        self.setup_logger()
    
    def setup_logger(self):
        self.logger = logging.getLogger("ConfigExporter")
        self.logger.setLevel(logging.DEBUG)
        
        # 创建logs目录
        self.logs_dir = os.path.join(os.getcwd(), "logs")
        os.makedirs(self.logs_dir, exist_ok=True)
        
        # 创建文件处理器
        self.current_log_filename = f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        log_file = os.path.join(self.logs_dir, self.current_log_filename)
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        
        # 创建格式器
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def set_output_callback(self, callback):
        self.output_callback = callback
    
    def _log(self, level, message):
        if self.output_callback:
            prefix = {
                logging.INFO: "[信息]",
                logging.WARNING: "[警告]",
                logging.ERROR: "[错误]",
                logging.DEBUG: "[调试]"
            }.get(level, "[信息]")
            
            self.output_callback(f"{prefix} {message}")
        
        if level == logging.DEBUG:
            self.logger.debug(message)
        elif level == logging.INFO:
            self.logger.info(message)
        elif level == logging.WARNING:
            self.logger.warning(message)
        elif level == logging.ERROR:
            self.logger.error(message)
    
    def debug(self, message):
        self._log(logging.DEBUG, message)
    
    def info(self, message):
        self._log(logging.INFO, message)
    
    def warning(self, message):
        self._log(logging.WARNING, message)
    
    def error(self, message):
        self._log(logging.ERROR, message)
        
    def get_log_files(self):
        """获取所有日志文件列表"""
        if not hasattr(self, 'logs_dir') or not os.path.exists(self.logs_dir):
            return []
        return [f for f in os.listdir(self.logs_dir) if f.endswith(".log")]
        
    def get_current_log_filename(self):
        """获取当前正在使用的日志文件名"""
        if hasattr(self, 'current_log_filename'):
            return self.current_log_filename
        return None