数据条数: 18
dwRankId (uint):1005001
dwActivityId (uint):1005
dwRankMin (uint):1
dwRankMax (uint):1
dwPersonalRewards (int[4][2]):[[301, 10], [401, 1], [0, 0], [0, 0]]
dwPerSonalCurrencyReward (int[2]):[1001, 1000]

dwRankId (uint):1005002
dwActivityId (uint):1005
dwRankMin (uint):2
dwRankMax (uint):2
dwPersonalRewards (int[4][2]):[[301, 10], [401, 1], [0, 0], [0, 0]]
dwPerSonalCurrencyReward (int[2]):[1001, 800]

dwRankId (uint):1005003
dwActivityId (uint):1005
dwRankMin (uint):3
dwRankMax (uint):3
dwPersonalRewards (int[4][2]):[[301, 10], [401, 1], [0, 0], [0, 0]]
dwPerSonalCurrencyReward (int[2]):[1001, 500]

dwRankId (uint):1005004
dwActivityId (uint):1005
dwRankMin (uint):4
dwRankMax (uint):10
dwPersonalRewards (int[4][2]):[[301, 10], [401, 1], [0, 0], [0, 0]]
dwPerSonalCurrencyReward (int[2]):[1001, 300]

dwRankId (uint):1005005
dwActivityId (uint):1005
dwRankMin (uint):11
dwRankMax (uint):20
dwPersonalRewards (int[4][2]):[[301, 10], [401, 1], [0, 0], [0, 0]]
dwPerSonalCurrencyReward (int[2]):[1001, 200]

dwRankId (uint):1005006
dwActivityId (uint):1005
dwRankMin (uint):21
dwRankMax (uint):50
dwPersonalRewards (int[4][2]):[[301, 10], [401, 1], [0, 0], [0, 0]]
dwPerSonalCurrencyReward (int[2]):[1001, 100]

dwRankId (uint):1005007
dwActivityId (uint):1005
dwRankMin (uint):51
dwRankMax (uint):100
dwPersonalRewards (int[4][2]):[[301, 10], [401, 1], [0, 0], [0, 0]]
dwPerSonalCurrencyReward (int[2]):[1001, 80]

dwRankId (uint):1005008
dwActivityId (uint):1005
dwRankMin (uint):101
dwRankMax (uint):200
dwPersonalRewards (int[4][2]):[[301, 10], [401, 1], [0, 0], [0, 0]]
dwPerSonalCurrencyReward (int[2]):[1001, 50]

dwRankId (uint):1005009
dwActivityId (uint):1005
dwRankMin (uint):201
dwRankMax (uint):9999
dwPersonalRewards (int[4][2]):[[301, 10], [401, 1], [0, 0], [0, 0]]
dwPerSonalCurrencyReward (int[2]):[1001, 10]

dwRankId (uint):2005001
dwActivityId (uint):2005
dwRankMin (uint):1
dwRankMax (uint):1
dwPersonalRewards (int[4][2]):[[301, 10], [401, 1], [0, 0], [0, 0]]
dwPerSonalCurrencyReward (int[2]):[1002, 1000]

dwRankId (uint):2005002
dwActivityId (uint):2005
dwRankMin (uint):2
dwRankMax (uint):2
dwPersonalRewards (int[4][2]):[[301, 10], [401, 1], [0, 0], [0, 0]]
dwPerSonalCurrencyReward (int[2]):[1002, 800]

dwRankId (uint):2005003
dwActivityId (uint):2005
dwRankMin (uint):3
dwRankMax (uint):3
dwPersonalRewards (int[4][2]):[[301, 10], [401, 1], [0, 0], [0, 0]]
dwPerSonalCurrencyReward (int[2]):[1002, 500]

dwRankId (uint):2005004
dwActivityId (uint):2005
dwRankMin (uint):4
dwRankMax (uint):10
dwPersonalRewards (int[4][2]):[[301, 10], [401, 1], [0, 0], [0, 0]]
dwPerSonalCurrencyReward (int[2]):[1002, 300]

dwRankId (uint):2005005
dwActivityId (uint):2005
dwRankMin (uint):11
dwRankMax (uint):20
dwPersonalRewards (int[4][2]):[[301, 10], [401, 1], [0, 0], [0, 0]]
dwPerSonalCurrencyReward (int[2]):[1002, 200]

dwRankId (uint):2005006
dwActivityId (uint):2005
dwRankMin (uint):21
dwRankMax (uint):50
dwPersonalRewards (int[4][2]):[[301, 10], [401, 1], [0, 0], [0, 0]]
dwPerSonalCurrencyReward (int[2]):[1002, 100]

dwRankId (uint):2005007
dwActivityId (uint):2005
dwRankMin (uint):51
dwRankMax (uint):100
dwPersonalRewards (int[4][2]):[[301, 10], [401, 1], [0, 0], [0, 0]]
dwPerSonalCurrencyReward (int[2]):[1002, 80]

dwRankId (uint):2005008
dwActivityId (uint):2005
dwRankMin (uint):101
dwRankMax (uint):200
dwPersonalRewards (int[4][2]):[[301, 10], [401, 1], [0, 0], [0, 0]]
dwPerSonalCurrencyReward (int[2]):[1002, 50]

dwRankId (uint):2005009
dwActivityId (uint):2005
dwRankMin (uint):201
dwRankMax (uint):9999
dwPersonalRewards (int[4][2]):[[301, 10], [401, 1], [0, 0], [0, 0]]
dwPerSonalCurrencyReward (int[2]):[1002, 10]

