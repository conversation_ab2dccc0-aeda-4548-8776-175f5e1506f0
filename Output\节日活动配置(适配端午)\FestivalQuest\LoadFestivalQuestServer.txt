BOOL CGameData::LoadFestivalQuestServerCfg()
{
    std::string DataPath = "data/FestivalQuestServer.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        ::g_pClientToLog->PrintFileDirct("CDataFile::LoadFestivalQuestCfg fopen error");
        return FALSE;
    }
    
    m_mapFestivalQuestCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stFestivalQuestServerCfg cfg;
        fread(&cfg, sizeof(stFestivalQuestServerCfg), 1, fp);

        m_mapFestivalQuestCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}