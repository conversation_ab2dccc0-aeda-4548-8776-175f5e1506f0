#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
打包脚本，将Python程序打包为独立的Windows可执行文件
"""

import os
import sys
import shutil
import subprocess

def build_exe():
    print("开始打包配置导表工具...")
    
    # 清理旧的打包文件
    for dir_name in ["build", "dist"]:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
    
    # 使用PyInstaller打包
    cmd = [
        "pyinstaller",
        "--name=配置导表工具",
        "--windowed",  # GUI模式
        "--icon=resources/icon.ico",
        "--add-data=README.md;.",
        "main.py"
    ]
    
    # 执行打包命令
    try:
        subprocess.check_call(cmd)
        print("打包完成!")
        print(f"可执行文件路径: {os.path.abspath('dist/配置导表工具/配置导表工具.exe')}")
    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    # 创建resources目录
    if not os.path.exists("resources"):
        os.makedirs("resources")
    
    # 创建icon.ico示例文件（如果不存在）
    icon_path = "resources/icon.ico"
    if not os.path.exists(icon_path):
        print("警告: 图标文件不存在，请手动添加icon.ico文件到resources目录")
    
    if build_exe():
        print("打包成功！")
    else:
        print("打包失败，请检查错误信息。") 