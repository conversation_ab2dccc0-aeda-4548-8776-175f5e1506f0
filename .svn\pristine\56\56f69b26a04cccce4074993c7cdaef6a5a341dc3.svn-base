#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import openpyxl
import struct
import threading
import json
import re
import ast
from concurrent.futures import ThreadPoolExecutor
from utils.logger import Logger
from core.type_converter import TypeConverter
from datetime import datetime
import time

class ExcelProcessor:
    def __init__(self):
        self.logger = Logger()
        self.type_converter = TypeConverter()
        self.timestamp_cache = {}
        self.load_timestamp_cache()
        
        self.cpp_load_function_template = """服务端生成说明：
BOOL Load${页签名}ServerCfg();
----------------------------------------
std::map<DWORD, ${页签名}Server> m_map${页签名}Cfg;
----------------------------------------
BOOL CGameData::Load${页签名}ServerCfg()
{
    std::string DataPath = "data/${页签名}Server.SData";
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        ::g_pClientToLog->PrintFileDirect("CDataFile::${函数名} fopen error");
        return FALSE;
    }
    
    m_map${页签名}Cfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        st${页签名}ServerCfg cfg;
        fread(&cfg, sizeof(st${页签名}ServerCfg), 1, fp);

        m_map${页签名}Cfg[cfg.dwId] = cfg;
    }

    fclose(fp);
    return TRUE;
}

客户端生成说明：
BOOL Load${页签名}ClientCfg();
----------------------------------------
std::map<DWORD, ${页签名}Client> m_map${页签名}Cfg;
----------------------------------------
BOOL CDataFile::Load${页签名}ClientCfg()
{
    std::string DataPath = "data/${页签名}Client.SData";
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        GSLogError(":${函数名} Failed!  [%s]", DataPath.c_str());
        return FALSE;
    }
    
    m_map${页签名}Cfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        st${页签名}ClientCfg cfg;
        fread(&cfg, sizeof(st${页签名}ClientCfg), 1, fp);

        m_map${页签名}Cfg[cfg.dwId] = cfg;
    }

    fclose(fp);
    return TRUE;
}
"""
    
    def parse_time_value(self, time_str):
        """解析时间字符串为时间戳
        
        Args:
            time_str: 时间字符串
            
        Returns:
            int: 时间戳（秒）
        """
        if time_str is None:
            return 0
            
        # 如果已经是数值类型，直接返回
        if isinstance(time_str, (int, float)):
            return int(time_str)
            
        # 确保是字符串类型
        if not isinstance(time_str, str):
            time_str = str(time_str)
            
        # 移除字符串前后可能存在的引号
        time_str = time_str.strip()
        if (time_str.startswith('"') and time_str.endswith('"')) or \
           (time_str.startswith("'") and time_str.endswith("'")):
            time_str = time_str[1:-1].strip()
            
        # 空字符串返回0
        if not time_str:
            return 0
            
        # 尝试直接转换为整数
        try:
            return int(float(time_str))
        except (ValueError, TypeError):
            pass
            
        # 尝试不同的日期时间格式
        formats = [
            '%Y-%m-%d %H:%M:%S',     # 2023-01-01 12:34:56
            '%Y/%m/%d %H:%M:%S',     # 2023/01/01 12:34:56
            '%Y-%m-%d %H:%M',        # 2023-01-01 12:34
            '%Y/%m/%d %H:%M',        # 2023/01/01 12:34
            '%Y-%m-%d',              # 2023-01-01
            '%Y/%m/%d',              # 2023/01/01
            '%d-%m-%Y %H:%M:%S',     # 01-01-2023 12:34:56
            '%d/%m/%Y %H:%M:%S',     # 01/01/2023 12:34:56
            '%Y%m%d%H%M%S',          # 20230101123456
            '%Y%m%d %H:%M',          # 20230101 12:34
            '%Y%m%d %H:%M:%S',       # 20230101 12:34:56
            '%Y%m%d'                 # 20230101
        ]
        
        for fmt in formats:
            try:
                dt = datetime.strptime(time_str, fmt)
                timestamp = int(time.mktime(dt.timetuple()))
                return timestamp
            except ValueError:
                continue
                
        # 所有格式均失败，返回0
        self.logger.error(f"无法解析时间字符串 '{time_str}'，将使用默认值0")
        return 0
    
    def load_timestamp_cache(self):
        """加载时间戳缓存"""
        cache_file = os.path.join(os.getcwd(), "timestamp_cache.json")
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    self.timestamp_cache = json.load(f)
            except Exception as e:
                self.logger.error(f"加载时间戳缓存失败: {str(e)}")
    
    def save_timestamp_cache(self):
        """保存时间戳缓存"""
        cache_file = os.path.join(os.getcwd(), "timestamp_cache.json")
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.timestamp_cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存时间戳缓存失败: {str(e)}")
    
    def process_file(self, file_path, force_gen=False, output_path=None, sdata_paths=None):
        """处理单个Excel文件

        Args:
            file_path: Excel文件路径
            force_gen: 是否强制生成
            output_path: 输出路径，如果为None则使用与配置文件相同的目录
            sdata_paths: SData文件的自定义导出路径字典

        Returns:
            bool: 处理是否成功
        """
        if not os.path.exists(file_path):
            self.logger.error(f"文件不存在: {file_path}")
            return False
        
        file_mtime = os.path.getmtime(file_path)
        
        # 检查时间戳，判断是否需要重新生成
        if not force_gen and file_path in self.timestamp_cache:
            if file_mtime <= self.timestamp_cache[file_path]:
                self.logger.info(f"跳过未修改的文件: {os.path.basename(file_path)}")
                return True
        
        try:
            # 获取Excel文件名（不含扩展名）
            excel_file_name = os.path.splitext(os.path.basename(file_path))[0]
            
            # 决定输出目录
            if output_path:
                # 使用指定的输出路径
                base_output_dir = output_path
            else:
                # 默认使用与配置文件相同的目录
                base_output_dir = os.path.dirname(file_path)
                
            # 创建以Excel文件名命名的目录
            excel_dir = os.path.join(base_output_dir, excel_file_name)
            os.makedirs(excel_dir, exist_ok=True)
            
            self.logger.info(f"开始处理: {os.path.basename(file_path)}")
            # self.logger.info(f"输出目录: {excel_dir}")
            
            # 打开Excel文件
            workbook = openpyxl.load_workbook(file_path, data_only=True)
            
            # 处理每个sheet
            with ThreadPoolExecutor(max_workers=min(os.cpu_count(), 4)) as executor:
                futures = []
                for sheet_name in workbook.sheetnames:
                    futures.append(executor.submit(self.process_sheet,
                                                  workbook[sheet_name],
                                                  excel_dir,
                                                  excel_file_name,
                                                  sheet_name,
                                                  sdata_paths))
                
                success = True
                for future in futures:
                    if not future.result():
                        success = False
            
            # 更新时间戳
            if success:
                self.timestamp_cache[file_path] = file_mtime
                self.save_timestamp_cache()
            
            workbook.close()
            return success
            
        except Exception as e:
            self.logger.error(f"处理文件失败: {str(e)}")
            return False
    
    def process_sheet(self, sheet, output_dir, excel_name, sheet_name, sdata_paths=None):
        """处理单个表格

        Args:
            sheet: openpyxl的worksheet对象
            output_dir: 基础输出目录（Excel文件名目录）
            excel_name: Excel文件名（不含扩展名）
            sheet_name: 表格名称
            sdata_paths: SData文件的自定义导出路径字典

        Returns:
            bool: 处理是否成功
        """
        try:
            # self.logger.info(f"处理页签: {sheet_name}")
            
            # 检查表格是否为空
            if sheet.max_row < 5:
                self.logger.error(f"表格 {sheet_name} 行数不足，跳过")
                return False
            
            # 读取表头
            field_comments = [cell.value for cell in sheet[1]]  # 字段说明
            field_types = [cell.value for cell in sheet[2]]     # 字段类型
            field_names = [cell.value for cell in sheet[3]]     # 字段名
            field_targets = [cell.value for cell in sheet[4]]   # 生成类型(server/client/all)
            
            # 过滤无效列
            valid_columns = []
            field_info = []
            
            for i, (comment, type_str, name, target) in enumerate(zip(field_comments, field_types, field_names, field_targets)):
                if not type_str or not name:
                    # 如果字段类型或字段名为空，发出警告并忽略该列
                    col_letter = openpyxl.utils.cell.get_column_letter(i + 1)

                    # 检查字段名是否为"~"，如果是则不显示警告
                    if name == "~":
                        # 静默忽略该列，不显示警告
                        pass
                    # elif col_letter != 'A':
                    #     if comment:
                    #         self.logger.info(f"页签 '{sheet_name}' 忽略 {col_letter} 列 ('{comment}')")
                    #     else:
                    #         self.logger.info(f"页签 '{sheet_name}' 忽略 {col_letter} 列")
                    # else:
                    elif comment:
                        self.logger.warning(f"页签 '{sheet_name}' 忽略 {col_letter} 列 ('{comment}')，可以将字段名设置为'~'跳过检查")
                    else:
                        self.logger.warning(f"页签 '{sheet_name}' 忽略 {col_letter} 列，可以将字段名设置为~跳过检查")
                    continue
                
                # 规范化字段名，确保大小写一致
                # 首先检查是否是dwreward这类特殊字段
                normalized_name = name
                if isinstance(name, str):
                    # 处理特定的字段名规范化
                    if name.lower().startswith('dwreward'):
                        # 确保dwReward字段开头大小写正确（dw开头，Reward首字母大写）
                        prefix = 'dwReward'
                        num_part = name[len('dwreward'):] if name.lower().startswith('dwreward') else ''
                        normalized_name = prefix + num_part
                    # 可以添加其他字段名规范化规则
                
                # 处理注释中可能的换行
                processed_comment = comment
                if isinstance(comment, str):
                    # 将注释中的换行替换为空格
                    processed_comment = re.sub(r'\s*\n\s*', ' ', comment)
                
                valid_columns.append(i)
                field_info.append({
                    'index': i,
                    'comment': processed_comment,
                    'type': type_str,
                    'name': normalized_name,  # 使用规范化后的字段名
                    'target': target,
                    'cpp_type': self.type_converter.get_cpp_type(type_str)
                })
            
            # 将字段信息分为服务端和客户端
            server_fields = [f for f in field_info if f['target'] == 'server' or f['target'] == 'all']
            client_fields = [f for f in field_info if f['target'] == 'client' or f['target'] == 'all']
            # 检查标记为"*"的唯一字段
            unique_fields = [f for f in field_info if f['target'] == '*']
            
            # 如果有标记为"*"的字段，确保它们也被添加到服务端和客户端字段中
            for field in unique_fields:
                field['target'] = 'all'  # 将target修改为all
                if field not in server_fields:
                    server_fields.append(field)
                if field not in client_fields:
                    client_fields.append(field)

            # 定义数据类型的取值范围
            type_ranges = {
                'byte': (0, 255),                    # 无符号8位整数
                'short': (-32768, 32767),            # 有符号16位整数
                'ushort': (0, 65535),                # 无符号16位整数
                'int': (-2147483648, 2147483647),    # 有符号32位整数
                'uint': (0, 4294967295),             # 无符号32位整数
                'ulong': (0, 4294967295),            # 无符号32位整数（与uint相同）
                'ulonglong': (0, 18446744073709551615),  # 无符号64位整数
                'bool': (0, 1),                      # 布尔值（0或1）
                'char': (0, 255),                    # 单个字符（ASCII码范围）
                'float': (-3.402823e+38, 3.402823e+38),  # 单精度浮点数近似值范围
                'ufloat': (0, 3.402823e+38),         # 无符号浮点数（实际上是处理为非负浮点数）
                'double': (-1.7976931348623157e+308, 1.7976931348623157e+308),  # 双精度浮点数近似值范围
                'time_t': (0, 4294967295)            # 时间戳（作为无符号32位整数处理）
            }
            
            # 读取数据
            data_rows = []
            for row_idx, row in enumerate(sheet.iter_rows(min_row=5, values_only=True), 5):
                if all(cell is None for cell in row):
                    continue
                    
                row_data = {}
                
                # 检查每个字段的数据是否合法
                for i, cell_value in enumerate(row):
                    if i in valid_columns:
                        field = field_info[valid_columns.index(i)]
                        field_name = field['name']
                        field_type = field['type']
                        
                        # 处理空值
                        if cell_value is None:
                            # 根据字段类型设置默认值，而不是直接使用None
                            # 检查是否是二维数组类型
                            two_dim_array_match = re.match(r'(\w+)\[(\d+)\]\[(\d+)\]', field_type)
                            if two_dim_array_match:
                                base_type = two_dim_array_match.group(1)
                                rows = int(two_dim_array_match.group(2))
                                cols = int(two_dim_array_match.group(3))
                                
                                # 根据基础类型创建合适的默认二维数组
                                if base_type in ('float', 'ufloat', 'double'):
                                    cell_value = [[0.0] * cols for _ in range(rows)]
                                elif base_type == 'char':
                                    # 字符串数组的默认值为空字符串数组，需要是二维的
                                    cell_value = [[""] * cols for _ in range(rows)]
                                else:
                                    cell_value = [[0] * cols for _ in range(rows)]
                            # 检查是否是一维数组类型
                            elif array_match := re.match(r'(\w+)\[(\d+)\]', field_type):
                                base_type = array_match.group(1)
                                size = int(array_match.group(2))
                                
                                # 根据基础类型创建合适的默认一维数组
                                if base_type in ('float', 'ufloat', 'double'):
                                    cell_value = [0.0] * size
                                elif base_type == 'char':
                                    # 字符数组默认为空字符串
                                    cell_value = ""
                                else:
                                    cell_value = [0] * size
                            # 检查是否是字符串类型
                            elif field_type.startswith('char['):
                                # 字符串默认为空字符串
                                cell_value = ""
                            # 简单类型处理
                            else:
                                # 根据基础类型设置默认值
                                if field_type in ('float', 'ufloat', 'double'):
                                    cell_value = 0.0
                                elif field_type == 'bool':
                                    cell_value = False
                                elif field_type == 'time_t':
                                    cell_value = 0  # 时间戳默认为0
                                else:
                                    cell_value = 0
                            
                            # 记录警告信息
                            # self.logger.warning(f"页签 '{sheet_name}' 记录 #{row_idx+1} 字段 '{field_name}' ({field_type}) 未配置值，将使用默认值 {cell_value}")
                            
                            row_data[field_name] = cell_value
                            continue
                        
                        # 检查二维数组是否越界
                        two_dim_array_match = re.match(r'(\w+)\[(\d+)\]\[(\d+)\]', field_type)
                        if two_dim_array_match and cell_value is not None:
                            base_type = two_dim_array_match.group(1)
                            rows = int(two_dim_array_match.group(2)) # Expected dimensions from type definition
                            cols = int(two_dim_array_match.group(3))
                            
                            # 严格解析和验证二维数组格式
                            # array_data 将在 cell_value 是有效的二维数组表示时被填充。
                            # 否则，将记录错误并返回 False。
                            if isinstance(cell_value, str):
                                stripped_value = cell_value.strip()
                                if not stripped_value: # 空字符串或仅包含空白的字符串
                                    self.logger.error(f"页签 '{sheet_name}' 记录 #{row_idx+1} 字段 '{field_name}' ({field_type}) 为空字符串，但要求二维数组格式。")
                                    return False

                                if stripped_value.startswith('[') and stripped_value.endswith(']'):
                                    try:
                                        parsed_value = ast.literal_eval(stripped_value)
                                        # 有效的二维数组是列表的列表。空列表 [] (表示零行) 也是可接受的。
                                        if isinstance(parsed_value, list) and \
                                           (not parsed_value or all(isinstance(item, list) for item in parsed_value)):
                                            array_data = parsed_value
                                        else:
                                            self.logger.error(f"页签 '{sheet_name}' 记录 #{row_idx+1} 字段 '{field_name}' ({field_type}) 要求二维数组格式 (例如 \'[[1,2],[3,4]]\')，但提供的值 '{cell_value}' 解析后结构不正确 (不是列表的列表)。")
                                            return False
                                    except (SyntaxError, ValueError) as e:
                                        self.logger.error(f"页签 '{sheet_name}' 记录 #{row_idx+1} 字段 '{field_name}' ({field_type}) 的二维数组字符串 '{cell_value}' 格式无效: {str(e)}")
                                        return False
                                else:
                                    self.logger.error(f"页签 '{sheet_name}' 记录 #{row_idx+1} 字段 '{field_name}' ({field_type}) 要求二维数组格式 (例如 \'[[1,2],[3,4]]\')，但提供了非数组格式的字符串 '{cell_value}'。")
                                    return False
                            elif isinstance(cell_value, list):
                                # 有效的二维数组是列表的列表。空列表 [] 也是可接受的。
                                if not cell_value or all(isinstance(item, list) for item in cell_value):
                                    array_data = cell_value
                                else:
                                    self.logger.error(f"页签 '{sheet_name}' 记录 #{row_idx+1} 字段 '{field_name}' ({field_type}) 要求二维数组格式，但提供了列表 '{cell_value}'，其元素不是列表。")
                                    return False
                            else:
                                # cell_value 不是字符串也不是列表 (例如 int, float)。
                                # None 值已由之前的默认值逻辑处理。
                                self.logger.error(f"页签 '{sheet_name}' 记录 #{row_idx+1} 字段 '{field_name}' ({field_type}) 要求二维数组格式，但提供了类型为 {type(cell_value).__name__} 的值 '{cell_value}'。")
                                return False
                            
                            # 检查行数是否越界
                            if len(array_data) > rows:
                                error_msg = f"二维数组行数越界错误: 页签 '{sheet_name}' 记录 #{row_idx+1} 行 '{field_name}' 字段定义为 {field_type}，" \
                                            f"但实际行数为 {len(array_data)}，超出了定义的行数 {rows}。" \
                                            f"数据: {cell_value}"
                                self.logger.error(error_msg)
                                return False
                            
                            # 检查列数是否越界
                            for row_idx_inner, row_item in enumerate(array_data):
                                if len(row_item) > cols:
                                    error_msg = f"二维数组列数越界错误: 页签 '{sheet_name}' 记录 #{row_idx+1} 行 '{field_name}' 字段定义为 {field_type}，" \
                                                f"但第 {row_idx_inner+1} 行的列数为 {len(row_item)}，超出了定义的列数 {cols}。" \
                                                f"数据: {cell_value}"
                                    self.logger.error(error_msg)
                                    return False
                            
                            # 检查二维数组元素的值是否在类型范围内
                            if base_type in type_ranges:
                                min_val, max_val = type_ranges[base_type]
                                for row_idx_inner, row_item in enumerate(array_data):
                                    for col_idx, item in enumerate(row_item):
                                        # 跳过None值
                                        if item is None:
                                            continue
                                        
                                        # 如果是 char 数组，只检查数组越界，不检查内容是否为有效数值
                                        if base_type == 'char':
                                            continue
                                        
                                        try:
                                            # 根据基本类型进行不同的检查
                                            if base_type == 'bool':
                                                # 布尔值特殊处理
                                                if isinstance(item, bool):
                                                    num_val = 1 if item else 0
                                                elif isinstance(item, (int, float)):
                                                    num_val = 1 if item != 0 else 0
                                                elif isinstance(item, str):
                                                    lower_val = item.lower().strip()
                                                    if lower_val in ('true', 't', 'yes', 'y', '1'):
                                                        num_val = 1
                                                    elif lower_val in ('false', 'f', 'no', 'n', '0', ''):
                                                        num_val = 0
                                                    else:
                                                        self.logger.error(f"页签 '{sheet_name}' 记录 #{row_idx+1} 行 '{field_name}' 字段的第 [{row_idx_inner+1}][{col_idx+1}] 个元素 '{item}' 不是有效的布尔值")
                                                        num_val = 0  # 默认为false
                                                else:
                                                    num_val = 0
                                            elif base_type in ('float', 'ufloat', 'double'):
                                                # 浮点数检查
                                                num_val = float(item)
                                                if base_type == 'ufloat' and num_val < 0:
                                                    error_msg = f"数值范围错误: 页签 '{sheet_name}' 记录 #{row_idx+1} 行 '{field_name}' 字段的第 [{row_idx_inner+1}][{col_idx+1}] 个元素值 {num_val} " \
                                                                f"不能为负数，因为字段类型是 {base_type}"
                                                    self.logger.error(error_msg)
                                                    return False
                                            else:
                                                # 整数类型
                                                num_val = int(float(item))  # 先转为float再转为int，支持小数点表示
                                            
                                            # 范围检查
                                            if num_val < min_val or num_val > max_val:
                                                error_msg = f"数值范围错误: 页签 '{sheet_name}' 记录 #{row_idx+1} 行 '{field_name}' 字段的第 [{row_idx_inner+1}][{col_idx+1}] 个元素值 {num_val} " \
                                                            f"超出了 {base_type} 类型的取值范围 [{min_val}, {max_val}]"
                                                self.logger.error(error_msg)
                                                return False
                                        except (ValueError, TypeError):
                                            # 对于char数组不进行数值验证
                                            if base_type != 'char':
                                                # 如果是空字符串，静默替换为0
                                                if isinstance(item, str) and not item.strip():
                                                    # 空字符串转为0，不输出警告
                                                    cell_value[col_idx] = 0
                                                else:
                                                    self.logger.error(f"页签 '{sheet_name}' 记录 #{row_idx+1} 行 '{field_name}' 字段的第 [{row_idx_inner+1}][{col_idx+1}] 个元素 '{item}' 不是有效的数值")
                        
                        # 检查一维数组是否越界
                        array_match = re.match(r'(\w+)\[(\d+)\]', field_type)
                        if array_match and cell_value is not None and not two_dim_array_match:
                            base_type = array_match.group(1)
                            size = int(array_match.group(2))
                            
                            # 解析数组值
                            array_data = []
                            if isinstance(cell_value, str) and cell_value.startswith('[') and cell_value.endswith(']'):
                                try:
                                    array_data = ast.literal_eval(cell_value)
                                    if not isinstance(array_data, list):
                                        array_data = [array_data]
                                except (SyntaxError, ValueError):
                                    # 解析失败，创建默认数组
                                    if base_type in ('float', 'ufloat', 'double'):
                                        array_data = [0.0] * size
                                    else:
                                        array_data = [0] * size
                                    value = array_data
                                    self.logger.error(f"表格 '{sheet_name}' 记录 #{row_idx+1} 字段 '{field_name}' ({field_type}) 解析一维数组失败，使用默认值数组")
                                    return False
                            elif isinstance(cell_value, list):
                                array_data = cell_value
                            else:
                                # 单个值转为数组
                                try:
                                    if cell_value is None:
                                        array_data = [0] * size
                                    else:
                                        array_data = [cell_value] if cell_value is not None else []
                                except Exception:
                                    array_data = [0] * size
                            
                            # 确保array_data始终有值
                            if not array_data:
                                array_data = [0] * size
                            
                            # 检查数组是否越界
                            if len(array_data) > size:
                                # 发现数组越界，记录错误并中止处理
                                error_msg = f"数组越界错误: 页签 '{sheet_name}' 记录 #{row_idx+1} 行 '{field_name}' 字段定义为 {field_type}，" \
                                            f"但实际数据长度为 {len(array_data)}，超出了定义的长度 {size}。" \
                                            f"数据: {cell_value}"
                                self.logger.error(error_msg)
                                # 返回处理失败
                                return False
                        
                            # 检查数组元素的值是否在类型范围内
                            if base_type in type_ranges:
                                min_val, max_val = type_ranges[base_type]
                                for idx, item in enumerate(array_data):
                                    # 跳过None值
                                    if item is None:
                                        continue
                                    
                                    # 如果是 char 数组，只检查数组越界，不检查内容是否为有效数值
                                    if base_type == 'char':
                                        continue
                                    
                                    try:
                                        # 根据基本类型进行不同的检查
                                        if base_type == 'bool':
                                            # 布尔值特殊处理
                                            if isinstance(item, bool):
                                                num_val = 1 if item else 0
                                            elif isinstance(item, (int, float)):
                                                num_val = 1 if item != 0 else 0
                                            elif isinstance(item, str):
                                                lower_val = item.lower().strip()
                                                if lower_val in ('true', 't', 'yes', 'y', '1'):
                                                    num_val = 1
                                                elif lower_val in ('false', 'f', 'no', 'n', '0', ''):
                                                    num_val = 0
                                                else:
                                                    self.logger.error(f"页签 '{sheet_name}' 记录 #{row_idx+1} 行 '{field_name}' 字段的第 {idx+1} 个元素 '{item}' 不是有效的布尔值")
                                                    num_val = 0  # 默认为false
                                            else:
                                                num_val = 0
                                        elif base_type in ('float', 'ufloat', 'double'):
                                            # 浮点数检查
                                            num_val = float(item)
                                            if base_type == 'ufloat' and num_val < 0:
                                                error_msg = f"数值范围错误: 页签 '{sheet_name}' 记录 #{row_idx+1} 行 '{field_name}' 字段的第 {idx+1} 个元素值 {num_val} " \
                                                            f"不能为负数，因为字段类型是 {base_type}"
                                                self.logger.error(error_msg)
                                                return False
                                        elif base_type == 'time_t':
                                            # 时间戳特殊处理，使用parse_time_value函数处理
                                            if isinstance(item, str) and not item.strip():
                                                # 空字符串视为0
                                                num_val = 0
                                                item = 0
                                            else:
                                                # 解析时间字符串
                                                timestamp = self.parse_time_value(item)
                                                item = timestamp  # 更新item的值
                                                num_val = timestamp
                                        elif isinstance(item, (int, float)):
                                            # 数值直接使用
                                            num_val = int(item)
                                        elif isinstance(item, str) and not item.strip():
                                            # 空字符串处理为0
                                            num_val = 0
                                            item = 0
                                            cell_value[idx] = 0  # 更新数组中的值
                                        else:
                                            # 其他情况，尝试转换为数值
                                            try:
                                                num_val = int(float(item))  # 先转为float再转为int，支持小数点表示
                                            except (ValueError, TypeError):
                                                self.logger.error(f"页签 '{sheet_name}' 记录 #{row_idx+1} 行 '{field_name}' 字段的第 {idx+1} 个元素 '{item}' 不是有效的数值，将使用默认值0")
                                                # 由于这是一个错误，应该返回处理失败
                                                return False
                                    
                                    # 范围检查
                                        if num_val < min_val or num_val > max_val:
                                            error_msg = f"数值范围错误: 页签 '{sheet_name}' 记录 #{row_idx+1} 行 '{field_name}' 字段的第 {idx+1} 个元素值 {num_val} " \
                                                        f"超出了 {base_type} 类型的取值范围 [{min_val}, {max_val}]"
                                            self.logger.error(error_msg)
                                            return False
                                    except (ValueError, TypeError):
                                        # 对于char数组不进行数值验证
                                        if base_type != 'char':
                                            # 如果是空字符串，静默替换为0
                                            if isinstance(item, str) and not item.strip():
                                                # 空字符串转为0，不输出警告
                                                cell_value[idx] = 0
                                            # else:
                                                # self.logger.warning(f"页签 '{sheet_name}' 记录 #{row_idx+1} 行 '{field_name}' 字段的第 {idx+1} 个元素 '{item}' 不是有效的数值")
                        
                        # 检查简单类型的值范围
                        elif field_type in type_ranges and cell_value is not None:
                            min_val, max_val = type_ranges[field_type]
                            try:
                                # 尝试将值转换为数值进行比较
                                if isinstance(cell_value, str) and not cell_value.strip():
                                    # 空字符串视为None，跳过检查
                                    pass
                                else:
                                    # 根据类型不同进行相应的转换和检查
                                    if field_type == 'bool':
                                        # 布尔值特殊处理
                                        if isinstance(cell_value, bool):
                                            num_val = 1 if cell_value else 0
                                        elif isinstance(cell_value, (int, float)):
                                            num_val = 1 if cell_value != 0 else 0
                                        elif isinstance(cell_value, str):
                                            lower_val = cell_value.lower().strip()
                                            if lower_val in ('true', 't', 'yes', 'y', '1'):
                                                num_val = 1
                                            elif lower_val in ('false', 'f', 'no', 'n', '0', ''):
                                                num_val = 0
                                            else:
                                                self.logger.error(f"页签 '{sheet_name}' 记录 #{row_idx+1} 行 '{field_name}' 字段的值 '{cell_value}' 不是有效的布尔值")
                                                num_val = 0  # 默认为false
                                        else:
                                            num_val = 0
                                    elif field_type in ('float', 'ufloat', 'double'):
                                        # 浮点数检查
                                        num_val = float(cell_value)
                                        if field_type == 'ufloat' and num_val < 0:
                                            error_msg = f"数值范围错误: 页签 '{sheet_name}' 记录 #{row_idx+1} 行 '{field_name}' 字段的值 {num_val} " \
                                                        f"不能为负数，因为字段类型是 {field_type}"
                                            self.logger.error(error_msg)
                                            return False
                                    elif field_type == 'time_t':
                                        # 时间戳特殊处理，使用parse_time_value函数处理
                                        if isinstance(cell_value, str) and not cell_value.strip():
                                            # 空字符串视为0
                                            num_val = 0
                                            cell_value = 0
                                        else:
                                            # 解析时间字符串
                                            num_val = self.parse_time_value(cell_value)
                                            cell_value = num_val
                                    else:
                                        # 整数类型检查
                                        num_val = int(float(cell_value))  # 先转为float再转为int，支持小数点表示
                                    
                                    # 范围检查
                                    if num_val < min_val or num_val > max_val:
                                        error_msg = f"数值范围错误: 页签 '{sheet_name}' 记录 #{row_idx+1} 行 '{field_name}' 字段的值 {num_val} " \
                                                    f"超出了 {field_type} 类型的取值范围 [{min_val}, {max_val}]"
                                        self.logger.error(error_msg)
                                        return False
                            except (ValueError, TypeError):
                                # 非数值类型，记录错误并返回处理失败
                                if cell_value is not None and str(cell_value).strip():
                                    self.logger.error(f"页签 '{sheet_name}' 记录 #{row_idx+1} 行 '{field_name}' 字段的值 '{cell_value}' 不是有效的数值")
                                    return False  # 遇到无效数值，处理失败
                        
                        # 存储字段值
                        row_data[field_name] = cell_value
                
                if row_data:
                    data_rows.append(row_data)
            
            if not data_rows:
                self.logger.warning(f"表格 {sheet_name} 没有有效数据，跳过")
                return False
            
            # 检查唯一字段的值是否有重复
            for field in unique_fields:
                field_name = field['name']
                field_values = [row.get(field_name) for row in data_rows]
                
                # 创建一个字典来跟踪每个值出现的行号
                value_rows = {}
                for row_idx, value in enumerate(field_values):
                    if value in value_rows:
                        value_rows[value].append(row_idx + 5)  # +5是因为数据从第5行开始
                    else:
                        value_rows[value] = [row_idx + 5]
                
                # 检查是否有重复值
                for value, rows in value_rows.items():
                    if len(rows) > 1:
                        error_msg = f"唯一字段重复错误: 页签 '{sheet_name}' 字段 '{field_name}' 被标记为唯一，但值 '{value}' 在第 {', '.join(map(str, rows))} 行重复出现"
                        self.logger.error(error_msg)
                        return False  # 发现重复值，处理失败
            
            # 创建以sheet名命名的目录
            sheet_dir = os.path.join(output_dir, sheet_name)
            os.makedirs(sheet_dir, exist_ok=True)
            
            # 生成服务端文件
            if server_fields:
                # 创建服务端目录
                server_dir = os.path.join(sheet_dir, "Server")
                os.makedirs(server_dir, exist_ok=True)
                
                # 生成服务端结构体定义文件
                server_struct_file = os.path.join(server_dir, f"GameData{sheet_name}Server.txt")
                self.generate_struct_file(server_struct_file, f"{sheet_name}Server", server_fields)
                
                # 生成服务端二进制文件 - 始终在默认位置生成
                server_file = os.path.join(server_dir, f"{sheet_name}Server.SData")
                self.generate_binary_file(server_file, server_fields, data_rows, sheet_name)

                # 如果设置了自定义路径，也在自定义位置生成
                server_key = f"{excel_name}_{sheet_name}_Server"
                if sdata_paths and server_key in sdata_paths and sdata_paths[server_key].strip():
                    # 使用自定义路径，直接在用户设置的目录下生成文件
                    custom_dir = sdata_paths[server_key]
                    custom_server_file = os.path.join(custom_dir, f"{sheet_name}Server.SData")
                    # 确保目录存在
                    os.makedirs(custom_dir, exist_ok=True)
                    # 只生成SData文件，不生成.txt文件
                    self.generate_binary_file(custom_server_file, server_fields, data_rows, sheet_name, generate_txt=False)
                
                # 生成服务端可视化文本文件
                # server_text_file = os.path.join(server_dir, f"{sheet_name}Server.txt")
                # self.generate_data_text_file(server_text_file, server_fields, data_rows)
            
            # 生成客户端文件
            if client_fields:
                # 创建客户端目录
                client_dir = os.path.join(sheet_dir, "Client")
                os.makedirs(client_dir, exist_ok=True)
                
                # 生成客户端结构体定义文件
                client_struct_file = os.path.join(client_dir, f"GameData{sheet_name}Client.txt")
                self.generate_struct_file(client_struct_file, f"{sheet_name}Client", client_fields)
                
                # 生成客户端二进制文件 - 始终在默认位置生成
                client_file = os.path.join(client_dir, f"{sheet_name}Client.SData")
                self.generate_binary_file(client_file, client_fields, data_rows, sheet_name)

                # 如果设置了自定义路径，也在自定义位置生成
                client_key = f"{excel_name}_{sheet_name}_Client"
                if sdata_paths and client_key in sdata_paths and sdata_paths[client_key].strip():
                    # 使用自定义路径，直接在用户设置的目录下生成文件
                    custom_dir = sdata_paths[client_key]
                    custom_client_file = os.path.join(custom_dir, f"{sheet_name}Client.SData")
                    # 确保目录存在
                    os.makedirs(custom_dir, exist_ok=True)
                    # 只生成SData文件，不生成.txt文件
                    self.generate_binary_file(custom_client_file, client_fields, data_rows, sheet_name, generate_txt=False)
                
                # 生成客户端可视化文本文件
                # client_text_file = os.path.join(client_dir, f"{sheet_name}Client.txt")
                # self.generate_data_text_file(client_text_file, client_fields, data_rows)
            
            # 生成加载函数文件
            self._generate_and_save_load_functions(sheet_name, sheet_dir)

            # 只有没有错误时才返回处理成功
            self.logger.info(f"页签 {sheet_name} 处理成功")
            return True
            
        except Exception as e:
            self.logger.error(f"处理页签  {sheet_name} 失败: {str(e)}")
            return False
    
    def generate_binary_file(self, file_path, fields, data_rows, sheet_name="", generate_txt=True):
        """生成二进制文件

        Args:
            file_path: 输出文件路径
            fields: 字段信息
            data_rows: 数据行
            sheet_name: 表格名称
            generate_txt: 是否生成调试txt文件
        """
        try:
            if generate_txt:
                # 创建对应的调试文本文件路径
                debug_txt_path = file_path + ".txt"

                # 同时打开二进制文件和调试文本文件
                with open(file_path, 'wb') as f, open(debug_txt_path, 'w', encoding='utf-8') as debug_f:
                    self._write_binary_data(f, debug_f, fields, data_rows, sheet_name)
            else:
                # 只打开二进制文件，不生成调试文本文件
                with open(file_path, 'wb') as f:
                    self._write_binary_data(f, None, fields, data_rows, sheet_name)
        except Exception as e:
            self.logger.error(f"生成二进制文件失败 {os.path.basename(file_path)}: {str(e)}")
            raise

    def _write_binary_data(self, f, debug_f, fields, data_rows, sheet_name):
        """写入二进制数据的核心逻辑"""
        # 写入数据条数到二进制文件
        f.write(struct.pack('<I', len(data_rows)))
        if debug_f:
            debug_f.write(f"数据条数: {len(data_rows)}\n")

        # 写入每条数据
        for row_idx, row in enumerate(data_rows):

            for field in fields:
                        name = field['name']
                        value = row.get(name)
                        field_type = field['type']
                        
                        if debug_f:
                            debug_f.write(f"{name} ({field_type}):")
                        
                        # 特殊处理字符串数组
                        if field_type.startswith('char[') and '[' in field_type and ']' in field_type and field_type.count('[') == 2:
                            # 这是一个char二维数组（字符串数组）
                            two_dim_array_match = re.match(r'char\[(\d+)\]\[(\d+)\]', field_type)
                            if two_dim_array_match:
                                rows = int(two_dim_array_match.group(1))
                                cols = int(two_dim_array_match.group(2))
                                
                                # 解析原始字符串数组值
                                string_array = []
                                if isinstance(value, str) and value.startswith('[') and value.endswith(']'):
                                    try:
                                        # 尝试解析为Python列表
                                        parsed_data = ast.literal_eval(value)
                                        if isinstance(parsed_data, list):
                                            # 处理字符串列表
                                            for item in parsed_data:                                 
                                                if isinstance(item, str):
                                                    # 去除可能存在的引号
                                                    cleaned_str = item
                                                    if cleaned_str.startswith('"') and cleaned_str.endswith('"'):
                                                        cleaned_str = cleaned_str[1:-1]
                                                    elif cleaned_str.startswith("'") and cleaned_str.endswith("'"):
                                                        cleaned_str = cleaned_str[1:-1]
                                                    string_array.append(cleaned_str)
                                                elif isinstance(item, list) and len(item) > 0:
                                                    # 嵌套列表，只取第一个元素
                                                    if item[0] is not None:
                                                        val_str = str(item[0])
                                                        if val_str.startswith('"') and val_str.endswith('"'):
                                                            val_str = val_str[1:-1]
                                                        elif val_str.startswith("'") and val_str.endswith("'"):
                                                            val_str = val_str[1:-1]
                                                        string_array.append(val_str)
                                                    else:
                                                        string_array.append("")
                                                else:
                                                    # 其他类型
                                                    if item is not None:
                                                        val_str = str(item)
                                                        if val_str.startswith('"') and val_str.endswith('"'):
                                                            val_str = val_str[1:-1]
                                                        elif val_str.startswith("'") and val_str.endswith("'"):
                                                            val_str = val_str[1:-1]
                                                        string_array.append(val_str)
                                                    else:
                                                        string_array.append("")
                                    except (SyntaxError, ValueError):
                                        self.logger.error(f"表格 '{sheet_name}' 记录 #{row_idx+1} 字段 '{name}' ({field_type}) 解析字符串数组失败")
                                        return False  # 解析失败应该返回处理失败
                                elif isinstance(value, list):
                                    # 直接处理列表
                                    for item in value:
                                        if isinstance(item, str):
                                            # 去除可能存在的引号
                                            cleaned_str = item
                                            if cleaned_str.startswith('"') and cleaned_str.endswith('"'):
                                                cleaned_str = cleaned_str[1:-1]
                                            elif cleaned_str.startswith("'") and cleaned_str.endswith("'"):
                                                cleaned_str = cleaned_str[1:-1]
                                            string_array.append(cleaned_str)
                                        elif isinstance(item, list) and len(item) > 0:
                                            # 嵌套列表
                                            if item[0] is not None:
                                                val_str = str(item[0])
                                                if val_str.startswith('"') and val_str.endswith('"'):
                                                    val_str = val_str[1:-1]
                                                elif val_str.startswith("'") and val_str.endswith("'"):
                                                    val_str = val_str[1:-1]
                                                string_array.append(val_str)
                                            else:
                                                string_array.append("")
                                        else:
                                            # 其他类型
                                            if item is not None:
                                                val_str = str(item)
                                                if val_str.startswith('"') and val_str.endswith('"'):
                                                    val_str = val_str[1:-1]
                                                elif val_str.startswith("'") and val_str.endswith("'"):
                                                    val_str = val_str[1:-1]
                                                string_array.append(val_str)
                                            else:
                                                string_array.append("")
                                
                                # 确保数组长度符合要求
                                if len(string_array) > rows:
                                    self.logger.error(f"表格 '{sheet_name}' 记录 #{row_idx+1} 字段 '{name}' ({field_type}) 字符串数组长度 {len(string_array)} 超过定义的长度 {rows}，将被截断")
                                    string_array = string_array[:rows]
                                
                                # 打印原始字符串数组以便调试
                                if debug_f:
                                    debug_f.write(f"{string_array}\n")
                                
                                # 直接将字符串数组传递给二进制转换器
                                # type_converter会处理字符串的编码和转换
                                value = string_array
                        
                        # 处理普通的字符串字段
                        elif field_type.startswith('char[') and not ('[' in field_type and ']' in field_type and field_type.count('[') == 2):
                            # 处理空字符串 - 修改此处逻辑，确保写入空字符串
                            if value is None or value == "":
                                # 强制设置为空字符串，确保被正确写入而不是None
                                value = ""
                                if debug_f:
                                    debug_f.write("\n")
                            else:
                                if debug_f:
                                    debug_f.write(f"\"{value}\"\n")
                        
                        # 处理普通的二维数组字段
                        elif two_dim_array_match := re.match(r'(\w+)\[(\d+)\]\[(\d+)\]', field_type):
                            base_type = two_dim_array_match.group(1)
                            rows = int(two_dim_array_match.group(2))
                            cols = int(two_dim_array_match.group(3))
                            
                            # 非字符串类型的二维数组处理
                            if isinstance(value, str) and value.startswith('[') and value.endswith(']'):
                                try:
                                    # 尝试解析为Python对象
                                    parsed_value = ast.literal_eval(value)
                                    if isinstance(parsed_value, list):
                                        # 统一处理为二维数组
                                        array_data = []
                                        if all(isinstance(item, list) for item in parsed_value):
                                            # 已经是二维结构
                                            array_data = parsed_value
                                            
                                            # 检查每行的列数是否符合要求，如果不足则补齐
                                            for row_sub_idx, row_item in enumerate(array_data):
                                                if len(row_item) < cols:
                                                    self.logger.error(f"表格 '{sheet_name}' 记录 #{row_idx+1} 字段 '{name}' ({field_type}) 二维数组第 {row_sub_idx+1} 行的列数为 {len(row_item)}，小于定义的列数 {cols}，将进行补齐")
                                                    # 根据类型补齐到指定列数
                                                    if base_type in ('float', 'ufloat', 'double'):
                                                        array_data[row_sub_idx].extend([0.0] * (cols - len(row_item)))
                                                    else:
                                                        array_data[row_sub_idx].extend([0] * (cols - len(row_item)))
                                        else:
                                            # 是一维数组，转为二维
                                            for sub_row_idx in range(0, len(parsed_value), cols):
                                                if sub_row_idx + cols <= len(parsed_value):
                                                    array_data.append(parsed_value[sub_row_idx:sub_row_idx+cols])
                                                else:
                                                    # 不足一行的情况
                                                    row_data = parsed_value[sub_row_idx:]
                                                    # 补齐到cols长度
                                                    if base_type in ('float', 'ufloat', 'double'):
                                                        row_data.extend([0.0] * (cols - len(row_data)))
                                                    else:
                                                        row_data.extend([0] * (cols - len(row_data)))
                                                    array_data.append(row_data)
                                        
                                        # 确保行数符合要求
                                        if len(array_data) < rows:
                                            # 不足行数，补充空行
                                            # self.logger.warning(f"表格 '{sheet_name}' 记录 #{row_idx+1} 字段 '{name}' ({field_type}) 二维数组行数 {len(array_data)} 小于定义的行数 {rows}，将补充空行")
                                            for _ in range(rows - len(array_data)):
                                                if base_type in ('float', 'ufloat', 'double'):
                                                    array_data.append([0.0] * cols)
                                                else:
                                                    array_data.append([0] * cols)
                                        elif len(array_data) > rows:
                                            # 超出行数，截断
                                            self.logger.error(f"表格 '{sheet_name}' 记录 #{row_idx+1} 字段 '{name}' ({field_type}) 二维数组行数 {len(array_data)} 超过定义的行数 {rows}，将被截断")
                                            array_data = array_data[:rows]
                                        
                                        value = array_data
                                        if debug_f:
                                            debug_f.write(f"{array_data}\n")
                                except (SyntaxError, ValueError):
                                    # 解析失败，返回处理失败
                                    self.logger.error(f"表格 '{sheet_name}' 记录 #{row_idx+1} 字段 '{name}' ({field_type}) 解析二维数组失败")
                                    return False  # 解析失败应该返回处理失败
                            elif isinstance(value, list):
                                # 直接处理列表值
                                array_data = []
                                
                                # 确保它是二维数组
                                if all(isinstance(item, list) for item in value):
                                    array_data = value
                                    
                                    # 检查每行的列数是否符合要求，如果不足则补齐
                                    for row_sub_idx, row_item in enumerate(array_data):
                                        if len(row_item) < cols:
                                            self.logger.info(f"表格 '{sheet_name}' 记录 #{row_idx+1} 字段 '{name}' ({field_type}) 二维数组第 {row_sub_idx+1} 行的列数为 {len(row_item)}，小于定义的列数 {cols}，将进行补齐")
                                            # 根据类型补齐到指定列数
                                            if base_type in ('float', 'ufloat', 'double'):
                                                array_data[row_sub_idx].extend([0.0] * (cols - len(row_item)))
                                            else:
                                                array_data[row_sub_idx].extend([0] * (cols - len(row_item)))
                                else:
                                    # 一维数组转为二维
                                    for sub_row_idx in range(0, len(value), cols):
                                        if sub_row_idx + cols <= len(value):
                                            array_data.append(value[sub_row_idx:sub_row_idx+cols])
                                        else:
                                            row_data = value[sub_row_idx:]
                                            # 补齐到cols长度
                                            if base_type in ('float', 'ufloat', 'double'):
                                                row_data.extend([0.0] * (cols - len(row_data)))
                                            else:
                                                row_data.extend([0] * (cols - len(row_data)))
                                            array_data.append(row_data)
                                
                                # 确保行数符合要求
                                if len(array_data) < rows:
                                    self.logger.info(f"表格 '{sheet_name}' 记录 #{row_idx+1} 字段 '{name}' ({field_type}) 二维数组行数 {len(array_data)} 小于定义的行数 {rows}，将补充空行")
                                    for _ in range(rows - len(array_data)):
                                        if base_type in ('float', 'ufloat', 'double'):
                                            array_data.append([0.0] * cols)
                                        else:
                                            array_data.append([0] * cols)
                                elif len(array_data) > rows:
                                    self.logger.error(f"表格 '{sheet_name}' 记录 #{row_idx+1} 字段 '{name}' ({field_type}) 二维数组行数 {len(array_data)} 超过定义的行数 {rows}，将被截断")
                                    array_data = array_data[:rows]
                                
                                value = array_data
                                if debug_f:
                                    debug_f.write(f"{array_data}\n")
                            else:
                                # 单个值或空值，创建完整的二维数组
                                if base_type in ('float', 'ufloat', 'double'):
                                    array_data = [[0.0] * cols for _ in range(rows)]
                                else:
                                    array_data = [[0] * cols for _ in range(rows)]
                                
                                # 如果有值则放在第一个位置
                                if value is not None and value != "" and value != 0:
                                    try:
                                        if base_type in ('float', 'ufloat', 'double'):
                                            array_data[0][0] = float(value)
                                        else:
                                            array_data[0][0] = int(float(value))
                                    except (ValueError, TypeError):
                                        pass
                                
                                value = array_data
                                if debug_f:
                                    debug_f.write(f"{array_data}\n")
                        
                        # 处理一维数组字段
                        elif array_match := re.match(r'(\w+)\[(\d+)\]', field_type):
                            base_type = array_match.group(1)
                            size = int(array_match.group(2))
                            
                            if isinstance(value, str) and value.startswith('[') and value.endswith(']'):
                                try:
                                    # 尝试解析字符串为Python列表
                                    array_data = ast.literal_eval(value)
                                    if isinstance(array_data, list):
                                        # 确保数组长度符合定义
                                        if len(array_data) > size:
                                            self.logger.error(f"表格 '{sheet_name}' 记录 #{row_idx+1} 字段 '{name}' ({field_type}) 一维数组长度 {len(array_data)} 超过定义的长度 {size}，将被截断")
                                            array_data = array_data[:size]  # 截断过长的数组
                                        # 补充长度不足的数组
                                        elif len(array_data) < size and base_type != 'char':
                                            # 根据基础类型创建合适的默认值
                                            if base_type in ('float', 'ufloat', 'double'):
                                                array_data.extend([0.0] * (size - len(array_data)))
                                            else:
                                                array_data.extend([0] * (size - len(array_data)))
                                        
                                        value = array_data
                                        if debug_f:
                                            debug_f.write(f"{array_data}\n")
                                except (SyntaxError, ValueError):
                                    # 解析失败，创建默认数组
                                    if base_type in ('float', 'ufloat', 'double'):
                                        array_data = [0.0] * size
                                    else:
                                        array_data = [0] * size
                                    value = array_data
                                    # self.logger.info(f"表格 '{sheet_name}' 记录 #{row_idx+1} 字段 '{name}' ({field_type}) 解析一维数组失败，使用默认值数组")
                            elif isinstance(value, list):
                                # 直接处理列表类型的值
                                array_data = value
                                
                                # 确保数组长度符合定义
                                if len(array_data) > size:
                                    self.logger.error(f"表格 '{sheet_name}' 记录 #{row_idx+1} 字段 '{name}' ({field_type}) 一维数组长度 {len(array_data)} 超过定义的长度 {size}，将被截断")
                                    array_data = array_data[:size]  # 截断过长的数组
                                # 补充长度不足的数组
                                elif len(array_data) < size and base_type != 'char':
                                    # 根据基础类型创建合适的默认值
                                    if base_type in ('float', 'ufloat', 'double'):
                                        array_data.extend([0.0] * (size - len(array_data)))
                                    else:
                                        array_data.extend([0] * (size - len(array_data)))
                                
                                value = array_data
                                if debug_f:
                                    debug_f.write(f"{array_data}\n")
                            else:
                                # 单个值或空值，创建完整长度的一维数组
                                if base_type in ('float', 'ufloat', 'double'):
                                    array_data = [0.0] * size
                                else:
                                    array_data = [0] * size
                                
                                # 如果有值则放在第一个位置
                                if value is not None and value != "" and value != 0:
                                    try:
                                        if base_type in ('float', 'ufloat', 'double'):
                                            array_data[0] = float(value)
                                        else:
                                            array_data[0] = int(float(value))
                                    except (ValueError, TypeError):
                                        pass
                                # elif value is None or value == "":
                                    # 对于空值，记录使用默认值数组的日志
                                    # self.logger.info(f"表格 '{sheet_name}' 记录 #{row_idx+1} 字段 '{name}' ({field_type}) 未配置值，使用默认值数组")
                                
                                value = array_data
                                if debug_f:
                                    debug_f.write(f"{array_data}\n")
                        else:
                            if debug_f:
                                debug_f.write(f"{value}\n")
                        # 将值转换为二进制格式并写入
                        binary_data = self.type_converter.convert_to_binary(value, field_type)
                        f.write(binary_data)
                        
                        # 写入二进制表示到调试文件
                        if debug_f:
                            hex_data = binary_data.hex()
                            formatted_hex = ' '.join(hex_data[i:i+2] for i in range(0, min(len(hex_data), 100), 2))
            if debug_f:
                debug_f.write("\n")
    def format_char_array(self, value):
        """格式化字符串数组，去除所有0填充
        
        Args:
            value: 原始值
            
        Returns:
            str: 格式化后的字符串数组
        """
        string_list = []
        try:
            # 解析字符串值
            if isinstance(value, str) and value.startswith('[') and value.endswith(']'):
                try:
                    parsed = ast.literal_eval(value)
                    if isinstance(parsed, list):
                        for item in parsed:
                            if isinstance(item, list) and len(item) > 0:
                                # 嵌套列表，只取第一个字符串元素
                                first = item[0] if item[0] != 0 else ""
                                if isinstance(first, str) or first:
                                    string_list.append(f'"{first}"')
                            elif isinstance(item, str) and item:
                                string_list.append(f'"{item}"')
                            elif item and item != 0:
                                string_list.append(f'"{str(item)}"')
                except:
                    # 解析失败，尝试使用原值
                    if value:
                        string_list.append(f'"{str(value)}"')
            # 处理列表值
            elif isinstance(value, list):
                for item in value:
                    if isinstance(item, list) and len(item) > 0:
                        # 嵌套列表，只取第一个非零元素
                        first = item[0] if item[0] != 0 else ""
                        if isinstance(first, str) or first:
                            string_list.append(f'"{first}"')
                    elif isinstance(item, str) and item:
                        # 直接添加字符串，添加双引号用于可视化表示
                        string_list.append(f'"{item}"')
                    elif item and item != 0:
                        string_list.append(f'"{str(item)}"')
            # 单个值处理
            elif value and value != 0:
                string_list.append(f'"{str(value)}"')
        except:
            # 处理失败，返回空数组
            pass
        
        # 生成最终格式
        if string_list:
            return f"[{','.join(string_list)}]"
        else:
            return "[]"

    def generate_struct_file(self, file_path, sheet_name, fields):
        """生成C++结构体定义文件
        
        Args:
            file_path: 输出文件路径
            sheet_name: 表格名称
            fields: 字段信息
        """
        try:
            # 获取结构体名称，移除可能包含的Client或Server后缀
            base_sheet_name = sheet_name
            if sheet_name.endswith('Client') or sheet_name.endswith('Server'):
                base_sheet_name = sheet_name.rsplit('Client', 1)[0].rsplit('Server', 1)[0]
            
            with open(file_path, 'w', encoding='utf-8') as f:
                # 直接写入结构体定义，不包含注释头
                f.write(f"struct st{sheet_name}Cfg\n")
                f.write("{\n")
                
                # 写入字段定义
                max_type_len = max(len(field['cpp_type'].split('[')[0]) for field in fields)
                
                # 计算所有字段的实际显示长度，包括数组部分
                field_display_lengths = []
                for field in fields:
                    cpp_type = field['cpp_type']
                    name = field['name']
                    
                    # 检查是否是二维数组类型
                    two_dim_array_match = re.match(r'(\w+)\[(\d+)\]\[(\d+)\]', cpp_type)
                    if two_dim_array_match:
                        base_type = two_dim_array_match.group(1)
                        size1 = two_dim_array_match.group(2)
                        size2 = two_dim_array_match.group(3)
                        array_suffix = f"[{size1}][{size2}]"
                        field_display_lengths.append(len(name) + len(array_suffix))
                    # 检查是否是一维数组类型
                    elif re.match(r'(\w+)\[(\d+)\]', cpp_type):
                        array_match = re.match(r'(\w+)\[(\d+)\]', cpp_type)
                        size = array_match.group(2)
                        array_suffix = f"[{size}]"
                        field_display_lengths.append(len(name) + len(array_suffix))
                    else:
                        field_display_lengths.append(len(name))
                
                # 计算最大字段显示长度，用于对齐注释
                max_field_display_len = max(field_display_lengths) if field_display_lengths else 0
                
                # 设定注释的起始位置，确保有足够的空间
                comment_start_position = max(40, max_type_len + max_field_display_len + 10)
                
                for field in fields:
                    cpp_type = field['cpp_type']
                    name = field['name']
                    comment = field['comment'] or ""
                    
                    # 检查是否是二维数组类型
                    two_dim_array_match = re.match(r'(\w+)\[(\d+)\]\[(\d+)\]', cpp_type)
                    if two_dim_array_match:
                        base_type = two_dim_array_match.group(1)
                        size1 = two_dim_array_match.group(2)
                        size2 = two_dim_array_match.group(3)
                        formatted_type = base_type
                        formatted_name = f"{name}[{size1}][{size2}]"
                    # 检查是否是一维数组类型
                    elif re.match(r'(\w+)\[(\d+)\]', cpp_type):
                        array_match = re.match(r'(\w+)\[(\d+)\]', cpp_type)
                        base_type = array_match.group(1)
                        size = array_match.group(2)
                        formatted_type = base_type
                        formatted_name = f"{name}[{size}]"
                    else:
                        formatted_type = cpp_type
                        formatted_name = name
                    
                    # 格式化类型与名称
                    type_padding = " " * (max_type_len - len(formatted_type) + 4)
                    
                    # 计算整行的长度（到分号位置）
                    line_before_comment = f"    {formatted_type}{type_padding}{formatted_name};"
                    
                    # 计算需要多少空格才能让注释对齐
                    comment_padding = " " * max(1, comment_start_position - len(line_before_comment))
                    
                    # 写入行，注释对齐
                    f.write(f"{line_before_comment}{comment_padding}// {comment}\n")
                
                # 确保结构体结束符单独成行
                f.write("};\n")
                
                # 移除字段对应关系的写入
            
            # self.logger.info(f"生成结构体定义文件: {os.path.basename(file_path)}")
            
        except Exception as e:
            self.logger.error(f"生成结构体定义文件失败 {os.path.basename(file_path)}: {str(e)}")
            raise 

    def _generate_and_save_load_functions(self, sheet_name_for_func, output_sheet_dir):
        """生成并保存C++加载函数到.txt文件"""
        try:
            # self.logger.info(f"为页签 {sheet_name_for_func} 生成加载函数文件...")
            # 从模板分割服务端和客户端部分
            server_template_start = "服务端生成说明："
            client_template_start = "客户端生成说明："

            server_template = ""
            client_template = ""
            
            template_content = self.cpp_load_function_template

            if server_template_start in template_content:
                start_index = template_content.find(server_template_start) + len(server_template_start)
                end_index = template_content.find(client_template_start) if client_template_start in template_content else len(template_content)
                server_template = template_content[start_index:end_index].strip()
            
            if client_template_start in template_content:
                start_index = template_content.find(client_template_start) + len(client_template_start)
                client_template = template_content[start_index:].strip()

            if not server_template and not client_template:
                self.logger.error(f"C++加载函数模板内容格式不正确。")
                return

            function_name_part = sheet_name_for_func + "Cfg"
            func_name_placeholder = f"Load{function_name_part}"

            # 生成服务端函数代码并保存
            if server_template:
                server_function_code = server_template.replace("${页签名}", sheet_name_for_func).replace("${函数名}", func_name_placeholder)
                server_func_file_path = os.path.join(output_sheet_dir, f"Load{sheet_name_for_func}Server.txt")
                os.makedirs(os.path.dirname(server_func_file_path), exist_ok=True) # 确保目录存在
                with open(server_func_file_path, 'w', encoding='utf-8') as f:
                    f.write(server_function_code)
                # self.logger.info(f"服务端加载函数文件 {os.path.basename(server_func_file_path)} 生成成功")

            # 生成客户端函数代码并保存
            if client_template:
                client_function_code = client_template.replace("${页签名}", sheet_name_for_func).replace("${函数名}", func_name_placeholder)
                client_func_file_path = os.path.join(output_sheet_dir, f"Load{sheet_name_for_func}Client.txt")
                os.makedirs(os.path.dirname(client_func_file_path), exist_ok=True) # 确保目录存在
                with open(client_func_file_path, 'w', encoding='utf-8') as f:
                    f.write(client_function_code)
                # self.logger.info(f"客户端加载函数文件 {os.path.basename(client_func_file_path)} 生成成功")

        except Exception as e:
            self.logger.error(f"为页签 {sheet_name_for_func} 生成加载函数文件失败: {e}")

if __name__ == '__main__':
    # 测试代码
    processor = ExcelProcessor()
    processor.process_file("path_to_your_excel_file.xlsx")
