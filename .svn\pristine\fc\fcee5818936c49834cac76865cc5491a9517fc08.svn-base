#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                           QPushButton, QLabel, QTableWidget, QApplication,
                           QSplitter, QTreeWidget, QTreeWidgetItem, QHeaderView,
                           QTableWidgetItem, QAbstractItemView, QComboBox,
                           QCheckBox, QLineEdit, QFileDialog, QMessageBox,
                           QTabWidget, QSpinBox, QStatusBar, QToolBar, QAction)
from PyQt5.QtCore import Qt, QSize, pyqtSignal, QSettings
from PyQt5.QtGui import QIcon, QFont, QTextCursor, QColor, QBrush

from utils.sdata_parser import SDataParser
import re

class SDataViewer(QMainWindow):
    def __init__(self, file_path=None, parent=None):
        super().__init__(parent)
        self.file_path = file_path
        self.parser = SDataParser()
        self.data = None  # 存储解析后的数据
        
        self.init_ui()
        if file_path:
            self.load_file(file_path)
    
    def init_ui(self):
        self.setWindowTitle('SData 数据查看器')
        self.setMinimumSize(1000, 700)
        
        # 创建状态栏
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建顶部区域 - 文件信息和工具栏
        top_layout = QHBoxLayout()
        
        # 文件信息区
        info_layout = QVBoxLayout()
        self.file_label = QLabel("文件: 未加载")
        self.file_label.setStyleSheet("font-weight: bold;")
        info_layout.addWidget(self.file_label)
        
        self.record_count_label = QLabel("记录数: 0")
        info_layout.addWidget(self.record_count_label)
        
        top_layout.addLayout(info_layout)
        top_layout.addStretch(1)
        
        # 工具区域
        tools_layout = QHBoxLayout()
        
        # 搜索框
        self.search_label = QLabel("搜索:")
        tools_layout.addWidget(self.search_label)
        
        self.search_field = QComboBox()
        self.search_field.addItem("全部字段")
        self.search_field.setMinimumWidth(120)
        tools_layout.addWidget(self.search_field)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入搜索内容")
        self.search_input.setMinimumWidth(200)
        self.search_input.returnPressed.connect(self.filter_table)
        tools_layout.addWidget(self.search_input)
        
        self.search_button = QPushButton("搜索")
        self.search_button.clicked.connect(self.filter_table)
        tools_layout.addWidget(self.search_button)
        
        self.clear_search_button = QPushButton("清除")
        self.clear_search_button.clicked.connect(self.clear_search)
        tools_layout.addWidget(self.clear_search_button)
        
        # 添加分隔
        tools_layout.addSpacing(20)
        
        # 操作按钮
        self.reload_button = QPushButton("重新加载")
        self.reload_button.clicked.connect(self.reload_file)
        # self.reload_button.setStyleSheet("background-color: #f0f0f0;")
        tools_layout.addWidget(self.reload_button)
        
        self.export_button = QPushButton("导出为CSV")
        self.export_button.clicked.connect(self.export_to_csv)
        tools_layout.addWidget(self.export_button)
        
        top_layout.addLayout(tools_layout)
        
        # 添加到主布局
        main_layout.addLayout(top_layout)
        
        # 创建表格视图
        self.table = QTableWidget()
        self.table.setEditTriggers(QAbstractItemView.NoEditTriggers)  # 设置为只读
        self.table.setAlternatingRowColors(True)  # 设置交替行颜色
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)  # 设置选择整行
        self.table.setSelectionMode(QAbstractItemView.SingleSelection)  # 设置只能选择一行
        self.table.setSortingEnabled(True)  # 允许排序
        self.table.horizontalHeader().setStretchLastSection(True)  # 最后一列自动拉伸
        self.table.horizontalHeader().setSectionsMovable(True)  # 允许拖动列
        
        # 设置表格样式
        self.table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #d8d8d8;
                gridline-color: #f0f0f0;
                background-color: white;
                alternate-background-color: #f9f9f9;
            }
            QTableWidget::item {
                padding: 4px;
                border-bottom: 1px solid #eeeeee;
            }
            QTableWidget::item:selected {
                background-color: #e0e8f0;
                color: black;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 6px;
                border: 1px solid #d8d8d8;
                border-left: 0px;
                font-weight: bold;
            }
        """)
        
        main_layout.addWidget(self.table)
        
        # 底部状态信息栏
        bottom_layout = QHBoxLayout()
        self.status_label = QLabel("")
        bottom_layout.addWidget(self.status_label)
        bottom_layout.addStretch()
        
        main_layout.addLayout(bottom_layout)
    
    def load_file(self, file_path):
        """加载并解析SData文件，以表格形式显示"""
        if not file_path or not os.path.exists(file_path):
            self.status_label.setText("文件不存在")
            return
        
        self.file_path = file_path
        self.file_label.setText(f"文件: {os.path.basename(file_path)}")
        self.status_label.setText("正在解析文件...")
        
        try:
            # 解析文件为结构化数据
            self.data = self.parser.parse_sdata_file_structured(file_path)
            
            if not self.data['success']:
                self.status_label.setText(f"解析失败: {self.data['message']}")
                # 清空表格
                self.table.setRowCount(0)
                self.table.setColumnCount(0)
                self.record_count_label.setText("记录数: 0")
                return
            
            # 更新记录数显示
            self.record_count_label.setText(f"记录数: {self.data['count']}")
            
            # 设置表格列
            fields = self.data['fields']
            self.table.setColumnCount(len(fields))
            
            # 设置表头
            headers = []
            for field in fields:
                header = f"{field['name']}\n{field['comment']}" if field['comment'] else field['name']
                headers.append(header)
                
                # 更新搜索字段下拉框
                if self.search_field.findText(field['name']) == -1:
                    self.search_field.addItem(field['name'])
            
            self.table.setHorizontalHeaderLabels(headers)
            self.table.horizontalHeader().setDefaultSectionSize(150)  # 设置默认列宽
            
            # 填充数据
            rows = self.data['rows']
            self.table.setRowCount(len(rows))
            
            for row_idx, row_data in enumerate(rows):
                for col_idx, field in enumerate(fields):
                    field_name = field['name']
                    field_type = field['type']
                    
                    # 获取字段值
                    value = row_data.get(field_name, "")
                    
                    # 创建表格单元格项
                    item = QTableWidgetItem(str(value))
                    
                    # 所有数据都使用左对齐
                    item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                    
                    # 特殊处理某些类型的显示
                    if 'bool' in field_type:
                        value_bool = str(value).lower() in ('true', '1', 'yes', 'y')
                        item.setText("是" if value_bool else "否")
                        # 设置背景色
                        if value_bool:
                            item.setBackground(QBrush(QColor("#e6ffe6")))  # 浅绿色
                    
                    # 添加到表格
                    self.table.setItem(row_idx, col_idx, item)
            
            # 自动调整列宽以适应内容
            self.table.resizeColumnsToContents()
            
            # 确保列宽不会太小
            for col in range(self.table.columnCount()):
                width = self.table.columnWidth(col)
                if width < 100:
                    self.table.setColumnWidth(col, 100)
                elif width > 300:
                    self.table.setColumnWidth(col, 300)
            
            self.status_label.setText(f"加载完成，共 {len(rows)} 条记录")
            
        except Exception as e:
            self.status_label.setText(f"解析文件出错: {str(e)}")
    
    def reload_file(self):
        """重新加载当前文件"""
        if self.file_path:
            self.load_file(self.file_path)
    
    def filter_table(self):
        """根据搜索条件筛选表格内容"""
        search_text = self.search_input.text().strip().lower()
        if not search_text:
            # 如果搜索文本为空，显示所有行
            for row in range(self.table.rowCount()):
                self.table.showRow(row)
            self.status_label.setText(f"显示所有 {self.table.rowCount()} 条记录")
            return
        
        search_field = self.search_field.currentText()
        is_search_all = search_field == "全部字段"
        
        # 获取要搜索的列索引
        search_col = -1
        if not is_search_all:
            for col in range(self.table.columnCount()):
                header_text = self.table.horizontalHeaderItem(col).text().split('\n')[0]
                if header_text == search_field:
                    search_col = col
                    break
        
        # 筛选行
        shown_count = 0
        for row in range(self.table.rowCount()):
            found = False
            
            if is_search_all:
                # 搜索所有列
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    if item and search_text in item.text().lower():
                        found = True
                        break
            else:
                # 只搜索指定列
                if search_col >= 0:
                    item = self.table.item(row, search_col)
                    if item and search_text in item.text().lower():
                        found = True
            
            if found:
                self.table.showRow(row)
                shown_count += 1
            else:
                self.table.hideRow(row)
        
        self.status_label.setText(f"找到 {shown_count} 条匹配记录")
    
    def clear_search(self):
        """清除搜索条件，显示所有行"""
        self.search_input.clear()
        self.search_field.setCurrentIndex(0)
        for row in range(self.table.rowCount()):
            self.table.showRow(row)
        self.status_label.setText(f"显示所有 {self.table.rowCount()} 条记录")
    
    def export_to_csv(self):
        """导出表格数据为CSV文件"""
        if not self.data or not self.data['success'] or not self.data['rows']:
            QMessageBox.warning(self, "导出失败", "没有数据可导出")
            return
        
        # 获取保存文件路径
        default_name = os.path.splitext(os.path.basename(self.file_path))[0] + ".csv"
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出CSV", default_name, "CSV文件 (*.csv)")
        
        if not file_path:
            return
        
        try:
            import csv
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入表头
                headers = []
                for col in range(self.table.columnCount()):
                    header = self.table.horizontalHeaderItem(col).text().split('\n')[0]
                    headers.append(header)
                writer.writerow(headers)
                
                # 写入数据
                for row in range(self.table.rowCount()):
                    # 仅导出当前可见的行
                    if not self.table.isRowHidden(row):
                        row_data = []
                        for col in range(self.table.columnCount()):
                            item = self.table.item(row, col)
                            row_data.append(item.text() if item else "")
                        writer.writerow(row_data)
            
            self.status_label.setText(f"成功导出到 {file_path}")
            
        except Exception as e:
            QMessageBox.critical(self, "导出失败", f"导出CSV失败: {str(e)}")
            self.status_label.setText(f"导出失败: {str(e)}")

def main():
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle("Fusion")
    
    # 获取命令行参数中的文件路径
    file_path = sys.argv[1] if len(sys.argv) > 1 else None
    
    viewer = SDataViewer(file_path)
    viewer.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 