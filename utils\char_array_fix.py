#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
字符数组编码修复工具 - 提供猴子补丁来修复TypeConverter中的字符数组编码问题
"""

import re
from core.type_converter import TypeConverter

def patch_char_array():
    """
    为TypeConverter提供猴子补丁，修复字符数组编码问题
    在需要使用TypeConverter的地方导入并调用此函数
    """
    # 保存原始方法
    original_convert = TypeConverter.convert_to_binary
    
    # 定义补丁方法
    def patched_convert_to_binary(self, value, type_str):
        """
        补丁版本的convert_to_binary方法，专门处理char[]类型问题
        """
        # 处理char[]类型
        if type_str.startswith('char['):
            # 解析数组大小
            match = re.match(r'char\[(\d+)\]', type_str)
            if match:
                size = int(match.group(1))
                
                # 处理空值
                if value is None or value == "":
                    return b'\x00' * size
                
                # 转换为字符串
                value_str = str(value)
                
                # UTF-8编码
                try:
                    utf8_bytes = value_str.encode('utf-8')
                    
                    # 检查长度
                    if len(utf8_bytes) >= size:
                        # 截断，确保不破坏UTF-8编码
                        max_len = size - 1
                        truncated = utf8_bytes[:max_len]
                        
                        # 检查是否在多字节字符中间截断
                        while len(truncated) > 0 and (truncated[-1] & 0xC0) == 0x80:
                            truncated = truncated[:-1]
                        
                        utf8_bytes = truncated
                    
                    # 添加终止符并填充
                    result = utf8_bytes + b'\x00' * (size - len(utf8_bytes))
                    return result
                except:
                    # 编码失败，返回空字符串
                    return b'\x00' * size
        
        # 其他类型使用原始方法
        return original_convert(self, value, type_str)
    
    # 应用猴子补丁
    TypeConverter.convert_to_binary = patched_convert_to_binary
    
    return True

# 自动应用补丁
if __name__ == "__main__":
    success = patch_char_array()
    if success:
        print("✓ 已成功应用TypeConverter字符数组编码补丁")
    else:
        print("✗ 应用补丁失败") 