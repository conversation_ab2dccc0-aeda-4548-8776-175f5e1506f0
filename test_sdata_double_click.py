#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试SData文件双击解析功能
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtTest import QTest

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.main_window import MainWindow

def test_sdata_double_click():
    """测试SData文件双击解析功能"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    print("=== SData文件双击解析功能测试 ===")
    print("1. 程序已启动")
    print("2. 请手动测试以下功能：")
    print("   - 切换到SData页签")
    print("   - 双击任意SData文件")
    print("   - 验证是否打开了SData解析窗口而不是文件夹")
    print("3. 测试完成后关闭程序")
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == '__main__':
    test_sdata_double_click()
