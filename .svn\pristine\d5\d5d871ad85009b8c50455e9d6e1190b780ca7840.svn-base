#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
字符数组编码问题修复应用程序

这个脚本提供两种修复方式:
1. 猴子补丁方式: 不修改源文件，临时修复编码问题
2. 直接修改方式: 修改TypeConverter源文件，永久解决问题

使用方法:
python apply_fix.py [--patch|--modify|--revert]

选项:
  --patch   应用猴子补丁(默认选项)
  --modify  修改源文件
  --revert  恢复备份

注意: --modify选项会修改源文件，但会先创建备份
"""

import sys
import os
import shutil
import re
import importlib
from utils.char_array_fix import patch_char_array

def apply_monkey_patch():
    """应用猴子补丁"""
    print("应用猴子补丁修复字符数组编码问题...")
    
    if patch_char_array():
        print("✓ 猴子补丁应用成功")
        print("\n在任何使用TypeConverter的代码中，添加以下导入语句:")
        print("from utils.char_array_fix import patch_char_array")
        print("patch_char_array()  # 应用字符数组编码修复")
        return True
    else:
        print("✗ 猴子补丁应用失败")
        return False

def modify_type_converter():
    """修改TypeConverter源文件"""
    print("修改TypeConverter源文件...")
    
    type_converter_path = "core/type_converter.py"
    backup_path = "core/type_converter.py.bak"
    
    # 检查文件是否存在
    if not os.path.exists(type_converter_path):
        print(f"错误: 找不到文件 {type_converter_path}")
        return False
    
    # 创建备份
    shutil.copy2(type_converter_path, backup_path)
    print(f"✓ 已创建备份: {backup_path}")
    
    # 读取源文件
    with open(type_converter_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 定位char[]处理部分
    char_array_pattern = r'(\s+elif type_str\.startswith\(\'char\[\'\):.*?)(\s+else:)'
    
    # 准备替换内容
    char_array_replacement = r"""
        elif type_str.startswith('char['):
            # 处理字符串类型 char[N]
            match = re.match(r'char\\[(\\d+)\\]', type_str)
            if match:
                size = int(match.group(1))
                
                # 处理空值
                if value is None or value == "":
                    return b'\\x00' * size
                
                # 转换为字符串
                value_str = str(value)
                
                # UTF-8编码
                try:
                    utf8_bytes = value_str.encode('utf-8')
                    
                    # 检查长度
                    if len(utf8_bytes) >= size:
                        # 截断，确保不破坏UTF-8编码
                        max_len = size - 1
                        truncated = utf8_bytes[:max_len]
                        
                        # 检查是否在多字节字符中间截断
                        while len(truncated) > 0 and (truncated[-1] & 0xC0) == 0x80:
                            truncated = truncated[:-1]
                        
                        utf8_bytes = truncated
                    
                    # 添加终止符并填充
                    result = utf8_bytes + b'\\x00' * (size - len(utf8_bytes))
                    return result
                except:
                    # 编码失败，返回空字符串
                    return b'\\x00' * size
\2"""
    
    # 替换代码
    if re.search(char_array_pattern, content, re.DOTALL):
        new_content = re.sub(char_array_pattern, char_array_replacement, content, flags=re.DOTALL)
        
        # 写入修改后的内容
        with open(type_converter_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✓ 源文件修改成功")
        
        # 重新加载模块
        try:
            import core.type_converter
            importlib.reload(core.type_converter)
            print("✓ 模块已重新加载")
        except:
            print("! 模块重新加载失败，可能需要重启应用程序")
        
        return True
    else:
        print("✗ 无法找到源文件中的char[]处理部分，修改失败")
        return False

def revert_backup():
    """恢复备份"""
    print("恢复TypeConverter备份...")
    
    backup_path = "core/type_converter.py.bak"
    type_converter_path = "core/type_converter.py"
    
    if not os.path.exists(backup_path):
        print(f"错误: 找不到备份文件 {backup_path}")
        return False
    
    try:
        shutil.copy2(backup_path, type_converter_path)
        print(f"✓ 已恢复备份: {type_converter_path}")
        
        # 重新加载模块
        try:
            import core.type_converter
            importlib.reload(core.type_converter)
            print("✓ 模块已重新加载")
        except:
            print("! 模块重新加载失败，可能需要重启应用程序")
        
        return True
    except Exception as e:
        print(f"✗ 恢复失败: {str(e)}")
        return False

def test_fix():
    """测试修复效果"""
    print("\n测试修复效果...")
    
    # 导入模块
    try:
        from core.type_converter import TypeConverter
    except ImportError:
        print("✗ 导入TypeConverter失败")
        return False
    
    # 创建转换器
    converter = TypeConverter()
    
    # 测试字符串
    test_str = "测试中文字符串"
    
    # 直接编码
    utf8_bytes = test_str.encode('utf-8')
    
    # 使用TypeConverter
    binary_data = converter.convert_to_binary(test_str, "char[64]")
    
    # 检查结果
    print(f"测试字符串: '{test_str}'")
    print(f"UTF-8编码: {' '.join(f'{b:02x}' for b in utf8_bytes)}")
    print(f"转换结果: {' '.join(f'{b:02x}' for b in binary_data[:len(utf8_bytes)])}")
    
    if utf8_bytes == binary_data[:len(utf8_bytes)]:
        print("✓ 修复成功! 字符编码问题已解决")
        return True
    else:
        print("✗ 修复失败! 字符编码问题仍然存在")
        return False

def print_help():
    """打印帮助信息"""
    print("字符数组编码问题修复工具")
    print("\n使用方法:")
    print("python apply_fix.py [--patch|--modify|--revert]")
    print("\n选项:")
    print("  --patch   应用猴子补丁(默认选项)")
    print("  --modify  修改源文件(会创建备份)")
    print("  --revert  恢复备份")
    print("  --help    显示帮助信息")

def main():
    print("=== 字符数组编码问题修复工具 ===\n")
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        option = sys.argv[1].lower()
        
        if option == "--patch":
            apply_monkey_patch()
        elif option == "--modify":
            modify_type_converter()
        elif option == "--revert":
            revert_backup()
        elif option in ["--help", "-h"]:
            print_help()
            return
        else:
            print(f"错误: 未知选项 {option}")
            print_help()
            return
    else:
        # 默认使用猴子补丁
        apply_monkey_patch()
    
    # 测试修复效果
    test_fix()

if __name__ == "__main__":
    main() 