#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import json
import ctypes
import re
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QLabel, QLineEdit, QFileDialog,
                            QListWidget, QTextEdit, QCheckBox, QMessageBox,
                            QTabWidget, QListWidgetItem, QTreeWidget, QTreeWidgetItem,
                            QMenu, QAction, QStatusBar, QSplitter, QFrame, QToolTip,
                            QApplication, QProgressBar, QProgressDialog, QMenuBar, QActionGroup,
                            QDialog, QTextBrowser, QDialogButtonBox, QTableWidget, QTableWidgetItem,
                            QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt, QSettings, QSize, QTimer
from PyQt5.QtGui import QCursor, QIcon, QFont, QColor, QPalette, QFontMetrics

from core.excel_processor import ExcelProcessor
from utils.logger import Logger
from utils.sdata_parser import SDataParser
from utils.sdata_viewer import SDataViewer
from ui.style_manager import StyleManager


class SmartSearchWidget(QWidget):
    """智能搜索组件，支持实时下拉提示和键盘导航"""

    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.suggestions = []
        self.current_selection = -1
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        # 使用简单的水平布局，只包含搜索框
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 搜索输入框
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索文件名（支持模糊匹配）")
        self.search_edit.textChanged.connect(self.on_text_changed)
        self.search_edit.returnPressed.connect(self.on_return_pressed)
        layout.addWidget(self.search_edit)

        # 创建独立的浮动建议窗口
        self.suggestions_window = QWidget(None, Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.suggestions_window.setVisible(False)
        self.suggestions_window.setAttribute(Qt.WA_ShowWithoutActivating)  # 显示时不激活窗口
        self.suggestions_window.setAttribute(Qt.WA_X11DoNotAcceptFocus)  # X11系统不接受焦点
        self.suggestions_window.setFocusPolicy(Qt.NoFocus)  # 不接受焦点
        self.suggestions_window.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 1px solid #ccc;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            }
        """)

        # 建议窗口布局
        suggestions_layout = QVBoxLayout(self.suggestions_window)
        suggestions_layout.setContentsMargins(0, 0, 0, 0)
        suggestions_layout.setSpacing(0)

        # 建议列表
        self.suggestions_list = QListWidget()
        self.suggestions_list.setFocusPolicy(Qt.NoFocus)  # 不接受焦点
        self.suggestions_list.itemClicked.connect(self.on_suggestion_clicked)
        self.suggestions_list.setStyleSheet("""
            QListWidget {
                border: none;
                background-color: transparent;
                outline: none;
                color: #333;
            }
            QListWidget::item {
                padding: 8px 12px;
                border-bottom: 1px solid #eee;
                min-height: 20px;
                color: #333;
                background-color: transparent;
            }
            QListWidget::item:hover {
                background-color: #f0f0f0;
                color: #333;
            }
            QListWidget::item:selected {
                background-color: #3daee9;
                color: white;
            }
            QListWidget::item:selected:hover {
                background-color: #2e8cc7;
                color: white;
            }
            QListWidget::item:last-child {
                border-bottom: none;
            }
        """)
        suggestions_layout.addWidget(self.suggestions_list)

        # 安装事件过滤器来处理键盘导航
        self.search_edit.installEventFilter(self)

    def eventFilter(self, obj, event):
        """事件过滤器，处理键盘导航和其他事件"""
        if obj == self.search_edit:
            if event.type() == event.KeyPress:
                if self.suggestions_window.isVisible():
                    if event.key() == Qt.Key_Down:
                        self.navigate_suggestions(1)
                        return True
                    elif event.key() == Qt.Key_Up:
                        self.navigate_suggestions(-1)
                        return True
                    elif event.key() == Qt.Key_Escape:
                        self.hide_suggestions()
                        return True
                    elif event.key() == Qt.Key_Tab:
                        if self.current_selection >= 0:
                            self.select_current_suggestion()
                            return True
            elif event.type() == event.FocusOut:
                # 延迟隐藏，给点击建议列表的时间
                QTimer.singleShot(200, self.check_and_hide_suggestions)
        return super().eventFilter(obj, event)

    def resizeEvent(self, event):
        """窗口大小改变时重新定位建议窗口"""
        super().resizeEvent(event)
        if hasattr(self, 'suggestions_window') and self.suggestions_window.isVisible():
            self.position_suggestions_window()

    def moveEvent(self, event):
        """窗口移动时重新定位建议窗口"""
        super().moveEvent(event)
        if hasattr(self, 'suggestions_window') and self.suggestions_window.isVisible():
            self.position_suggestions_window()

    def check_and_hide_suggestions(self):
        """检查是否应该隐藏建议窗口"""
        # 检查搜索框是否仍有焦点，或者鼠标是否在建议窗口上
        if not self.search_edit.hasFocus():
            # 检查鼠标是否在建议窗口上
            if hasattr(self, 'suggestions_window') and self.suggestions_window.isVisible():
                mouse_pos = QCursor.pos()
                window_rect = self.suggestions_window.geometry()
                if not window_rect.contains(mouse_pos):
                    self.hide_suggestions()

    def navigate_suggestions(self, direction):
        """导航建议列表"""
        if not self.suggestions:
            return

        self.current_selection += direction

        # 循环导航
        if self.current_selection >= len(self.suggestions):
            self.current_selection = 0
        elif self.current_selection < 0:
            self.current_selection = len(self.suggestions) - 1

        # 更新选择
        self.suggestions_list.setCurrentRow(self.current_selection)

    def on_text_changed(self, text):
        """文本改变时的处理"""
        if len(text.strip()) >= 1:  # 至少输入1个字符才开始搜索
            self.update_suggestions(text.strip())
        else:
            self.hide_suggestions()

        # 调用主窗口的文本改变处理
        if hasattr(self.main_window, 'on_search_text_changed'):
            self.main_window.on_search_text_changed(text)

    def update_suggestions(self, search_text):
        """更新建议列表"""
        try:
            # 获取所有匹配的文件
            all_matches = self.get_fuzzy_matches(search_text)

            # 限制建议数量
            self.suggestions = all_matches[:10]  # 最多显示10个建议

            # 更新UI
            self.suggestions_list.clear()
            self.current_selection = -1

            if self.suggestions:
                for suggestion in self.suggestions:
                    item_text = f"[{suggestion['type_display']}] {suggestion['filename']}"
                    self.suggestions_list.addItem(item_text)

                self.show_suggestions()
            else:
                self.hide_suggestions()

        except Exception as e:
            print(f"更新建议时出错: {e}")
            self.hide_suggestions()

    def get_fuzzy_matches(self, search_text):
        """获取模糊匹配的文件"""
        matches = []
        search_lower = search_text.lower()

        try:
            # 搜索Excel文件
            if self.main_window.search_filters.get('excel_files', True):
                for i in range(self.main_window.file_list.count()):
                    item = self.main_window.file_list.item(i)
                    filename = item.text()
                    if self.fuzzy_match(filename, search_lower):
                        matches.append({
                            'type': 'excel',
                            'type_display': 'Excel文件',
                            'filename': filename,
                            'tab_index': 0,
                            'list_widget': self.main_window.file_list,
                            'item_index': i,
                            'score': self.calculate_match_score(filename, search_lower)
                        })

            # 搜索结构体文件
            if self.main_window.search_filters.get('struct_files', True):
                # 服务端结构体
                for i in range(self.main_window.server_struct_list.count()):
                    item = self.main_window.server_struct_list.item(i)
                    filename = item.text()
                    if self.fuzzy_match(filename, search_lower):
                        matches.append({
                            'type': 'struct_server',
                            'type_display': '服务端结构体',
                            'filename': filename,
                            'tab_index': 1,
                            'list_widget': self.main_window.server_struct_list,
                            'item_index': i,
                            'subtab_index': 0,
                            'score': self.calculate_match_score(filename, search_lower)
                        })

                # 客户端结构体
                for i in range(self.main_window.client_struct_list.count()):
                    item = self.main_window.client_struct_list.item(i)
                    filename = item.text()
                    if self.fuzzy_match(filename, search_lower):
                        matches.append({
                            'type': 'struct_client',
                            'type_display': '客户端结构体',
                            'filename': filename,
                            'tab_index': 1,
                            'list_widget': self.main_window.client_struct_list,
                            'item_index': i,
                            'subtab_index': 1,
                            'score': self.calculate_match_score(filename, search_lower)
                        })

            # 搜索SData文件
            if self.main_window.search_filters.get('sdata_files', True):
                # 服务端SData
                for i in range(self.main_window.server_sdata_list.count()):
                    item = self.main_window.server_sdata_list.item(i)
                    filename = item.text()
                    if self.fuzzy_match(filename, search_lower):
                        matches.append({
                            'type': 'sdata_server',
                            'type_display': '服务端SData',
                            'filename': filename,
                            'tab_index': 2,
                            'list_widget': self.main_window.server_sdata_list,
                            'item_index': i,
                            'subtab_index': 0,
                            'score': self.calculate_match_score(filename, search_lower)
                        })

                # 客户端SData
                for i in range(self.main_window.client_sdata_list.count()):
                    item = self.main_window.client_sdata_list.item(i)
                    filename = item.text()
                    if self.fuzzy_match(filename, search_lower):
                        matches.append({
                            'type': 'sdata_client',
                            'type_display': '客户端SData',
                            'filename': filename,
                            'tab_index': 2,
                            'list_widget': self.main_window.client_sdata_list,
                            'item_index': i,
                            'subtab_index': 1,
                            'score': self.calculate_match_score(filename, search_lower)
                        })

            # 搜索加载函数文件
            if self.main_window.search_filters.get('load_function_files', True):
                # 服务端加载函数
                for i in range(self.main_window.server_load_function_list.count()):
                    item = self.main_window.server_load_function_list.item(i)
                    filename = item.text()
                    if self.fuzzy_match(filename, search_lower):
                        matches.append({
                            'type': 'load_function_server',
                            'type_display': '服务端加载函数',
                            'filename': filename,
                            'tab_index': 3,
                            'list_widget': self.main_window.server_load_function_list,
                            'item_index': i,
                            'subtab_index': 0,
                            'score': self.calculate_match_score(filename, search_lower)
                        })

                # 客户端加载函数
                for i in range(self.main_window.client_load_function_list.count()):
                    item = self.main_window.client_load_function_list.item(i)
                    filename = item.text()
                    if self.fuzzy_match(filename, search_lower):
                        matches.append({
                            'type': 'load_function_client',
                            'type_display': '客户端加载函数',
                            'filename': filename,
                            'tab_index': 3,
                            'list_widget': self.main_window.client_load_function_list,
                            'item_index': i,
                            'subtab_index': 1,
                            'score': self.calculate_match_score(filename, search_lower)
                        })

            # 按匹配分数排序
            matches.sort(key=lambda x: x['score'], reverse=True)

        except Exception as e:
            print(f"获取模糊匹配时出错: {e}")

        return matches

    def fuzzy_match(self, filename, search_text):
        """模糊匹配算法"""
        filename_lower = filename.lower()

        # 1. 完全包含匹配
        if search_text in filename_lower:
            return True

        # 2. 首字母匹配
        if filename_lower.startswith(search_text):
            return True

        # 3. 模糊字符匹配（允许跳过字符）
        search_index = 0
        for char in filename_lower:
            if search_index < len(search_text) and char == search_text[search_index]:
                search_index += 1

        return search_index == len(search_text)

    def calculate_match_score(self, filename, search_text):
        """计算匹配分数，用于排序"""
        filename_lower = filename.lower()
        score = 0

        # 完全匹配得分最高
        if filename_lower == search_text:
            score += 1000

        # 开头匹配得分较高
        elif filename_lower.startswith(search_text):
            score += 500

        # 包含匹配
        elif search_text in filename_lower:
            score += 300
            # 越早出现得分越高
            index = filename_lower.find(search_text)
            score += max(0, 100 - index)

        # 模糊匹配基础分数
        else:
            score += 100

        # 文件名越短得分越高（相关性更高）
        score += max(0, 50 - len(filename))

        return score

    def show_suggestions(self):
        """显示建议列表"""
        if not self.suggestions:
            return

        # 计算建议窗口的位置和大小
        self.position_suggestions_window()

        # 显示建议窗口
        self.suggestions_window.setVisible(True)
        self.suggestions_window.raise_()

    def hide_suggestions(self):
        """隐藏建议列表"""
        self.suggestions_window.setVisible(False)
        self.current_selection = -1

    def position_suggestions_window(self):
        """定位建议窗口的位置"""
        try:
            # 获取搜索框在全局坐标系中的位置
            global_pos = self.search_edit.mapToGlobal(self.search_edit.rect().bottomLeft())

            # 计算建议窗口的大小
            list_width = self.search_edit.width()
            list_height = min(200, len(self.suggestions) * 35 + 10)

            # 检查屏幕边界
            screen = QApplication.desktop().screenGeometry()
            if global_pos.y() + list_height > screen.bottom():
                # 如果下方空间不够，显示在搜索框上方
                global_pos.setY(global_pos.y() - self.search_edit.height() - list_height)

            # 设置建议窗口的位置和大小
            self.suggestions_window.setGeometry(
                global_pos.x(),
                global_pos.y(),
                list_width,
                list_height
            )

        except Exception as e:
            print(f"定位建议窗口时出错: {e}")



    def on_suggestion_clicked(self, item):
        """建议项被点击"""
        row = self.suggestions_list.row(item)
        if 0 <= row < len(self.suggestions):
            self.select_suggestion(self.suggestions[row])

    def on_return_pressed(self):
        """回车键处理"""
        if self.suggestions_window.isVisible() and self.current_selection >= 0:
            # 选择当前高亮的建议
            self.select_current_suggestion()
        else:
            # 执行普通搜索
            if hasattr(self.main_window, 'perform_search'):
                self.main_window.perform_search()

    def select_current_suggestion(self):
        """选择当前高亮的建议"""
        if 0 <= self.current_selection < len(self.suggestions):
            self.select_suggestion(self.suggestions[self.current_selection])

    def select_suggestion(self, suggestion):
        """选择建议并跳转"""
        # 更新搜索框文本
        self.search_edit.setText(suggestion['filename'])

        # 隐藏建议列表
        self.hide_suggestions()

        # 跳转到文件
        if hasattr(self.main_window, 'jump_to_file'):
            self.main_window.jump_to_file(suggestion)

        # 记录到搜索历史
        if hasattr(self.main_window, 'search_history'):
            search_text = suggestion['filename']
            if search_text not in self.main_window.search_history:
                self.main_window.search_history.insert(0, search_text)
                if len(self.main_window.search_history) > 20:
                    self.main_window.search_history = self.main_window.search_history[:20]


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()

        # Determine base directory for settings file
        if getattr(sys, 'frozen', False):
            # If the application is run as a bundle (e.g., by PyInstaller)
            # sys.executable is the path to the executable file
            self.base_dir = os.path.dirname(sys.executable)
        else:
            # If run as a .py script
            # sys.argv[0] is the script path
            self.base_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
        self.settings_file_path = os.path.join(self.base_dir, "app_settings.json")
        self.logger = Logger() # Logger should be initialized early for base_dir logging
        self.logger.info(f"应用程序基础目录 (用于设置文件): {self.base_dir}")
        self.logger.info(f"设置文件路径: {self.settings_file_path}")
        
        # 初始化样式管理器
        self.style_manager = StyleManager()
        
        # self.settings = QSettings("ConfigExporter", "ConfigExporter") # Removed QSettings for paths and theme
        self.excel_processor = ExcelProcessor()
        
        # 确保excel_processor使用相同的logger实例
        self.excel_processor.logger = self.logger
        
        # 设置应用样式 - 先初始化主题变量, load_settings will overwrite it if found in JSON
        self.current_theme = "light"

        # 初始化SData路径设置
        self.sdata_paths = {}  # 存储SData文件的自定义导出路径
        self.export_sdata_to_custom_location = False  # 是否导出SData到设置位置
        self.sdata_paths_modified = False  # 标记SData路径设置是否有未保存的更改

        # 初始化搜索功能
        self.search_history = []  # 搜索历史记录
        self.current_search_results = []  # 当前搜索结果
        self.search_filters = {  # 搜索过滤器状态
            'excel_files': True,
            'struct_files': True,
            'sdata_files': True,
            'load_function_files': True
        }

        # 初始化搜索功能
        self.search_history = []  # 搜索历史记录
        self.current_search_results = []  # 当前搜索结果
        self.search_filters = {  # 搜索过滤器状态
            'excel_files': True,
            'struct_files': True,
            'sdata_files': True,
            'load_function_files': True
        }
        
        # 加载图标 - 使用兼容打包后的路径
        self.excel_icon = self.load_icon("resources/icons/excel-file.png")
        self.struct_icon = self.load_icon("resources/icons/struct-file.png")
        self.sdata_icon = self.load_icon("resources/icons/sdata-file.png")
        self.load_function_icon = self.load_icon("resources/icons/function-file.png")
        self.path_settings_icon = self.load_icon("resources/icons/path-settings.png")
        
        # 设置应用图标
        self.app_icon = self.load_icon("resources/icons/app-icon-64.png")
        self.setWindowIcon(self.app_icon)
        
        # 先初始化UI组件
        self.init_ui() # path_edit, output_path_edit are created here
        self.load_settings() # Now loads from app_settings.json
        
        # 设置应用样式 - 使用样式管理器应用主题
        self.setup_style() # This calls apply_theme with self.current_theme
        
        # 验证路径有效性
        self.validate_paths()
        
        self.scan_excel_files()
        self.refresh_struct_files(auto_switch=False)  # 启动时加载结构体文件但不切换标签
        self.refresh_sdata_files()  # 启动时加载SData文件列表
        self.refresh_load_function_files() # 启动时加载加载函数文件列表
        
        # 设置状态栏初始消息
        self.statusBar().showMessage("就绪")
        
        # 更新日志文件计数
        self.update_log_files_count()
    
    def update_theme_specific_styles(self):
        """根据当前主题更新特定控件的样式"""
        # 使用样式管理器更新特定控件的样式
        self.style_manager.update_theme_specific_styles(self)
        
    def reset_statusbar_style(self):
        """重置状态栏样式为当前主题的默认样式"""
        # 使用样式管理器重置状态栏样式
        self.style_manager.reset_statusbar_style(self)
    
    def setup_style(self):
        """设置应用的样式和字体"""
        # 使用样式管理器设置样式
        self.style_manager.setup_style(self)
        
    def apply_theme(self, theme_name):
        """应用主题样式"""
        # 保存旧主题用于比较
        old_theme = self.current_theme
        self.current_theme = theme_name
        
        # 保存设置
        self.save_settings() # Save all settings, including the new theme
        
        # 使用样式管理器应用主题
        self.style_manager.apply_theme(self, theme_name)
    
    def reset_log_text_colors(self):
        """重置日志文本颜色以匹配当前主题"""
        # 使用样式管理器重置日志文本颜色
        self.style_manager.reset_log_text_colors(self)
    
    def log_output(self, message):
        """处理日志输出，使用StyleManager来应用样式"""
        # 使用StyleManager获取消息样式
        colored_message = self.style_manager.get_log_message_style(message)
        self.log_text.append(colored_message)
        
        # 根据消息类型设置状态栏样式和消息
        if "开始处理" in message:
            self.statusBar().showMessage("正在处理数据...", 2000)
        elif "错误" in message or "[错误]" in message:
            self.style_manager.set_error_status_style(self)
        elif "警告" in message or "[警告]" in message:
            self.style_manager.set_warning_status_style(self)
        elif "导出完成" in message:
            has_failures = "失败: 0" not in message
            only_skipped = "成功: 0" in message and "跳过: " in message and "失败: 0" in message
            self.style_manager.set_success_status_style(self, has_failures, only_skipped)
        # elif "找到" in message and ("个结构体文件" in message or "个SData文件" in message or "个TXT数据文件" in message or "个Excel配置文件" in message):
        #     self.statusBar().showMessage(message, 3000)
        
        # 自动滚动到底部
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())
        
    def init_ui(self):
        self.setWindowTitle("配置导表工具 v2.0")
        self.setGeometry(120, 100, 1600, 900)  # 设置更合适的窗口位置和大小
        self.setMinimumSize(900, 600)  # 设置最小窗口尺寸，确保在不同分辨率下有良好显示效果
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建状态栏
        status_bar = self.statusBar()
        
        # 创建永久状态信息面板
        self.excel_count_label = QLabel("Excel文件: 0")
        
        self.struct_count_label = QLabel("结构体文件: 0")
        
        self.sdata_count_label = QLabel("SData文件: 0")
        
        # 添加永久信息到状态栏
        status_bar.addPermanentWidget(self.excel_count_label)
        status_bar.addPermanentWidget(self.struct_count_label)
        status_bar.addPermanentWidget(self.sdata_count_label)
        
        # 设置接收拖放
        self.setAcceptDrops(True)
        
        # 主布局
        central_widget = QWidget()
        main_layout = QVBoxLayout()
        main_layout.setSpacing(12)  # 增加部件间距，提供更舒适的视觉体验
        main_layout.setContentsMargins(12, 12, 12, 12)  # 设置更合适的边距
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)
        
        # 搜索栏区域
        search_frame = QFrame()
        search_frame.setFrameShape(QFrame.StyledPanel)
        search_frame.setFrameShadow(QFrame.Raised)
        search_layout = QHBoxLayout(search_frame)
        search_layout.setContentsMargins(15, 10, 15, 10)
        search_layout.setSpacing(8)

        search_label = QLabel("全局搜索:")
        search_label.setMinimumWidth(100)

        # 创建智能搜索框
        self.search_widget = self.create_smart_search_widget()
        self.search_edit = self.search_widget.search_edit

        # 搜索按钮
        search_button = QPushButton("搜索")
        search_button.clicked.connect(self.perform_search)
        search_button.setMinimumWidth(60)
        search_button.setIcon(QIcon.fromTheme("edit-find"))

        # 清除搜索按钮
        clear_search_button = QPushButton("清除")
        clear_search_button.clicked.connect(self.clear_search)
        clear_search_button.setMinimumWidth(60)
        clear_search_button.setIcon(QIcon.fromTheme("edit-clear"))

        # 搜索过滤器按钮
        filter_button = QPushButton("过滤器")
        filter_button.clicked.connect(self.show_search_filters)
        filter_button.setMinimumWidth(60)
        filter_button.setIcon(QIcon.fromTheme("view-filter"))

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_widget, 1)
        search_layout.addWidget(search_button)
        search_layout.addWidget(clear_search_button)
        search_layout.addWidget(filter_button)

        main_layout.addWidget(search_frame)

        # 配置路径选择区域
        path_frame = QFrame()
        path_frame.setFrameShape(QFrame.StyledPanel)
        path_frame.setFrameShadow(QFrame.Raised)
        path_layout = QVBoxLayout(path_frame)
        path_layout.setContentsMargins(15, 15, 15, 15)  # 增加内边距
        path_layout.setSpacing(10)  # 设置垂直间距
        
        # 配置路径选择
        config_path_layout = QHBoxLayout()
        path_label = QLabel("配置文件夹路径:")
        path_label.setMinimumWidth(100)
        self.path_edit = QLineEdit()
        self.path_edit.setReadOnly(True)
        # 移除这里的样式设置，将在update_theme_specific_styles方法中统一处理
        
        browse_button = QPushButton("浏览...")
        browse_button.setIcon(QIcon.fromTheme("folder-open"))
        browse_button.setToolTip("选择Excel配置文件所在文件夹")
        browse_button.clicked.connect(self.browse_folder)
        browse_button.setMinimumWidth(80)
        
        open_config_button = QPushButton("打开")
        open_config_button.setIcon(QIcon.fromTheme("document-open"))
        open_config_button.setToolTip("打开配置文件夹")
        open_config_button.clicked.connect(self.open_config_folder)
        open_config_button.setMinimumWidth(80)
        
        config_path_layout.addWidget(path_label)
        config_path_layout.addWidget(self.path_edit, 1)
        config_path_layout.addWidget(browse_button)
        config_path_layout.addWidget(open_config_button)
        
        # 输出路径选择
        output_path_layout = QHBoxLayout()
        output_path_label = QLabel("输出文件夹路径:")
        output_path_label.setMinimumWidth(100)
        self.output_path_edit = QLineEdit()
        self.output_path_edit.setReadOnly(True)
        # 移除这里的样式设置，将在update_theme_specific_styles方法中统一处理
        self.output_path_edit.setPlaceholderText("不指定则使用配置文件夹路径")
        
        output_browse_button = QPushButton("浏览...")
        output_browse_button.setIcon(QIcon.fromTheme("folder-open"))
        output_browse_button.setToolTip("选择导出文件保存位置")
        output_browse_button.clicked.connect(self.browse_output_folder)
        output_browse_button.setMinimumWidth(80)
        
        open_output_button = QPushButton("打开")
        open_output_button.setIcon(QIcon.fromTheme("document-open"))
        open_output_button.setToolTip("打开输出文件夹")
        open_output_button.clicked.connect(self.open_output_folder)
        open_output_button.setMinimumWidth(80)
        
        output_path_layout.addWidget(output_path_label)
        output_path_layout.addWidget(self.output_path_edit, 1)
        output_path_layout.addWidget(output_browse_button)
        output_path_layout.addWidget(open_output_button)
        
        path_layout.addLayout(config_path_layout)
        path_layout.addLayout(output_path_layout)
        
        main_layout.addWidget(path_frame)
        
        # 文件列表和日志区域 - 使用分割器
        content_splitter = QSplitter(Qt.Horizontal)
        content_splitter.setChildrenCollapsible(False)
        content_splitter.setHandleWidth(6)  # 增加分割条宽度，提高可用性
        
        # 左侧部分：使用TabWidget实现Excel文件和结构体文件的切换
        left_widget = QFrame()
        left_widget.setFrameShape(QFrame.StyledPanel)
        left_widget.setFrameShadow(QFrame.Raised)
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(12, 12, 12, 12)  # 增加内边距
        
        # 主页签 - 包含Excel文件列表、结构体等内容
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        self.tab_widget.setDocumentMode(True)
        self.tab_widget.setElideMode(Qt.ElideRight)  # 标签文字过长时显示省略号
        self.tab_widget.setMovable(False)  # 禁止用户拖动调整标签顺序
        self.tab_widget.setTabsClosable(False)  # 不显示关闭按钮
        
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
        
        # 创建Excel文件列表选项卡
        excel_tab = QWidget()
        excel_layout = QVBoxLayout(excel_tab)
        excel_layout.setContentsMargins(8, 8, 8, 8)  # 增加内边距
        excel_layout.setSpacing(8)  # 增加控件间距
        
        # Excel文件列表
        files_header_layout = QHBoxLayout()
        excel_label = QLabel("Excel配置文件")
        excel_label.setStyleSheet("font-weight: bold;")
        refresh_button = QPushButton("刷新")
        refresh_button.setIcon(QIcon.fromTheme("view-refresh"))
        refresh_button.setToolTip("重新扫描配置路径下的Excel文件")
        refresh_button.clicked.connect(self.scan_excel_files)
        refresh_button.setMaximumWidth(80)
        
        files_header_layout.addWidget(excel_label)
        files_header_layout.addStretch()
        files_header_layout.addWidget(refresh_button)
        
        self.file_list = QListWidget()
        self.file_list.setSelectionMode(QListWidget.ExtendedSelection)
        self.file_list.setAlternatingRowColors(True)
        # 优化列表项样式，增加内边距和改进视觉效果
        self.file_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #cccccc;
                border-radius: 4px;
            }
            QListWidget::item {
                padding: 5px;
                border-bottom: 1px solid #eeeeee;
            }
            QListWidget::item:selected {
                background-color: #d0e4ff;
                color: black;
                border-radius: 2px;
            }
            QListWidget::item:hover {
                background-color: #e8f0fe;
            }
        """)
        
        # 添加双击事件处理
        self.file_list.itemDoubleClicked.connect(self.open_excel_file)
        
        excel_layout.addLayout(files_header_layout)
        excel_layout.addWidget(self.file_list)
        
        # 导出按钮
        buttons_layout = QHBoxLayout()
        self.force_gen_checkbox = QCheckBox("强制生成(忽略时间Ctrl+F)")
        self.force_gen_checkbox.setToolTip("选中后将忽略时间戳检查，强制重新导出所有文件")
        self.force_gen_checkbox.setChecked(True)

        self.export_sdata_custom_checkbox = QCheckBox("导出SData到设置位置")
        self.export_sdata_custom_checkbox.setToolTip("选中后将SData文件导出到自定义设置的位置")
        self.export_sdata_custom_checkbox.setChecked(False)



        self.export_selected_button = QPushButton("导出选中")
        self.export_selected_button.setIcon(self.excel_icon)
        self.export_selected_button.setToolTip("导出选中的Excel文件")
        self.export_selected_button.clicked.connect(self.export_selected)

        self.export_all_button = QPushButton("导出全部")
        self.export_all_button.setIcon(self.excel_icon)
        self.export_all_button.setToolTip("导出所有Excel文件")
        self.export_all_button.clicked.connect(self.export_all)

        buttons_layout.addWidget(self.force_gen_checkbox)
        buttons_layout.addWidget(self.export_sdata_custom_checkbox)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.export_selected_button)
        buttons_layout.addWidget(self.export_all_button)
        
        excel_layout.addLayout(buttons_layout)
        
        # 创建结构体文件列表选项卡
        struct_tab = QWidget()
        struct_layout = QVBoxLayout(struct_tab)
        struct_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建结构体文件列表选项卡的子标签页
        self.struct_subtabs = QTabWidget()
        self.struct_subtabs.setTabPosition(QTabWidget.North)
        self.struct_subtabs.setDocumentMode(True)
        self.struct_subtabs.setMovable(False)  # 禁止页签拖动
        # 连接子标签页切换信号，实现切换时自动刷新
        self.struct_subtabs.currentChanged.connect(lambda: self.refresh_struct_files(auto_switch=False))

        # 创建服务端结构体文件标签页
        server_struct_tab = QWidget()
        server_struct_layout = QVBoxLayout(server_struct_tab)
        server_struct_layout.setContentsMargins(5, 5, 5, 5)

        server_struct_header_layout = QHBoxLayout()
        server_struct_label = QLabel("服务端结构体文件")
        server_struct_label.setStyleSheet("font-weight: bold;")
        server_struct_refresh_button = QPushButton("刷新")
        server_struct_refresh_button.setIcon(QIcon.fromTheme("view-refresh"))
        server_struct_refresh_button.setToolTip("刷新服务端结构体文件列表")
        server_struct_refresh_button.clicked.connect(self.refresh_struct_files)
        server_struct_refresh_button.setMaximumWidth(80)

        server_struct_header_layout.addWidget(server_struct_label)
        server_struct_header_layout.addStretch()
        
        # 添加清除按钮
        server_struct_clear_button = QPushButton("清除")
        server_struct_clear_button.setIcon(QIcon.fromTheme("edit-delete"))
        server_struct_clear_button.setToolTip("清除所有服务端结构体文件")
        server_struct_clear_button.clicked.connect(lambda: self.clear_all_struct_files(is_server=True))
        server_struct_clear_button.setMaximumWidth(80)
        
        server_struct_header_layout.addWidget(server_struct_clear_button)
        server_struct_header_layout.addWidget(server_struct_refresh_button)

        self.server_struct_list = QListWidget()
        self.server_struct_list.setSelectionMode(QListWidget.SingleSelection)
        self.server_struct_list.setAlternatingRowColors(True)
        self.server_struct_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.server_struct_list.customContextMenuRequested.connect(lambda pos: self.show_struct_context_menu(pos, is_server=True))
        self.server_struct_list.itemDoubleClicked.connect(self.show_struct_content)

        server_struct_layout.addLayout(server_struct_header_layout)
        server_struct_layout.addWidget(self.server_struct_list)

        # 创建客户端结构体文件标签页
        client_struct_tab = QWidget()
        client_struct_layout = QVBoxLayout(client_struct_tab)
        client_struct_layout.setContentsMargins(5, 5, 5, 5)

        client_struct_header_layout = QHBoxLayout()
        client_struct_label = QLabel("客户端结构体文件")
        client_struct_label.setStyleSheet("font-weight: bold;")
        client_struct_refresh_button = QPushButton("刷新")
        client_struct_refresh_button.setIcon(QIcon.fromTheme("view-refresh"))
        client_struct_refresh_button.setToolTip("刷新客户端结构体文件列表")
        client_struct_refresh_button.clicked.connect(self.refresh_struct_files)
        client_struct_refresh_button.setMaximumWidth(80)

        client_struct_header_layout.addWidget(client_struct_label)
        client_struct_header_layout.addStretch()
        
        # 添加清除按钮
        client_struct_clear_button = QPushButton("清除")
        client_struct_clear_button.setIcon(QIcon.fromTheme("edit-delete"))
        client_struct_clear_button.setToolTip("清除所有客户端结构体文件")
        client_struct_clear_button.clicked.connect(lambda: self.clear_all_struct_files(is_server=False))
        client_struct_clear_button.setMaximumWidth(80)
        
        client_struct_header_layout.addWidget(client_struct_clear_button)
        client_struct_header_layout.addWidget(client_struct_refresh_button)

        self.client_struct_list = QListWidget()
        self.client_struct_list.setSelectionMode(QListWidget.SingleSelection)
        self.client_struct_list.setAlternatingRowColors(True)
        self.client_struct_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.client_struct_list.customContextMenuRequested.connect(lambda pos: self.show_struct_context_menu(pos, is_server=False))
        self.client_struct_list.itemDoubleClicked.connect(self.show_struct_content)

        client_struct_layout.addLayout(client_struct_header_layout)
        client_struct_layout.addWidget(self.client_struct_list)

        # 添加子标签页到结构体文件标签页
        self.struct_subtabs.addTab(server_struct_tab, "服务端")
        self.struct_subtabs.addTab(client_struct_tab, "客户端")

        struct_layout.addWidget(self.struct_subtabs)
        
        # 创建SData文件树选项卡
        sdata_tab = QWidget()
        sdata_layout = QVBoxLayout(sdata_tab)
        sdata_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建SData文件列表选项卡的子标签页
        self.sdata_subtabs = QTabWidget()
        self.sdata_subtabs.setTabPosition(QTabWidget.North)
        self.sdata_subtabs.setDocumentMode(True)
        self.sdata_subtabs.setMovable(False)  # 禁止页签拖动
        # 连接子标签页切换信号，实现切换时自动刷新
        self.sdata_subtabs.currentChanged.connect(lambda: self.refresh_sdata_files())
        
        # 创建服务端标签页
        server_tab = QWidget()
        server_layout = QVBoxLayout(server_tab)
        server_layout.setContentsMargins(5, 5, 5, 5)
        
        # 服务端文件列表标题
        server_header_layout = QHBoxLayout()
        server_label = QLabel("服务端SData文件")
        server_label.setStyleSheet("font-weight: bold;")
        server_refresh_button = QPushButton("刷新")
        server_refresh_button.setIcon(QIcon.fromTheme("view-refresh"))
        server_refresh_button.setToolTip("刷新服务端SData文件列表")
        server_refresh_button.clicked.connect(self.refresh_sdata_files)
        server_refresh_button.setMaximumWidth(80)
        
        server_header_layout.addWidget(server_label)
        server_header_layout.addStretch()
        
        # 添加清除按钮
        server_sdata_clear_button = QPushButton("清除")
        server_sdata_clear_button.setIcon(QIcon.fromTheme("edit-delete"))
        server_sdata_clear_button.setToolTip("清除所有服务端SData文件")
        server_sdata_clear_button.clicked.connect(lambda: self.clear_all_sdata_files(is_server=True))
        server_sdata_clear_button.setMaximumWidth(80)
        
        server_header_layout.addWidget(server_sdata_clear_button)
        server_header_layout.addWidget(server_refresh_button)
        
        # 创建服务端文件列表
        self.server_sdata_list = QListWidget()
        self.server_sdata_list.setSelectionMode(QListWidget.ExtendedSelection) # Changed from SingleSelection
        self.server_sdata_list.setAlternatingRowColors(True)
        self.server_sdata_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.server_sdata_list.customContextMenuRequested.connect(lambda pos: self.show_sdata_context_menu(pos, is_server=True))
        self.server_sdata_list.itemDoubleClicked.connect(lambda item: self.parse_sdata_file_from_item(item, is_server=True))
        
        server_layout.addLayout(server_header_layout)
        server_layout.addWidget(self.server_sdata_list)
        
        # 创建客户端标签页
        client_tab = QWidget()
        client_layout = QVBoxLayout(client_tab)
        client_layout.setContentsMargins(5, 5, 5, 5)
        
        # 客户端文件列表标题
        client_header_layout = QHBoxLayout()
        client_label = QLabel("客户端SData文件")
        client_label.setStyleSheet("font-weight: bold;")
        client_refresh_button = QPushButton("刷新")
        client_refresh_button.setIcon(QIcon.fromTheme("view-refresh"))
        client_refresh_button.setToolTip("刷新客户端SData文件列表")
        client_refresh_button.clicked.connect(self.refresh_sdata_files)
        client_refresh_button.setMaximumWidth(80)
        
        client_header_layout.addWidget(client_label)
        client_header_layout.addStretch()
        
        # 添加清除按钮
        client_sdata_clear_button = QPushButton("清除")
        client_sdata_clear_button.setIcon(QIcon.fromTheme("edit-delete"))
        client_sdata_clear_button.setToolTip("清除所有客户端SData文件")
        client_sdata_clear_button.clicked.connect(lambda: self.clear_all_sdata_files(is_server=False))
        client_sdata_clear_button.setMaximumWidth(80)
        
        client_header_layout.addWidget(client_sdata_clear_button)
        client_header_layout.addWidget(client_refresh_button)
        
        # 创建客户端文件列表
        self.client_sdata_list = QListWidget()
        self.client_sdata_list.setSelectionMode(QListWidget.ExtendedSelection) # Changed from SingleSelection
        self.client_sdata_list.setAlternatingRowColors(True)
        self.client_sdata_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.client_sdata_list.customContextMenuRequested.connect(lambda pos: self.show_sdata_context_menu(pos, is_server=False))
        self.client_sdata_list.itemDoubleClicked.connect(lambda item: self.parse_sdata_file_from_item(item, is_server=False))
        
        client_layout.addLayout(client_header_layout)
        client_layout.addWidget(self.client_sdata_list)
        
        # 添加子标签页到SData标签页
        self.sdata_subtabs.addTab(server_tab, "服务端")
        self.sdata_subtabs.addTab(client_tab, "客户端")
        
        sdata_layout.addWidget(self.sdata_subtabs)
        
        # 创建加载函数文件列表选项卡
        load_func_files_tab = QWidget()
        load_func_files_layout = QVBoxLayout(load_func_files_tab)
        load_func_files_layout.setContentsMargins(5, 5, 5, 5)

        # 创建加载函数文件的子标签页
        self.load_function_subtabs = QTabWidget()
        self.load_function_subtabs.setTabPosition(QTabWidget.North)
        self.load_function_subtabs.setDocumentMode(True)
        self.load_function_subtabs.setMovable(False)  # 禁止页签拖动
        # 连接子标签页切换信号，实现切换时自动刷新
        self.load_function_subtabs.currentChanged.connect(lambda: self.refresh_load_function_files())

        # 创建服务端加载函数文件标签页
        server_load_func_tab = QWidget()
        server_load_func_layout = QVBoxLayout(server_load_func_tab)
        server_load_func_layout.setContentsMargins(5, 5, 5, 5)

        server_load_func_header_layout = QHBoxLayout()
        server_load_func_label = QLabel("服务端加载函数文件")
        server_load_func_label.setStyleSheet("font-weight: bold;")
        server_load_func_refresh_button = QPushButton("刷新")
        server_load_func_refresh_button.setIcon(QIcon.fromTheme("view-refresh"))
        server_load_func_refresh_button.setToolTip("刷新服务端加载函数文件列表")
        server_load_func_refresh_button.clicked.connect(self.refresh_load_function_files) # 未来可能需要区分刷新
        server_load_func_refresh_button.setMaximumWidth(80)

        server_load_func_header_layout.addWidget(server_load_func_label)
        server_load_func_header_layout.addStretch()
        
        # 添加清除按钮
        server_load_func_clear_button = QPushButton("清除")
        server_load_func_clear_button.setIcon(QIcon.fromTheme("edit-delete"))
        server_load_func_clear_button.setToolTip("清除所有服务端加载函数文件")
        server_load_func_clear_button.clicked.connect(lambda: self.clear_all_load_function_files(is_server=True))
        server_load_func_clear_button.setMaximumWidth(80)
        
        server_load_func_header_layout.addWidget(server_load_func_clear_button)
        server_load_func_header_layout.addWidget(server_load_func_refresh_button)

        self.server_load_function_list = QListWidget()
        self.server_load_function_list.setSelectionMode(QListWidget.SingleSelection)
        self.server_load_function_list.setAlternatingRowColors(True)
        self.server_load_function_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.server_load_function_list.customContextMenuRequested.connect(lambda pos: self.show_load_function_context_menu(pos, is_server=True))
        self.server_load_function_list.itemDoubleClicked.connect(self.show_load_function_file_content)

        server_load_func_layout.addLayout(server_load_func_header_layout)
        server_load_func_layout.addWidget(self.server_load_function_list)
        
        # 创建客户端加载函数文件标签页
        client_load_func_tab = QWidget()
        client_load_func_layout = QVBoxLayout(client_load_func_tab)
        client_load_func_layout.setContentsMargins(5, 5, 5, 5)

        client_load_func_header_layout = QHBoxLayout()
        client_load_func_label = QLabel("客户端加载函数文件")
        client_load_func_label.setStyleSheet("font-weight: bold;")
        client_load_func_refresh_button = QPushButton("刷新")
        client_load_func_refresh_button.setIcon(QIcon.fromTheme("view-refresh"))
        client_load_func_refresh_button.setToolTip("刷新客户端加载函数文件列表")
        client_load_func_refresh_button.clicked.connect(self.refresh_load_function_files) # 未来可能需要区分刷新
        client_load_func_refresh_button.setMaximumWidth(80)

        client_load_func_header_layout.addWidget(client_load_func_label)
        client_load_func_header_layout.addStretch()
        
        # 添加清除按钮
        client_load_func_clear_button = QPushButton("清除")
        client_load_func_clear_button.setIcon(QIcon.fromTheme("edit-delete"))
        client_load_func_clear_button.setToolTip("清除所有客户端加载函数文件")
        client_load_func_clear_button.clicked.connect(lambda: self.clear_all_load_function_files(is_server=False))
        client_load_func_clear_button.setMaximumWidth(80)
        
        client_load_func_header_layout.addWidget(client_load_func_clear_button)
        client_load_func_header_layout.addWidget(client_load_func_refresh_button)

        self.client_load_function_list = QListWidget()
        self.client_load_function_list.setSelectionMode(QListWidget.SingleSelection)
        self.client_load_function_list.setAlternatingRowColors(True)
        self.client_load_function_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.client_load_function_list.customContextMenuRequested.connect(lambda pos: self.show_load_function_context_menu(pos, is_server=False))
        self.client_load_function_list.itemDoubleClicked.connect(self.show_load_function_file_content)

        client_load_func_layout.addLayout(client_load_func_header_layout)
        client_load_func_layout.addWidget(self.client_load_function_list)

        # 添加子标签页到加载函数文件标签页
        self.load_function_subtabs.addTab(server_load_func_tab, "服务端")
        self.load_function_subtabs.addTab(client_load_func_tab, "客户端")

        load_func_files_layout.addWidget(self.load_function_subtabs)

        # 创建SData路径设置页签
        sdata_path_tab = self.create_sdata_path_tab()

        # 创建SData反向推导页签
        from ui.sdata_reverse_tab import SDataReverseTab
        sdata_reverse_tab = SDataReverseTab(self)

        # 添加选项卡到TabWidget
        self.tab_widget.addTab(excel_tab, "表格列表")
        self.tab_widget.addTab(struct_tab, "导出结构")
        self.tab_widget.addTab(sdata_tab, "导出SData")
        self.tab_widget.addTab(load_func_files_tab, "加载函数文件")
        self.tab_widget.addTab(sdata_path_tab, "SData路径设置")
        self.tab_widget.addTab(sdata_reverse_tab, "SData反向推导")

        # 为每个标签页添加独特的图标
        self.tab_widget.setTabIcon(0, self.excel_icon)
        self.tab_widget.setTabIcon(1, self.struct_icon)
        self.tab_widget.setTabIcon(2, self.sdata_icon)
        self.tab_widget.setTabIcon(3, self.load_function_icon)
        self.tab_widget.setTabIcon(4, self.path_settings_icon)  # SData路径设置页签图标
        self.tab_widget.setTabIcon(5, self.sdata_icon)  # SData反向推导页签图标
        
        left_layout.addWidget(self.tab_widget)
        
        # 右侧日志区域
        log_widget = QFrame()
        log_widget.setFrameShape(QFrame.StyledPanel)
        log_widget.setFrameShadow(QFrame.Raised)
        log_layout = QVBoxLayout(log_widget)
        log_layout.setContentsMargins(12, 12, 12, 12)  # 增加内边距
        log_layout.setSpacing(8)  # 增加控件间距
        
        log_header_layout = QHBoxLayout()
        log_label = QLabel("日志输出:")
        log_label.setStyleSheet("font-weight: bold;")
        
        # 日志文件计数标签
        self.log_files_count_label = QLabel()
        self.update_log_files_count()
        
        clear_log_files_button = QPushButton("清除日志文件")
        clear_log_files_button.setIcon(QIcon.fromTheme("edit-delete"))
        clear_log_files_button.setToolTip("删除所有日志文件")
        clear_log_files_button.clicked.connect(self.clear_log_files)
        clear_log_files_button.setMaximumWidth(120)

        clear_log_button = QPushButton("清除日志")
        clear_log_button.setIcon(QIcon.fromTheme("edit-clear"))
        clear_log_button.setToolTip("清除日志内容")
        clear_log_button.clicked.connect(self.clear_log)
        clear_log_button.setMaximumWidth(100)
        
        log_header_layout.addWidget(log_label)
        log_header_layout.addStretch()
        log_header_layout.addWidget(self.log_files_count_label)
        log_header_layout.addWidget(clear_log_files_button)
        log_header_layout.addWidget(clear_log_button)
       
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        # 设置基本样式，其他样式将在update_theme_specific_styles方法中处理
        self.log_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #cccccc;
                border-radius: 4px;
                padding: 2px;
                font-family: "Consolas", "Courier New", monospace;
                line-height: 1.4;
            }
        """)
        # 设置字体和行高，提高可读性
        font = self.log_text.font()
        font.setPointSize(9)  # 设置合适的字体大小
        self.log_text.setFont(font)
        
        log_layout.addLayout(log_header_layout)
        log_layout.addWidget(self.log_text)
        
        # 添加到分割器
        content_splitter.addWidget(left_widget)
        content_splitter.addWidget(log_widget)
        content_splitter.setSizes([400, 400])  # 设置初始分割比例
        
        main_layout.addWidget(content_splitter, 1)  # 添加1的拉伸因子使其充满剩余空间
        
        # 设置日志输出回调
        self.logger.set_output_callback(self.log_output)
    
    def load_settings(self):
        """从 app_settings.json 加载配置 """
        # Try to use the script's directory for a more portable default "config" folder
        # instead of os.getcwd() which can vary depending on how the script is launched.
        try:
            # self.base_dir should be set in __init__
            default_config_path_base = self.base_dir 
        except AttributeError:
            # Fallback if self.base_dir is not set, though it should be.
            default_config_path_base = os.path.dirname(os.path.abspath(sys.argv[0]))

        default_config_path = os.path.join(default_config_path_base, "Config") # Changed to "Config" to match usage elsewhere
        config_path = default_config_path
        output_path = ""
        theme = "light" # Default theme
        window_settings = None  # 初始化窗口设置变量
        sdata_paths = {}  # SData路径设置
        export_sdata_custom = False  # 是否导出SData到设置位置

        try:
            if os.path.exists(self.settings_file_path):
                with open(self.settings_file_path, 'r', encoding='utf-8') as f:
                    settings_data = json.load(f)
                    config_path = settings_data.get("config_path", default_config_path)
                    output_path = settings_data.get("output_path", "")
                    theme = settings_data.get("theme", "light")
                    window_settings = settings_data.get("window", None)  # 获取窗口设置
                    sdata_paths = settings_data.get("sdata_paths", {})  # 获取SData路径设置
                    export_sdata_custom = settings_data.get("export_sdata_custom", False)  # 获取SData导出选项
                    # self.logger.info(f"从 {self.settings_file_path} 加载设置成功。主题: {theme}, 配置路径: {config_path}")
            else:
                self.logger.info(f"配置文件 {self.settings_file_path} 不存在，将使用默认设置并尝试创建。")
                # Attempt to save default settings if file doesn't exist, to create it.
                # This will also create default "Config" and "Output" dirs if validate_paths is called after this
                # or if save_settings itself triggers such logic indirectly.
        except json.JSONDecodeError as e:
            self.logger.error(f"解析配置文件 {self.settings_file_path} 失败: {e}。文件可能已损坏。将使用默认设置。")
            # Fallback to defaults in case of error
            config_path = default_config_path
            output_path = ""
            theme = "light"
        except Exception as e:
            self.logger.error(f"加载配置文件 {self.settings_file_path} 失败: {e}。将使用默认设置。")
            # Fallback to defaults in case of error
            config_path = default_config_path
            output_path = ""
            theme = "light"

        # 统一路径分隔符样式
        config_path = self.normalize_path(config_path)
        output_path = self.normalize_path(output_path) if output_path else ""

        self.path_edit.setText(config_path)
        self.output_path_edit.setText(output_path)
        self.current_theme = theme # This will be used by setup_style -> apply_theme
        self.sdata_paths = sdata_paths  # 应用SData路径设置
        self.export_sdata_to_custom_location = export_sdata_custom  # 应用SData导出选项
        
        # 设置复选框状态
        if hasattr(self, 'export_sdata_custom_checkbox'):
            self.export_sdata_custom_checkbox.setChecked(self.export_sdata_to_custom_location)

        # 应用窗口设置（在窗口显示后执行）
        if window_settings:
            # 使用QTimer延迟执行，确保窗口已经创建
            QTimer.singleShot(100, lambda: self.apply_window_settings(window_settings))
        
        # If the settings file didn't exist, save the defaults now to create it.
        if not os.path.exists(self.settings_file_path):
            self.save_settings() # This will write the current (default) paths and theme
            
    def apply_window_settings(self, window_settings):
        """应用保存的窗口设置"""
        try:
            # 设置窗口位置和大小
            x = window_settings.get("x", 100)
            y = window_settings.get("y", 100)
            width = window_settings.get("width", 1280)
            height = window_settings.get("height", 800)
            
            # 确保窗口在可见区域内
            screen = QApplication.desktop().availableGeometry()
            if x < 0 or x > screen.width() - 100:
                x = 100
            if y < 0 or y > screen.height() - 100:
                y = 100
            if width < 900 or width > screen.width():
                width = 1280
            if height < 600 or height > screen.height():
                height = 800
                
            self.setGeometry(x, y, width, height)
            
            # 设置分割器位置
            splitter_sizes = window_settings.get("splitter_sizes", [])
            if splitter_sizes:
                for child in self.findChildren(QSplitter):
                    if child.orientation() == Qt.Horizontal:  # 只应用到水平分割器
                        child.setSizes(splitter_sizes)
                        break
        except Exception as e:
            self.logger.error(f"应用窗口设置失败: {e}")
            # 出错时使用默认设置
    
    def save_settings(self):
        """将当前设置保存到 app_settings.json """
        # 获取主窗口的几何信息
        geometry = self.geometry()
        
        # 获取分割器位置
        splitter_sizes = []
        for child in self.findChildren(QSplitter):
            if child.orientation() == Qt.Horizontal:  # 只保存水平分割器的状态
                splitter_sizes = child.sizes()
                break
        
        # 获取当前复选框状态
        export_sdata_custom = False
        if hasattr(self, 'export_sdata_custom_checkbox'):
            export_sdata_custom = self.export_sdata_custom_checkbox.isChecked()

        settings_data = {
            "config_path": self.path_edit.text(),
            "output_path": self.output_path_edit.text(),
            "theme": self.current_theme,
            "sdata_paths": getattr(self, 'sdata_paths', {}),
            "export_sdata_custom": export_sdata_custom,
            "window": {
                "x": geometry.x(),
                "y": geometry.y(),
                "width": geometry.width(),
                "height": geometry.height(),
                "splitter_sizes": splitter_sizes
            }
        }
        try:
            with open(self.settings_file_path, 'w', encoding='utf-8') as f:
                json.dump(settings_data, f, ensure_ascii=False, indent=4)
            # self.logger.info(f"设置已保存到 {self.settings_file_path}") # Can be noisy
        except Exception as e:
            self.logger.error(f"保存配置文件 {self.settings_file_path} 失败: {e}")
    
    def browse_folder(self):
        folder_path = QFileDialog.getExistingDirectory(self, "选择配置文件夹", self.path_edit.text())
        if folder_path:
            # 统一路径分隔符样式
            folder_path = self.normalize_path(folder_path)
            self.path_edit.setText(folder_path)
            self.save_settings()
            self.scan_excel_files()
    
    def browse_output_folder(self):
        """浏览选择输出文件夹"""
        current_path = self.output_path_edit.text() or self.path_edit.text()
        folder_path = QFileDialog.getExistingDirectory(self, "选择输出文件夹", current_path)
        if folder_path:
            # 统一路径分隔符样式
            folder_path = self.normalize_path(folder_path)
            self.output_path_edit.setText(folder_path)
            self.save_settings() #确保调用 self.save_settings()
    
    def scan_excel_files(self):
        """扫描并加载Excel文件列表，为每个文件添加Excel图标"""
        self.file_list.clear()
        path = self.path_edit.text()
        
        # self.logger.info(f"正在扫描路径: {path}")
        
        # 检查路径是否为可能无权限的根目录
        import re
        if re.match(r'^[A-Za-z]:[\/\\]?$', path):
            self.logger.warning(f"检测到根目录路径，可能没有写入权限: {path}")
            # 使用默认路径
            default_path = os.path.join(os.getcwd(), "config")
            default_path = self.normalize_path(default_path)
            self.logger.info(f"切换到默认路径: {default_path}")
            path = default_path
            self.path_edit.setText(path)
            self.save_settings()
        
        if os.path.exists(path):
            try:
                excel_files = [f for f in os.listdir(path) if f.endswith('.xlsx') and not f.startswith('~$')]
                excel_files.sort()  # 对文件名进行排序
                
                for file in excel_files:
                    # 创建带图标的列表项
                    item = QListWidgetItem(self.excel_icon, file)
                    self.file_list.addItem(item)
                
                excel_count = len(excel_files)
                # self.logger.info(f"找到 {excel_count} 个Excel配置文件")
                
                # 更新状态栏中的Excel文件计数
                self.excel_count_label.setText(f"Excel文件: {excel_count}")
                
                # 自动切换到表格列表标签
                self.tab_widget.setCurrentIndex(0)
            except Exception as e:
                self.logger.error(f"扫描文件夹出错: {str(e)}")
        else:
            self.logger.warning(f"路径不存在: {path}")
            try:
                os.makedirs(path, exist_ok=True)
                self.logger.info(f"已创建路径: {path}")
            except PermissionError as e:
                self.logger.error(f"无法创建路径，权限被拒绝: {str(e)}")
                # 使用默认路径
                default_path = os.path.join(os.getcwd(), "Config")
                default_path = self.normalize_path(default_path)
                self.logger.info(f"切换到默认路径: {default_path}")
                path = default_path
                self.path_edit.setText(path)
                self.save_settings()
                
                # 尝试创建默认路径
                if not os.path.exists(path):
                    try:
                        os.makedirs(path, exist_ok=True)
                        self.logger.info(f"已创建默认路径: {path}")
                    except Exception as e2:
                        self.logger.error(f"创建默认路径失败: {str(e2)}")
    
    def export_selected(self):
        selected_items = self.file_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "警告", "请先选择要导出的Excel文件")
            return
        
        files_to_export = [item.text() for item in selected_items]
        self.logger.info(f"开始导出选中的 {len(files_to_export)} 个文件...")
        self._process_files(files_to_export)
    
    def export_all(self):
        files_to_export = [self.file_list.item(i).text() for i in range(self.file_list.count())]
        if not files_to_export:
            QMessageBox.warning(self, "警告", "没有可导出的Excel文件")
            return
        
        self.logger.info(f"开始导出全部 {len(files_to_export)} 个文件...")
        self._process_files(files_to_export)
    
    def open_config_folder(self):
        """打开配置文件夹"""
        path = self.path_edit.text()
        if os.path.exists(path):
            self._open_folder(path)
        else:
            self.logger.warning(f"配置路径不存在: {path}")
    
    def open_output_folder(self):
        """打开输出文件夹"""
        path = self.output_path_edit.text()
        if not path:  # 如果输出路径为空，则使用配置路径
            path = self.path_edit.text()
            
        # 检查路径是否为可能无权限的根目录
        import re
        if re.match(r'^[A-Za-z]:[\/\\]?$', path):
            self.logger.warning(f"检测到根目录路径，可能没有写入权限: {path}")
            # 使用默认路径
            default_path = os.path.join(os.getcwd(), "Output")
            default_path = self.normalize_path(default_path)
            self.logger.info(f"切换到默认输出路径: {default_path}")
            path = default_path
            if not self.output_path_edit.text():
                # 如果原来用的是配置路径，就不更新输出路径编辑框
                pass
            else:
                self.output_path_edit.setText(path)
                self.save_settings()
            
        if os.path.exists(path):
            self._open_folder(path)
        else:
            self.logger.warning(f"输出路径不存在: {path}")
            try:
                os.makedirs(path, exist_ok=True)
                self.logger.info(f"已创建输出路径: {path}")
                self._open_folder(path)
            except PermissionError as e:
                self.logger.error(f"无法创建输出路径，权限被拒绝: {str(e)}")
                # 使用默认路径
                default_path = os.path.join(os.getcwd(), "Output")
                default_path = self.normalize_path(default_path)
                self.logger.info(f"切换到默认输出路径: {default_path}")
                path = default_path
                if not self.output_path_edit.text():
                    # 如果原来用的是配置路径，就不更新输出路径编辑框
                    pass
                else:
                    self.output_path_edit.setText(path)
                    self.save_settings()
                
                # 尝试创建默认输出路径
                if not os.path.exists(path):
                    try:
                        os.makedirs(path, exist_ok=True)
                        self.logger.info(f"已创建默认输出路径: {path}")
                        self._open_folder(path)
                    except Exception as e2:
                        self.logger.error(f"创建默认输出路径失败: {str(e2)}")

    def copy_sdata_file(self, file_paths):
        """复制一个或多个SData文件到剪贴板"""
        if not file_paths:
            self.logger.warning("没有选择要复制的文件")
            return
        
        try:
            import win32clipboard
            import win32con
            from ctypes import wintypes
            import struct
            
            # 确保所有路径都是绝对路径
            abs_file_paths = [os.path.abspath(p) for p in file_paths]
            
            # 构建CF_HDROP格式所需的数据
            # DROPFILES结构头部 (20字节)
            # pFiles: 从结构开始到文件列表的偏移量 (通常是20)
            # pt: 拖放点 (x, y) - 对于复制操作，通常是 (0,0)
            # fNC: 是否包含非客户端坐标 (通常是0)
            # fWide: 文件名是否为宽字符 (True for Unicode)
            drop_files_struct_format = "iiii?" # pFiles (UINT), pt.x (LONG), pt.y (LONG), fNC (BOOL), fWide (BOOL)
            # 文件列表以双空字符结尾
            file_list_str = '\0'.join(abs_file_paths) + '\0\0'
            file_list_bytes = file_list_str.encode('utf-16-le') # 使用UTF-16 LE编码
            
            # pFiles是DROPFILES结构的大小
            pFiles = struct.calcsize(drop_files_struct_format)
            # pt.x, pt.y, fNC 设为0
            # fWide 设为True (1)
            drop_files_header = struct.pack(drop_files_struct_format, pFiles, 0, 0, 0, True)
            
            # 组合头部和文件列表数据
            data = drop_files_header + file_list_bytes
            
            win32clipboard.OpenClipboard()
            win32clipboard.EmptyClipboard()
            win32clipboard.SetClipboardData(win32con.CF_HDROP, data)
            win32clipboard.CloseClipboard()
            
            if len(file_paths) == 1:
                self.logger.info(f"文件 {os.path.basename(file_paths[0])} 已复制到剪贴板")
            else:
                self.logger.info(f"{len(file_paths)} 个文件已复制到剪贴板")
            
        except ImportError:
            self.logger.error("复制文件失败: 缺少pywin32库")
            self.logger.info("如果您是从源码运行，请运行: python install_dependencies.py")
            self.logger.info("如果您使用的是打包版本，请重新下载完整版本")
            # 尝试使用旧的单文件复制方法作为后备（如果只有一个文件）
            if len(file_paths) == 1:
                self.logger.info("尝试使用旧的复制方法...")
                try:
                    from pathlib import Path
                    import win32com.client
                    
                    file_path = file_paths[0]
                    file_name = os.path.basename(file_path)
                    file_path_win = str(Path(file_path).resolve())
                    
                    shell = win32com.client.Dispatch("Shell.Application")
                    folder = shell.NameSpace(os.path.dirname(file_path_win))
                    file_item = folder.ParseName(os.path.basename(file_path_win))
                    
                    if file_item is not None:
                        # 使用InvokeVerbEx而不是InvokeVerb，因为它在某些情况下更可靠
                        # 但对于简单的复制，InvokeVerb通常就足够了
                        file_item.InvokeVerb("Copy")
                        self.logger.info(f"文件 {file_name} (通过旧方法) 已复制到剪贴板")
                    else:
                        self.logger.error(f"找不到文件 (旧方法): {file_path_win}")
                except Exception as e_old:
                    self.logger.error(f"旧的复制方法也失败: {str(e_old)}")
            else:
                self.logger.warning("无法复制多个文件，因为缺少pywin32库且旧方法不支持多文件。")
        
        except Exception as e:
            self.logger.error(f"复制文件失败: {str(e)}")

    def parse_sdata_file_from_item(self, item, is_server=True):
        """双击SData文件时解析文件内容"""
        # 获取文件路径
        file_path = item.data(Qt.UserRole)

        # 如果不是SData文件项，不处理
        if not file_path or not file_path.endswith('.SData'):
            return

        # 检查文件是否存在
        if not os.path.exists(file_path):
            self.logger.error(f"SData文件不存在: {file_path}")
            return

        # 调用解析方法
        try:
            self.logger.info(f"正在解析SData文件: {os.path.basename(file_path)}")
            self.parse_sdata_file(file_path)
        except Exception as e:
            self.logger.error(f"解析SData文件失败: {str(e)}")

    def open_sdata_location(self, item, is_server=True):
        """打开SData文件所在目录（用于右键菜单等）"""
        # 获取文件路径
        file_path = item.data(Qt.UserRole)

        # 如果不是SData文件项，不处理
        if not file_path or not file_path.endswith('.SData'):
            return

        # 获取文件所在目录
        dir_path = os.path.dirname(file_path)

        if os.path.exists(dir_path):
            try:
                # self.logger.info(f"正在打开目录: {dir_path}")
                self._open_folder(dir_path)
            except Exception as e:
                self.logger.error(f"打开文件所在目录失败: {str(e)}")
        else:
            self.logger.error(f"目录不存在: {dir_path}")

    def closeEvent(self, event):
        """窗口关闭事件，保存当前设置"""
        self.save_settings()  # 保存窗口状态和其他设置
        event.accept()
    
    def dragEnterEvent(self, event):
        """处理拖动进入事件，判断是否接受拖放"""
        # 检查是否是文件拖放
        if event.mimeData().hasUrls():
            # 只检查是否包含Excel文件
            for url in event.mimeData().urls():
                file_path = url.toLocalFile()
                if file_path.endswith('.xlsx') and not os.path.basename(file_path).startswith('~$'):
                    event.acceptProposedAction()
                    # 显示拖放提示区域
                    self.showDropIndicator(True)
                    return
        event.ignore()

    def dragMoveEvent(self, event):
        """处理拖动移动事件"""
        # 如果包含Excel文件，接受动作
        if event.mimeData().hasUrls():
            has_excel = False
            
            for url in event.mimeData().urls():
                file_path = url.toLocalFile()
                if file_path.endswith('.xlsx') and not os.path.basename(file_path).startswith('~$'):
                    has_excel = True
            
            if has_excel:
                event.acceptProposedAction()
                # 显示拖放提示信息
                self.statusBar().showMessage("松开鼠标以导入Excel文件", 2000)
                return
        event.ignore()

    def dragLeaveEvent(self, event):
        """处理拖动离开事件"""
        # 隐藏拖放提示区域
        self.showDropIndicator(False)
        event.accept()

    def dropEvent(self, event):
        """处理拖放事件，导入Excel文件"""
        # 隐藏拖放提示区域
        self.showDropIndicator(False)
        
        excel_files = []
        sdata_files = []
        
        # 获取所有拖放的Excel文件和SData文件
        for url in event.mimeData().urls():
            file_path = url.toLocalFile()
            if file_path.endswith('.xlsx') and not os.path.basename(file_path).startswith('~$'):
                excel_files.append(file_path)
        
        if not excel_files:
            event.ignore()
            return
        
        # 接受拖放动作
        event.acceptProposedAction()
        self.statusBar().showMessage(f"正在处理 {len(excel_files)} 个Excel文件...", 2000)
        
        # 要求用户确认
        reply = QMessageBox.question(
            self, 
            "确认导入", 
            f"是否要导入并处理以下 {len(excel_files)} 个Excel文件?\n\n" + 
            "\n".join([os.path.basename(f) for f in excel_files]),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )
        
        if reply == QMessageBox.Yes:
            try:
                # 处理拖放的文件
                force_gen = self.force_gen_checkbox.isChecked()
                output_path = self.output_path_edit.text() or None
                
                success_count = 0
                skipped_count = 0
                failed_count = 0
                
                self.logger.info(f"开始处理拖放的 {len(excel_files)} 个Excel文件...")
                
                # 遍历处理每个文件
                for i, file_path in enumerate(excel_files):
                    # 更新状态栏进度
                    file_name = os.path.basename(file_path)
                    self.statusBar().showMessage(f"正在处理文件 ({i+1}/{len(excel_files)}): {file_name}")
                    QApplication.processEvents()  # 保持UI响应
                    
                    self.logger.info(f"处理文件: {file_name}")
                    
                    # 检查是否启用了SData自定义导出位置
                    export_sdata_custom = self.export_sdata_custom_checkbox.isChecked()
                    sdata_paths_to_use = self.sdata_paths if export_sdata_custom else None
                    result = self.excel_processor.process_file(file_path, force_gen, output_path, sdata_paths_to_use)
                    if result:
                        if not force_gen and file_path in self.excel_processor.timestamp_cache:
                            file_mtime = os.path.getmtime(file_path)
                            if file_mtime <= self.excel_processor.timestamp_cache[file_path]:
                                skipped_count += 1
                                continue
                        success_count += 1
                    else:
                        failed_count += 1
                
                # 更新导出结果提示
                result_message = f"拖放导入完成! 成功: {success_count}, 跳过: {skipped_count}, 失败: {failed_count}"
                self.logger.info(result_message)
                
                # 在状态栏显示结果摘要
                if failed_count > 0:
                    self.statusBar().showMessage(f"导入完成，有 {failed_count} 个文件失败", 5000)
                elif success_count == 0 and skipped_count > 0:
                    self.statusBar().showMessage("导入完成，所有文件均无修改", 5000)
                else:
                    self.statusBar().showMessage(f"导入成功完成 ({success_count} 个文件)", 5000)
                
                # 刷新文件列表（重新扫描配置目录）
                self.scan_excel_files()
                
                # 如果有成功处理的文件，刷新结构体列表和其他列表
                if success_count > 0:
                    self.refresh_struct_files(auto_switch=True)
                    self.refresh_sdata_files()
                    self.refresh_load_function_files() # 新增：刷新加载函数文件列表
            except Exception as e:
                self.logger.error(f"处理文件时出错: {str(e)}")
                # 确保错误消息在主线程中显示
                QApplication.processEvents()
                self.statusBar().showMessage(f"处理出错: {str(e)}", 5000)

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        
        # 选择配置路径动作
        config_path_action = QAction('选择配置路径...', self)
        config_path_action.setShortcut('Ctrl+O')
        config_path_action.triggered.connect(self.browse_folder)
        file_menu.addAction(config_path_action)
        
        # 选择输出路径动作
        output_path_action = QAction('选择输出路径...', self)
        output_path_action.triggered.connect(self.browse_output_folder)
        file_menu.addAction(output_path_action)

        # SData导出位置设置动作
        sdata_path_action = QAction('SData导出位置设置...', self)
        sdata_path_action.setToolTip('设置每个SData文件的自定义导出位置')
        sdata_path_action.triggered.connect(self.switch_to_sdata_path_tab)
        file_menu.addAction(sdata_path_action)

        file_menu.addSeparator()
        
        # 导出动作
        export_selected_action = QAction('导出选中', self)
        export_selected_action.setShortcut('Ctrl+S')
        export_selected_action.setIcon(self.excel_icon)
        export_selected_action.triggered.connect(self.export_selected)
        file_menu.addAction(export_selected_action)
        
        export_all_action = QAction('导出全部', self)
        export_all_action.setShortcut('Ctrl+Shift+S')
        export_all_action.setIcon(self.excel_icon)
        export_all_action.triggered.connect(self.export_all)
        file_menu.addAction(export_all_action)
        
        file_menu.addSeparator()
        
        # 退出动作
        exit_action = QAction('退出', self)
        exit_action.setShortcut('Alt+F4')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 编辑菜单
        edit_menu = menubar.addMenu('编辑')
        
        # 强制生成选项
        force_gen_action = QAction('强制生成(忽略时间戳)', self)
        force_gen_action.setShortcut('Ctrl+F')
        force_gen_action.setCheckable(True)
        force_gen_action.setChecked(True)
        force_gen_action.triggered.connect(lambda checked: self.force_gen_checkbox.setChecked(checked))
        edit_menu.addAction(force_gen_action)
        
        # 将复选框状态与动作状态同步
        self.force_gen_checkbox = QCheckBox("强制生成(忽略时间戳)")
        self.force_gen_checkbox.toggled.connect(lambda checked: force_gen_action.setChecked(checked))
        
        edit_menu.addSeparator()
        
        # 清除日志动作
        clear_log_action = QAction('清除日志', self)
        clear_log_action.setShortcut('Delete')
        clear_log_action.triggered.connect(self.clear_log)
        edit_menu.addAction(clear_log_action)
        
        # 清除日志文件动作
        clear_log_files_action = QAction('清除日志文件', self)
        clear_log_files_action.setShortcut('Ctrl+Shift+Delete')
        clear_log_files_action.triggered.connect(self.clear_log_files)
        edit_menu.addAction(clear_log_files_action)
        
        # 重置日志文本颜色动作
        reset_log_colors_action = QAction('重置日志文本颜色', self)
        reset_log_colors_action.setShortcut('Ctrl+L')
        reset_log_colors_action.triggered.connect(self.reset_log_text_colors)
        edit_menu.addAction(reset_log_colors_action)

        edit_menu.addSeparator()

        # 搜索功能
        search_action = QAction('搜索文件', self)
        search_action.setShortcut('Ctrl+F')
        search_action.triggered.connect(self.focus_search)
        edit_menu.addAction(search_action)

        # 搜索过滤器
        search_filter_action = QAction('搜索过滤器', self)
        search_filter_action.setShortcut('Ctrl+Shift+F')
        search_filter_action.triggered.connect(self.show_search_filters)
        edit_menu.addAction(search_filter_action)
        
        # 视图菜单
        view_menu = menubar.addMenu('视图')
        
        # 刷新动作
        refresh_action = QAction('刷新当前视图', self)
        refresh_action.setShortcut('F5')
        refresh_action.triggered.connect(self.refresh_current_view)
        view_menu.addAction(refresh_action)
        
        view_menu.addSeparator()
        
        # 主题子菜单
        theme_menu = view_menu.addMenu('主题')
        
        # 浅色主题动作
        light_theme_action = QAction('浅色主题', self)
        light_theme_action.setCheckable(True)
        light_theme_action.setChecked(self.current_theme == "light")
        light_theme_action.triggered.connect(lambda: self.apply_theme("light"))
        theme_menu.addAction(light_theme_action)
        
        # 深色主题动作
        dark_theme_action = QAction('深色主题', self)
        dark_theme_action.setCheckable(True)
        dark_theme_action.setChecked(self.current_theme == "dark")
        dark_theme_action.triggered.connect(lambda: self.apply_theme("dark"))
        theme_menu.addAction(dark_theme_action)
        
        # 确保主题动作互斥
        theme_group = QActionGroup(self)
        theme_group.addAction(light_theme_action)
        theme_group.addAction(dark_theme_action)
        theme_group.setExclusive(True)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助')
        
        # 快捷键帮助动作
        shortcuts_help_action = QAction('快捷键帮助', self)
        shortcuts_help_action.setShortcut('F1')
        shortcuts_help_action.triggered.connect(self.show_shortcuts_help)
        help_menu.addAction(shortcuts_help_action)
        
        help_menu.addSeparator()
        
        # 关于动作
        about_action = QAction('关于', self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)

    def on_tab_changed(self, index):
        """处理标签页切换事件，自动刷新当前标签页内容"""
        # 检查路径是否有效，避免在应用启动时因路径为空而出错
        path = self.path_edit.text()
        if not path:
            return

        # 检查是否从SData路径设置页签切换出去，且有未保存的更改
        previous_index = getattr(self, '_previous_tab_index', -1)
        if previous_index == 4 and index != 4 and self.sdata_paths_modified:
            # 从SData路径设置页签切换到其他页签，且有未保存的更改
            reply = QMessageBox.question(
                self,
                "未保存的更改",
                "SData路径设置有未保存的更改，是否保存？",
                QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                # 保存设置
                self.save_sdata_path_settings()
            elif reply == QMessageBox.Cancel:
                # 取消切换，回到SData路径设置页签
                self.tab_widget.setCurrentIndex(4)
                return
            # 如果选择No，则继续切换但不保存

        # 记录当前页签索引，用于下次切换时检查
        self._previous_tab_index = index

        if index == 0:  # Excel表格列表
            self.scan_excel_files()
        elif index == 1:  # 导出结构
            self.refresh_struct_files(auto_switch=False)
        elif index == 2:  # 导出SData
            self.refresh_sdata_files()
        elif index == 3:  # 加载函数文件
            self.refresh_load_function_files()
        elif index == 4:  # SData路径设置
            self.refresh_sdata_path_table()
        elif index == 5:  # SData反向推导
            pass  # 反向推导页签不需要特殊的刷新操作
    
    def refresh_current_view(self):
        """刷新当前活动标签页"""
        current_tab = self.tab_widget.currentIndex()
        if current_tab == 0:  # Excel表格列表
            self.scan_excel_files()
        elif current_tab == 1:  # 导出结构
            self.refresh_struct_files()
        elif current_tab == 2:  # 导出SData
            self.refresh_sdata_files()
        elif current_tab == 3: # 加载函数文件
            self.refresh_load_function_files()
        elif current_tab == 4:  # SData路径设置
            self.refresh_sdata_path_table()
        elif current_tab == 5:  # SData反向推导
            pass  # 反向推导页签不需要特殊的刷新操作
        
    def show_about_dialog(self):
        """显示关于对话框"""
        about_text = """
        <div style="text-align: center;">
            <h2>配置导表工具 v2.0</h2>
            <p>一个用于处理Excel配置文件并导出为多种格式的工具</p>
            <p>支持导出结构体定义、SData二进制文件</p>
            <p>作者：@晓晨</p>
        </div>
        """
        
        QMessageBox.about(self, "关于配置导表工具", about_text)



    def validate_sdata_paths(self, files):
        """验证SData路径设置，返回缺失路径的列表"""
        missing_paths = []

        for file in files:
            excel_path = os.path.join(self.path_edit.text(), file)
            if not os.path.exists(excel_path):
                continue

            try:
                import openpyxl
                workbook = openpyxl.load_workbook(excel_path, data_only=True)
                excel_name = os.path.splitext(file)[0]

                for sheet_name in workbook.sheetnames:
                    # 检查页签是否有效（至少5行数据）
                    sheet = workbook[sheet_name]
                    if sheet.max_row < 5:
                        continue

                    # 读取第4行的生成类型信息
                    field_targets = [cell.value for cell in sheet[4]]

                    # 检查是否有server或client字段
                    has_server = any(target in ['server', 'all'] for target in field_targets if target)
                    has_client = any(target in ['client', 'all'] for target in field_targets if target)

                    if has_server:
                        server_key = f"{excel_name}_{sheet_name}_Server"
                        if server_key not in self.sdata_paths or not self.sdata_paths[server_key].strip():
                            missing_paths.append(f"{file}/{sheet_name}/Server.SData")

                    if has_client:
                        client_key = f"{excel_name}_{sheet_name}_Client"
                        if client_key not in self.sdata_paths or not self.sdata_paths[client_key].strip():
                            missing_paths.append(f"{file}/{sheet_name}/Client.SData")

                workbook.close()

            except Exception as e:
                self.logger.error(f"验证SData路径时读取Excel文件失败 {file}: {str(e)}")

        return missing_paths

    def validate_paths(self):
        """验证输入和输出路径的有效性，如有必要进行修正"""
        # 验证输入路径
        config_path = self.path_edit.text()
        
        # 检查输入路径是否为可能无权限的根目录
        import re
        if re.match(r'^[A-Za-z]:[\/\\]?$', config_path):
            self.logger.warning(f"检测到根目录路径，可能没有写入权限: {config_path}")
            # 使用默认路径
            default_path = os.path.join(self.base_dir, "Config") # Use base_dir
            default_path = self.normalize_path(default_path)
            self.logger.info(f"切换到默认路径: {default_path}")
            config_path = default_path
            self.path_edit.setText(config_path)
            self.save_settings() # Save all current settings
        
        # 确保输入路径存在
        if not os.path.exists(config_path):
            try:
                os.makedirs(config_path, exist_ok=True)
                self.logger.info(f"已创建配置路径: {config_path}")
            except PermissionError as e:
                self.logger.error(f"无法创建配置路径，权限被拒绝: {str(e)}")
                # 使用默认路径
                default_path = os.path.join(self.base_dir, "Config") # Use base_dir
                default_path = self.normalize_path(default_path)
                self.logger.info(f"切换到默认路径: {default_path}")
                config_path = default_path
                self.path_edit.setText(config_path)
                self.save_settings() # Save all current settings
                
                # 尝试创建默认路径
                if not os.path.exists(config_path):
                    try:
                        os.makedirs(config_path, exist_ok=True)
                        self.logger.info(f"已创建默认路径: {config_path}")
                    except Exception as e2:
                        self.logger.error(f"创建默认路径失败: {str(e2)}")
        
        # 验证输出路径（如果已设置）
        output_path = self.output_path_edit.text()
        if output_path:
            # 检查输出路径是否为可能无权限的根目录
            if re.match(r'^[A-Za-z]:[\/\\]?$', output_path):
                self.logger.warning(f"检测到输出根目录路径，可能没有写入权限: {output_path}")
                # 使用默认路径
                default_output_path = os.path.join(self.base_dir, "Output") # Use base_dir
                default_output_path = self.normalize_path(default_output_path)
                self.logger.info(f"切换到默认输出路径: {default_output_path}")
                output_path = default_output_path
                self.output_path_edit.setText(output_path)
                self.save_settings() # Save all current settings
            
            # 确保输出路径存在
            if not os.path.exists(output_path):
                try:
                    os.makedirs(output_path, exist_ok=True)
                    self.logger.info(f"已创建输出路径: {output_path}")
                except PermissionError as e:
                    self.logger.error(f"无法创建输出路径，权限被拒绝: {str(e)}")
                    # 使用默认路径
                    default_output_path = os.path.join(self.base_dir, "Output") # Use base_dir
                    default_output_path = self.normalize_path(default_output_path)
                    self.logger.info(f"切换到默认输出路径: {default_output_path}")
                    output_path = default_output_path
                    self.output_path_edit.setText(output_path)
                    self.save_settings() # Save all current settings
                    
                    # 尝试创建默认输出路径
                    if not os.path.exists(output_path):
                        try:
                            os.makedirs(output_path, exist_ok=True)
                            self.logger.info(f"已创建默认输出路径: {output_path}")
                        except Exception as e2:
                            self.logger.error(f"创建默认输出路径失败: {str(e2)}")

    def refresh_load_function_files(self):
        """刷新加载函数文件列表"""
        self.server_load_function_list.clear()
        self.client_load_function_list.clear()
        base_path = self.output_path_edit.text() or self.path_edit.text()
        server_load_function_files = []
        client_load_function_files = []

        if os.path.exists(base_path):
            try:
                # 遍历所有Excel对应的目录
                for excel_dir_name in os.listdir(base_path):
                    excel_dir_path = os.path.join(base_path, excel_dir_name)
                    if os.path.isdir(excel_dir_path):
                        # 遍历Excel目录下的所有Sheet对应的目录
                        for sheet_dir_name in os.listdir(excel_dir_path):
                            sheet_dir_path = os.path.join(excel_dir_path, sheet_dir_name)
                            if os.path.isdir(sheet_dir_path):
                                # 查找该目录下的所有加载函数文件 (Load*Server.txt, Load*Client.txt)
                                for file_name in os.listdir(sheet_dir_path):
                                    if file_name.startswith('Load') and file_name.endswith('.txt'):
                                        full_path = os.path.join(sheet_dir_path, file_name)
                                        display_name = os.path.basename(full_path) # 仅显示文件名
                                        if 'Server.txt' in file_name:
                                            server_load_function_files.append((display_name, full_path))
                                        elif 'Client.txt' in file_name:
                                            client_load_function_files.append((display_name, full_path))
            except Exception as e:
                self.logger.error(f"扫描加载函数文件出错: {str(e)}")
        
        for display_name, full_path in sorted(server_load_function_files):
            item = QListWidgetItem(display_name)
            item.setData(Qt.UserRole, full_path) # 存储完整路径供双击使用
            self.server_load_function_list.addItem(item)

        for display_name, full_path in sorted(client_load_function_files):
            item = QListWidgetItem(display_name)
            item.setData(Qt.UserRole, full_path) # 存储完整路径供双击使用
            self.client_load_function_list.addItem(item)

        # if server_load_function_files or client_load_function_files:
            # self.logger.info(f"找到 {len(server_load_function_files)} 个服务端加载函数文件和 {len(client_load_function_files)} 个客户端加载函数文件")
        # else:
        #     self.logger.info("未找到加载函数文件")
            
    def show_load_function_context_menu(self, position, is_server=True):
        """显示加载函数文件的右键菜单"""
        # 获取当前选中项
        list_widget = self.server_load_function_list if is_server else self.client_load_function_list
        item_at_pos = list_widget.itemAt(position)
        
        # 创建右键菜单
        context_menu = QMenu(self)
        
        # 添加清除所有加载函数文件选项
        clear_all_action = QAction("清除所有加载函数文件", self)
        clear_all_action.triggered.connect(lambda: self.clear_all_load_function_files(is_server))
        context_menu.addAction(clear_all_action)
        
        # 如果在某个项目上点击右键，添加特定文件的操作
        if item_at_pos:
            # 获取文件路径
            file_path = item_at_pos.data(Qt.UserRole)
            if file_path and file_path.endswith('.txt'):
                # 查看文件内容选项
                view_action = QAction("查看文件内容", self)
                view_action.triggered.connect(lambda: self.show_load_function_file_content(item_at_pos))
                context_menu.addAction(view_action)
                
                # 打开文件位置
                location_action = QAction("打开文件位置", self)
                location_action.triggered.connect(lambda: self._open_folder(os.path.dirname(file_path)))
                context_menu.addAction(location_action)
        
        # 在光标位置显示菜单
        context_menu.exec_(QCursor.pos())
        
    def clear_all_load_function_files(self, is_server=True):
        """清除所有加载函数文件"""
        # 获取文件列表
        list_widget = self.server_load_function_list if is_server else self.client_load_function_list
        side = "服务端" if is_server else "客户端"
        
        # 检查列表是否为空
        if list_widget.count() == 0:
            message = f"没有{side}加载函数文件可删除"
            self.logger.info(message)
            self.statusBar().showMessage(message, 3000)
            return
        
        # 确认对话框
        reply = QMessageBox.question(self, f"清除{side}加载函数文件", 
                                   f"确定要删除所有{side}加载函数文件吗？此操作不可撤销。",
                                   QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        
        if reply != QMessageBox.Yes:
            return
        
        deleted_count = 0
        failed_count = 0
        
        # 删除所有文件
        for i in range(list_widget.count()):
            item = list_widget.item(i)
            file_path = item.data(Qt.UserRole)
            
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    deleted_count += 1
            except Exception as e:
                self.logger.error(f"删除文件失败: {file_path}, 错误: {str(e)}")
                failed_count += 1
        
        # 刷新列表
        self.refresh_load_function_files()
        
        # 显示结果
        if deleted_count > 0:
            self.logger.info(f"已删除 {deleted_count} 个{side}加载函数文件")
        if failed_count > 0:
            self.logger.warning(f"有 {failed_count} 个{side}加载函数文件删除失败")
        
        # 更新状态栏
        self.statusBar().showMessage(f"已删除 {deleted_count} 个{side}加载函数文件", 3000)

    def show_load_function_file_content(self, item):
        """在日志中显示加载函数文件的内容"""
        file_full_path = item.data(Qt.UserRole)
        file_display_name = item.text() # 显示的路径 Excel/Sheet/File.txt

        if os.path.exists(file_full_path):
            try:
                with open(file_full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                self.logger.info(f"加载函数文件 {file_display_name} 内容:")
                
                # 使用样式管理器获取加载函数文件内容的样式
                style = self.style_manager.get_struct_content_style(content_type="load_function")
                bg_color = style["bg_color"]
                border_color = style["border_color"]
                text_color = style["text_color"]
                
                import html # 添加导入
                escaped_content = html.escape(content) # 转义内容

                divider = "-" * 40
                highlighted_content = f'<div style="margin: 5px 0;">{divider}</div>'
                highlighted_content += f'<pre style="background-color: {bg_color}; padding: 8px; margin: 0; border: 1px solid {border_color}; white-space: pre-wrap; font-family: Consolas, monospace; color: {text_color};">{escaped_content}</pre>' # 使用转义后的内容
                highlighted_content += f'<div style="margin: 5px 0;">{divider}</div>'
                
                self.log_text.append(highlighted_content)
                self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())
            except Exception as e:
                self.logger.error(f"读取加载函数文件 {file_display_name} 失败: {str(e)}")
        else:
            self.logger.error(f"加载函数文件不存在: {file_full_path}")

    def load_icon(self, path):
        """加载图标，兼容打包后的路径"""
        # 首先尝试直接加载 (普通运行模式)
        if os.path.exists(path):
            return QIcon(path)
        
        # 如果直接加载失败，尝试从打包目录加载 (PyInstaller模式)
        base_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
        pkg_path = os.path.join(base_dir, path)
        if os.path.exists(pkg_path):
            return QIcon(pkg_path)
        
        # 如果仍然失败，尝试从_MEIPASS加载 (PyInstaller单文件模式)
        if hasattr(sys, '_MEIPASS'):
            pkg_path = os.path.join(sys._MEIPASS, path)
            if os.path.exists(pkg_path):
                return QIcon(pkg_path)
        
        # 记录错误并返回空图标
        self.logger.warning(f"无法加载图标: {path}")
        return QIcon()

    def get_resource_path(self, path):
        """获取资源路径，兼容打包后的路径"""
        # 首先尝试直接访问 (普通运行模式)
        if os.path.exists(path):
            return path.replace('\\\\', '/')
        
        # 如果直接访问失败，尝试从打包目录访问 (PyInstaller模式)
        base_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
        pkg_path = os.path.join(base_dir, path)
        if os.path.exists(pkg_path):
            return pkg_path.replace('\\\\', '/')
        
        # 如果仍然失败，尝试从_MEIPASS访问 (PyInstaller单文件模式)
        if hasattr(sys, '_MEIPASS'):
            pkg_path = os.path.join(sys._MEIPASS, path)
            if os.path.exists(pkg_path):
                return pkg_path.replace('\\\\', '/')
        
        # 记录错误并返回原始路径
        self.logger.warning(f"无法访问资源: {path}")
        return path.replace('\\\\', '/')

    def _open_folder(self, path):
        """使用系统默认文件管理器打开文件夹"""
        try:
            # 规范化路径
            abs_path = os.path.abspath(path)
            self.logger.info(f"尝试打开文件夹: {abs_path}")

            # 检查路径是否存在
            if not os.path.exists(abs_path):
                self.logger.error(f"路径不存在: {abs_path}")
                return

            # 检查是否是目录
            if not os.path.isdir(abs_path):
                self.logger.error(f"路径不是目录: {abs_path}")
                return

            import subprocess
            if os.name == 'nt':  # Windows
                # 直接使用os.startfile，这是Windows上最可靠的方法
                os.startfile(abs_path)
                self.logger.info(f"已打开文件夹: {abs_path}")
            elif os.name == 'posix':  # macOS, Linux
                if sys.platform == 'darwin':  # macOS
                    subprocess.Popen(['open', abs_path])
                else:  # Linux
                    subprocess.Popen(['xdg-open', abs_path])
                self.logger.info(f"已打开文件夹: {abs_path}")
        except Exception as e:
            self.logger.error(f"打开文件夹失败: {str(e)}")
            # 尝试使用备选方法
            try:
                if os.name == 'nt':
                    os.startfile(path)
                    self.logger.info(f"使用备选方法成功打开文件夹: {path}")
            except Exception as e2:
                self.logger.error(f"备选方法也失败: {str(e2)}")
    
    def _process_files(self, files):
        force_gen = self.force_gen_checkbox.isChecked()
        config_path = self.path_edit.text()

        # 检查是否启用了SData自定义导出位置
        export_sdata_custom = self.export_sdata_custom_checkbox.isChecked()

        # 如果启用了SData自定义导出位置，检查是否有空路径
        if export_sdata_custom:
            # 先刷新SData项目列表以确保数据是最新的
            self.load_sdata_items()
            empty_paths = self.check_empty_sdata_paths()
            if empty_paths:
                error_msg = "勾选了'导出SData到设置位置'，但以下SData文件的导出路径为空：\n\n" + "\n".join(empty_paths)
                error_msg += "\n\n请先在'SData导出位置设置'页签中为所有SData文件设置导出路径，或取消勾选'导出SData到设置位置'选项。"
                QMessageBox.warning(self, "SData路径设置不完整", error_msg)
                return

        # 确定输出路径
        output_path = None
        if self.output_path_edit.text():
            output_path = self.output_path_edit.text()
            
            # 检查输出路径是否为可能无权限的根目录
            import re
            if re.match(r'^[A-Za-z]:[\/\\]?$', output_path):
                self.logger.warning(f"检测到输出根目录路径，可能没有写入权限: {output_path}")
                # 使用默认路径
                default_output_path = os.path.join(os.getcwd(), "Output")
                default_output_path = self.normalize_path(default_output_path)
                self.logger.info(f"切换到默认输出路径: {default_output_path}")
                output_path = default_output_path
                self.output_path_edit.setText(output_path)
                self.save_settings()
            
            if not os.path.exists(output_path):
                try:
                    os.makedirs(output_path, exist_ok=True)
                    self.logger.info(f"已创建输出路径: {output_path}")
                except PermissionError as e:
                    self.logger.error(f"无法创建输出路径，权限被拒绝: {str(e)}")
                    # 使用默认路径
                    default_output_path = os.path.join(os.getcwd(), "Output")
                    default_output_path = self.normalize_path(default_output_path)
                    self.logger.info(f"切换到默认输出路径: {default_output_path}")
                    output_path = default_output_path
                    self.output_path_edit.setText(output_path)
                    self.save_settings()
                    
                    # 尝试创建默认输出路径
                    if not os.path.exists(output_path):
                        try:
                            os.makedirs(output_path, exist_ok=True)
                            self.logger.info(f"已创建默认输出路径: {output_path}")
                        except Exception as e2:
                            self.logger.error(f"创建默认输出路径失败: {str(e2)}")
        
        try:
            # 添加计数器
            success_count = 0
            skipped_count = 0
            failed_count = 0
            
            # 在状态栏显示处理进度
            self.statusBar().showMessage(f"正在处理文件 (0/{len(files)})")
            
            # 处理文件
            has_processed = False
            
            for i, file in enumerate(files):
                # 更新状态栏进度 - 使用队列或者信号保证在主线程中更新UI
                QApplication.processEvents()  # 确保UI响应
                self.statusBar().showMessage(f"正在处理文件 ({i+1}/{len(files)}): {file}")
                    
                file_path = os.path.join(config_path, file)        
                
                # 在调用处理前检查是否会跳过
                will_skip = False
                if not force_gen and file_path in self.excel_processor.timestamp_cache:
                    file_mtime = os.path.getmtime(file_path)
                    if file_mtime <= self.excel_processor.timestamp_cache[file_path]:
                        will_skip = True
                
                # 处理文件
                sdata_paths_to_use = self.sdata_paths if export_sdata_custom else None
                result = self.excel_processor.process_file(file_path, force_gen, output_path, sdata_paths_to_use)
                
                # 防止UI在处理大文件时卡死
                QApplication.processEvents()
                
                # 统计结果
                if result:
                    if will_skip:
                        skipped_count += 1
                    else:
                        success_count += 1
                        has_processed = True
                else:
                    failed_count += 1

            
            # 更新导出结果提示
            result_message = f"导出完成! 成功: {success_count}, 跳过: {skipped_count}, 失败: {failed_count}"
            self.logger.info(result_message)
            
            # 在状态栏显示结果摘要 - 确保在主线程中更新UI
            QApplication.processEvents()
            if failed_count > 0:
                self.statusBar().showMessage(f"导出完成，有 {failed_count} 个文件失败", 5000)
            elif success_count == 0 and skipped_count > 0:
                self.statusBar().showMessage("导出完成，所有文件均无修改", 5000)
            else:
                self.statusBar().showMessage(f"导出成功完成 ({success_count} 个文件)", 5000)
            
            # 如果有文件被处理（非跳过），则刷新结构体文件列表和SData文件列表
            if has_processed:
                # 确保UI更新在主线程中进行
                QApplication.processEvents()
                # 刷新结构体文件列表，但不自动切换标签页
                self.refresh_struct_files(auto_switch=False)
                # 同时刷新SData文件列表
                self.refresh_sdata_files()
                self.refresh_load_function_files() # 新增：刷新加载函数文件列表
            
            # 更新日志文件计数
            self.update_log_files_count()
            
        except Exception as e:
            self.logger.error(f"处理文件时出错: {str(e)}") 
            # 确保错误消息在主线程中显示
            QApplication.processEvents()
            self.statusBar().showMessage(f"处理出错: {str(e)}", 5000)
    
    def show_struct_content(self, item):
        """在日志中显示结构体文件内容
        
        Args:
            item: 可以是QListWidgetItem对象或者文件路径字符串
        """
        # 检查item类型，获取完整的相对路径和文件名
        if isinstance(item, str):
            # 如果是字符串，直接作为文件路径使用
            file_path = item
            file_name = os.path.basename(item)
        else:
            # 如果是QListWidgetItem对象
            file_path = item.data(Qt.UserRole)
            file_name = item.text()
        
        base_path = self.output_path_edit.text() or self.path_edit.text()
        
        # 如果file_path已经是绝对路径，直接使用；否则拼接base_path
        if os.path.isabs(file_path):
            full_path = file_path
        else:
            full_path = os.path.join(base_path, file_path)
        
        if os.path.exists(full_path):
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 更简单直接的处理方法
                lines = content.split('\n')
                processed_lines = []
                i = 0
                
                while i < len(lines):
                    line = lines[i]
                    
                    # 检查是否是字段定义行（带注释）
                    field_match = re.match(r'(\s*\w+\s+\w+(?:\[\d+\])?(?:\[\d+\])?;\s*)//(.*)', line)
                    
                    if field_match:
                        # 提取字段定义和注释部分
                        field_def = field_match.group(1)
                        comment = field_match.group(2).strip()
                        
                        # 检查下一行是否不是字段定义也不是结构体结束
                        next_i = i + 1
                        while next_i < len(lines):
                            next_line = lines[next_i]
                            # 如果下一行是字段定义或结构体结束，停止
                            if re.match(r'\s*\w+\s+\w+(?:\[\d+\])?(?:\[\d+\])?;', next_line) or re.match(r'\s*};', next_line):
                                break
                            # 否则，将下一行视为注释的一部分
                            if next_line.strip():  # 只添加非空行
                                comment += " " + next_line.strip()
                            next_i += 1
                        
                        # 写入处理后的行
                        processed_lines.append(f"{field_def}// {comment}")
                        # 跳过已处理的行
                        i = next_i
                    # 处理结构体结束行
                    elif re.match(r'\s*};', line):
                        # 确保结构体结束符单独一行
                        processed_lines.append(line)
                        i += 1
                    # 处理可能是注释一部分的空行
                    elif i > 0 and not line.strip() and re.match(r'\s*\w+\s+\w+(?:\[\d+\])?(?:\[\d+\])?;', lines[i-1]):
                        # 跳过字段定义后的空行，这些行可能是注释的一部分
                        i += 1
                    else:
                        # 其他行保持不变
                        processed_lines.append(line)
                        i += 1
                
                # 确保每一行都有适当的换行符，并处理最后的结构体结束符
                final_content = []
                for i, line in enumerate(processed_lines):
                    # 如果当前行是空行，且下一行是结构体结束符，则跳过当前行
                    if not line.strip() and i+1 < len(processed_lines) and re.match(r'\s*};', processed_lines[i+1]):
                        continue
                    
                    # 确保每行末尾只有一个换行符
                    cleaned_line = line.rstrip('\n')
                    
                    # 对于结构体结束行，不添加额外的换行
                    if re.match(r'\s*};', cleaned_line):
                        final_content.append(cleaned_line)
                    else:
                        final_content.append(cleaned_line + '\n')
                
                # 直接连接所有行，不再添加额外的换行符
                processed_content = ''.join(final_content)
                
                # 在日志中显示结构体内容
                self.logger.info(f"结构体文件 {file_name} 内容:")
                
                # 使用样式管理器获取结构体内容的样式
                style = self.style_manager.get_struct_content_style()
                bg_color = style["bg_color"]
                border_color = style["border_color"]
                text_color = style["text_color"]
                
                # 确保内容显示时保留格式
                # 内容前后添加分隔线，更容易区分
                divider = "-" * 40
                highlighted_content = f'<div style="margin: 5px 0;">{divider}</div>'
                highlighted_content += f'<pre style="background-color: {bg_color}; padding: 8px; margin: 0; border: 1px solid {border_color}; white-space: pre-wrap; font-family: Consolas, monospace; color: {text_color};">{processed_content}</pre>'
                highlighted_content += f'<div style="margin: 5px 0;">{divider}</div>'
                
                self.log_text.append(highlighted_content)
                
                # 滚动到日志底部确保用户能看到内容
                self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())
            except Exception as e:
                self.logger.error(f"读取结构体文件失败: {str(e)}")
        else:
            self.logger.error(f"文件不存在: {full_path}")

    def update_log_files_count(self):
        """更新日志文件计数"""
        log_files = self.logger.get_log_files()
        self.log_files_count_label.setText(f"日志文件: {len(log_files)}个")
    
    def clear_log_files(self):
        """清除所有日志文件"""
        log_files = self.logger.get_log_files()
        if not log_files:
            self.statusBar().showMessage("没有日志文件需要清除", 2000)
            return
        
        # 获取当前正在使用的日志文件名
        current_log = self.logger.get_current_log_filename()
        
        # 计算可删除的文件数量（排除当前日志文件）
        deletable_files = [f for f in log_files if f != current_log]
        skipped_current = len(log_files) - len(deletable_files)
        
        # 构建确认消息
        confirm_message = f"确定要删除{len(deletable_files)}个日志文件吗？"
        if skipped_current > 0:
            confirm_message += f"\n\n注意：当前正在使用的日志文件 {current_log} 将被保留。"
            
        reply = QMessageBox.question(self, "确认删除", confirm_message, 
                                  QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            success_count = 0
            fail_count = 0
            for file_name in log_files:
                # 跳过当前正在使用的日志文件
                if file_name == current_log:
                    continue
                    
                try:
                    os.remove(os.path.join(self.logger.logs_dir, file_name))
                    success_count += 1
                except Exception as e:
                    self.logger.error(f"删除日志文件失败: {file_name}, 错误: {str(e)}")
                    fail_count += 1
            
            self.update_log_files_count()
            
            # 构建状态栏消息
            status_message = f"已删除{success_count}个日志文件"
            if skipped_current > 0:
                status_message += f"，保留了当前正在使用的日志文件"
            if fail_count > 0:
                status_message += f"，{fail_count}个文件删除失败"
                
            self.statusBar().showMessage(status_message, 3000)
    
    def clear_log(self):
        """清除日志输出文本框内容"""
        self.log_text.clear()
        self.statusBar().showMessage("日志已清除", 2000)
        # 更新日志文件计数
        self.update_log_files_count()
    
    def open_excel_file(self, item):
        """双击打开Excel文件"""
        file_name = item.text()
        file_path = os.path.join(self.path_edit.text(), file_name)
        
        if os.path.exists(file_path):
            try:
                # self.logger.info(f"正在打开Excel文件: {file_name}")
                if os.name == 'nt':  # Windows
                    os.startfile(file_path)
                else:  # macOS 或 Linux
                    import subprocess
                    subprocess.Popen(['open' if sys.platform == 'darwin' else 'xdg-open', file_path])
            except Exception as e:
                self.logger.error(f"打开Excel文件失败: {str(e)}")
        else:
            self.logger.error(f"文件不存在: {file_path}")
    
    def refresh_struct_files(self, auto_switch=True):
        """刷新结构体文件列表
        
        Args:
            auto_switch: 是否自动切换到结构体标签页
        """
        self.server_struct_list.clear()
        self.client_struct_list.clear()
        base_path = self.output_path_edit.text() or self.path_edit.text()
        server_struct_files = []
        client_struct_files = []
        
        # 记录扫描开始
        # self.logger.info(f"正在扫描结构体文件，路径: {base_path}")
        
        # 扫描配置目录下的所有Excel文件目录
        if os.path.exists(base_path):
            try:
                for item_name in os.listdir(base_path): # Changed item to item_name to avoid conflict with QListWidgetItem
                    item_path = os.path.join(base_path, item_name)
                    if os.path.isdir(item_path):
                        # 查找该目录下的所有结构体文件
                        for root, dirs, files in os.walk(item_path):
                            for file in files:
                                # 同时支持Client和Server结构体文件
                                if file.startswith('GameData') and file.endswith('.txt'):
                                    rel_path = os.path.relpath(os.path.join(root, file), base_path)
                                    if 'Server' in file:
                                        server_struct_files.append(rel_path)
                                    elif 'Client' in file:
                                        client_struct_files.append(rel_path)
            except Exception as e:
                self.logger.error(f"扫描结构体文件出错: {str(e)}")
        
        # 更新服务端结构体文件列表
        for struct_file in sorted(server_struct_files):
            filename = os.path.basename(struct_file)
            list_item = QListWidgetItem(filename) 
            list_item.setData(Qt.UserRole, struct_file) 
            self.server_struct_list.addItem(list_item) 
        
        # 更新客户端结构体文件列表
        for struct_file in sorted(client_struct_files):
            filename = os.path.basename(struct_file)
            list_item = QListWidgetItem(filename)
            list_item.setData(Qt.UserRole, struct_file)
            self.client_struct_list.addItem(list_item)
        
        # 更新状态栏中的结构体文件计数
        struct_count = len(server_struct_files) + len(client_struct_files)
        self.struct_count_label.setText(f"结构体文件: {struct_count}")
        
        if server_struct_files or client_struct_files:
            if auto_switch and self.tab_widget.currentIndex() == 0: # 保持原有逻辑，如果需要，可以调整
                self.tab_widget.setCurrentIndex(1)
    
    def show_struct_context_menu(self, position, is_server=True):
        """显示结构体文件的右键菜单"""
        # 获取当前选中项
        list_widget = self.server_struct_list if is_server else self.client_struct_list
        item_at_pos = list_widget.itemAt(position)
        
        # 创建右键菜单
        context_menu = QMenu(self)
        
        # 添加清除所有结构体文件选项
        clear_all_action = QAction("清除所有结构体文件", self)
        clear_all_action.triggered.connect(lambda: self.clear_all_struct_files(is_server))
        context_menu.addAction(clear_all_action)
        
        # 如果在某个项目上点击右键，添加查看内容选项
        if item_at_pos:
            # 获取文件路径
            rel_path = item_at_pos.data(Qt.UserRole)
            base_path = self.output_path_edit.text() or self.path_edit.text()
            file_path = os.path.join(base_path, rel_path)
            
            # 查看文件内容选项
            view_action = QAction("查看文件内容", self)
            view_action.triggered.connect(lambda: self.show_struct_content(file_path))
            context_menu.addAction(view_action)
            
            # 打开文件位置选项
            location_action = QAction("打开文件位置", self)
            location_action.triggered.connect(lambda: self._open_folder(os.path.dirname(file_path)))
            context_menu.addAction(location_action)
        
        # 在光标位置显示菜单
        context_menu.exec_(QCursor.pos())
    
    def clear_all_struct_files(self, is_server=True):
        """清除所有结构体文件"""
        # 获取文件列表
        list_widget = self.server_struct_list if is_server else self.client_struct_list
        side = "服务端" if is_server else "客户端"
        
        # 检查列表是否为空
        if list_widget.count() == 0:
            message = f"没有{side}结构体文件可删除"
            self.logger.info(message)
            self.statusBar().showMessage(message, 3000)
            return
        
        # 确认对话框
        reply = QMessageBox.question(self, f"清除{side}结构体文件", 
                                   f"确定要删除所有{side}结构体文件吗？此操作不可撤销。",
                                   QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        
        if reply != QMessageBox.Yes:
            return
        
        base_path = self.output_path_edit.text() or self.path_edit.text()
        deleted_count = 0
        failed_count = 0
        
        # 删除所有文件
        for i in range(list_widget.count()):
            item = list_widget.item(i)
            rel_path = item.data(Qt.UserRole)
            file_path = os.path.join(base_path, rel_path)
            
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    deleted_count += 1
            except Exception as e:
                self.logger.error(f"删除文件失败: {file_path}, 错误: {str(e)}")
                failed_count += 1
        
        # 刷新列表
        self.refresh_struct_files(auto_switch=False)
        
        # 显示结果
        if deleted_count > 0:
            self.logger.info(f"已删除 {deleted_count} 个{side}结构体文件")
        if failed_count > 0:
            self.logger.warning(f"有 {failed_count} 个{side}结构体文件删除失败")
        
        # 更新状态栏
        self.statusBar().showMessage(f"已删除 {deleted_count} 个{side}结构体文件", 3000)
    
    def refresh_sdata_files(self):
        """刷新服务端和客户端SData文件列表"""
        self.server_sdata_list.clear()
        self.client_sdata_list.clear()
        base_path = self.output_path_edit.text() or self.path_edit.text()
        
        # 扫描目录
        if os.path.exists(base_path):
            try:
                for excel_dir in os.listdir(base_path):
                    excel_path = os.path.join(base_path, excel_dir)
                    if os.path.isdir(excel_path):
                        # 扫描表格目录
                        for sheet_name in os.listdir(excel_path):
                            sheet_path = os.path.join(excel_path, sheet_name)
                            if os.path.isdir(sheet_path):
                                # 检查Server目录
                                server_dir_path = os.path.join(sheet_path, "Server") # Renamed server_path to server_dir_path
                                if os.path.exists(server_dir_path):
                                    # 查找该目录下的所有SData文件
                                    for file in os.listdir(server_dir_path):
                                        if file.endswith('.SData'):
                                            full_path = os.path.join(server_dir_path, file)
                                            # 创建列表项，仅显示文件名
                                            display_name = os.path.basename(full_path) 
                                            list_item = QListWidgetItem(display_name) 
                                            list_item.setData(Qt.UserRole, full_path) 
                                            self.server_sdata_list.addItem(list_item) 
                                
                                # 检查Client目录
                                client_dir_path = os.path.join(sheet_path, "Client") 
                                if os.path.exists(client_dir_path):
                                    # 查找该目录下的所有SData文件
                                    for file in os.listdir(client_dir_path):
                                        if file.endswith('.SData'):
                                            full_path = os.path.join(client_dir_path, file)
                                            # 创建列表项，仅显示文件名
                                            display_name = os.path.basename(full_path)
                                            list_item = QListWidgetItem(display_name) 
                                            list_item.setData(Qt.UserRole, full_path) 
                                            self.client_sdata_list.addItem(list_item) 
            except Exception as e:
                self.logger.error(f"扫描SData文件出错: {str(e)}")
        
        # 恢复使用交替行颜色，与其他列表保持一致
        self.server_sdata_list.setAlternatingRowColors(True)
        self.client_sdata_list.setAlternatingRowColors(True)
        
        # 显示扫描结果
        server_count = self.server_sdata_list.count()
        client_count = self.client_sdata_list.count()
        total_count = server_count + client_count
        
        # 更新状态栏中的SData文件计数
        self.sdata_count_label.setText(f"SData文件: {total_count}")
        
        # if total_count > 0:
        #     self.logger.info(f"找到 {total_count} 个SData文件 (服务端: {server_count}, 客户端: {client_count})")
        # else:
        #     self.logger.info("未找到SData文件")

    def show_sdata_context_menu(self, position, is_server=True):
        """显示SData文件的右键菜单"""
        # 获取当前选中项
        list_widget = self.server_sdata_list if is_server else self.client_sdata_list
        item_at_pos = list_widget.itemAt(position) # Changed item to item_at_pos
        
        # 创建右键菜单
        context_menu = QMenu(self)
        
        # 添加清除所有SData文件选项
        clear_all_action = QAction("清除所有SData文件", self)
        clear_all_action.triggered.connect(lambda: self.clear_all_sdata_files(is_server))
        context_menu.addAction(clear_all_action)
        
        # 如果在某个项目上点击右键，添加特定文件的操作
        if item_at_pos:
            # 获取文件路径
            file_path = item_at_pos.data(Qt.UserRole) # Changed item to item_at_pos
            if file_path and file_path.endswith('.SData'):
                # 解析SData文件操作
                parse_action = QAction("解析SData文件", self)
                parse_action.triggered.connect(lambda: self.parse_sdata_file(file_path))
                context_menu.addAction(parse_action)
                
                # 复制文件操作
                copy_action = QAction("复制选中文件", self) # Changed text to reflect multi-selection
                copy_action.triggered.connect(lambda: self.copy_selected_sdata_files(list_widget))
                context_menu.addAction(copy_action)
                
                # 打开文件位置
                location_action = QAction("打开文件位置", self)
                location_action.triggered.connect(lambda: self._open_folder(os.path.dirname(file_path)))
                context_menu.addAction(location_action)
        
        # 在光标位置显示菜单
        context_menu.exec_(QCursor.pos())
        
    def clear_all_sdata_files(self, is_server=True):
        """清除所有SData文件"""
        # 获取文件列表
        list_widget = self.server_sdata_list if is_server else self.client_sdata_list
        side = "服务端" if is_server else "客户端"
        
        # 检查列表是否为空
        if list_widget.count() == 0:
            message = f"没有{side}SData文件可删除"
            self.logger.info(message)
            self.statusBar().showMessage(message, 3000)
            return
        
        # 确认对话框
        reply = QMessageBox.question(self, f"清除{side}SData文件", 
                                   f"确定要删除所有{side}SData文件吗？此操作不可撤销。",
                                   QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        
        if reply != QMessageBox.Yes:
            return
        
        deleted_count = 0
        failed_count = 0
        
        # 删除所有文件
        for i in range(list_widget.count()):
            item = list_widget.item(i)
            file_path = item.data(Qt.UserRole)
            
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    deleted_count += 1
            except Exception as e:
                self.logger.error(f"删除文件失败: {file_path}, 错误: {str(e)}")
                failed_count += 1
        
        # 刷新列表
        self.refresh_sdata_files()
        
        # 显示结果
        if deleted_count > 0:
            self.logger.info(f"已删除 {deleted_count} 个{side}SData文件")
        if failed_count > 0:
            self.logger.warning(f"有 {failed_count} 个{side}SData文件删除失败")
        
        # 更新状态栏
        self.statusBar().showMessage(f"已删除 {deleted_count} 个{side}SData文件", 3000)

    def copy_selected_sdata_files(self, list_widget):
        """复制list_widget中所有选中的SData文件"""
        selected_items = list_widget.selectedItems()
        if not selected_items:
            self.logger.warning("没有选中要复制的SData文件")
            return

        files_to_copy = []
        for item in selected_items:
            file_path = item.data(Qt.UserRole)
            if file_path and file_path.endswith('.SData'):
                files_to_copy.append(file_path)
        
        if files_to_copy:
            self.copy_sdata_file(files_to_copy)
        else:
            self.logger.warning("选中的项目中没有有效的SData文件路径")

    def parse_sdata_file(self, file_path):
        """解析SData文件并在独立窗口中显示解析结果"""
        try:
            # 创建独立的SData查看器窗口
            # self.logger.info(f"正在解析SData文件: {os.path.basename(file_path)}")
            viewer = SDataViewer(file_path, self)
            viewer.show()
            
        except Exception as e:
            self.logger.error(f"解析SData文件失败: {str(e)}")
            
            # 如果加载查看器失败，尝试在日志区域显示
            try:
                # 实例化SData解析器
                parser = SDataParser()
                
                # 清空日志显示区域
                self.log_text.clear()
                self.reset_log_text_colors()
                
                # 解析文件
                parsed_text = parser.parse_sdata_file(file_path)
                
                # 使用样式管理器获取SData文件解析结果的样式
                style = self.style_manager.get_struct_content_style(content_type="sdata")
                bg_color = style["bg_color"]
                border_color = style["border_color"]
                text_color = style["text_color"]
                
                # 格式化显示解析结果，带有美观的样式
                divider = "-" * 40
                highlighted_content = f'<div style="margin: 5px 0;">{divider}</div>'
                highlighted_content += f'<pre style="background-color: {bg_color}; padding: 8px; margin: 0; border: 1px solid {border_color}; white-space: pre-wrap; font-family: Consolas, monospace; color: {text_color};">{parsed_text}</pre>'
                highlighted_content += f'<div style="margin: 5px 0;">{divider}</div>'
                
                # 显示解析结果
                self.log_text.append(highlighted_content)
                
                # 滚动到顶部
                self.log_text.verticalScrollBar().setValue(0)
                
                self.logger.info(f"解析完成 (备用方式): {os.path.basename(file_path)}")
            except Exception as e2:
                self.logger.error(f"备用解析方式也失败: {str(e2)}")
    
    def showDropIndicator(self, show):
        """显示或隐藏拖放提示区域"""
        # 如果尚未创建拖放提示区域，创建它
        if not hasattr(self, 'drop_indicator'):
            self.drop_indicator = QFrame(self)
            self.drop_indicator.setFrameShape(QFrame.StyledPanel)
            self.drop_indicator.setFrameShadow(QFrame.Raised)
            self.drop_indicator.setStyleSheet("""
                QFrame {
                    background-color: rgba(65, 105, 225, 0.2);
                    border: 2px dashed #4169E1;
                    border-radius: 8px;
                }
            """)
            self.drop_indicator.setGeometry(self.rect().adjusted(50, 50, -50, -50))
            
            # 添加提示标签
            label = QLabel("拖放Excel文件到这里导入", self.drop_indicator)
            label.setAlignment(Qt.AlignCenter)
            label.setStyleSheet("""
                QLabel {
                    color: #4169E1;
                    font-size: 16px;
                    font-weight: bold;
                }
            """)
            layout = QVBoxLayout(self.drop_indicator)
            layout.addWidget(label)
            
        # 显示或隐藏
        self.drop_indicator.setVisible(show)
        if show:
            # 确保拖放区域大小正确
            self.drop_indicator.setGeometry(self.rect().adjusted(50, 50, -50, -50))

    def keyPressEvent(self, event):
        """处理键盘事件"""
        # 处理通用快捷键
        if event.key() == Qt.Key_F1:
            # F1显示帮助
            self.show_shortcuts_help()
            return
        elif event.key() == Qt.Key_Delete:
            # Delete清除日志
            self.clear_log()
            return
        elif event.key() == Qt.Key_F5:
            # F5刷新当前视图
            self.refresh_current_view()
            return
        
        # 处理Ctrl组合键
        if event.modifiers() & Qt.ControlModifier:
            if event.key() == Qt.Key_O:
                # Ctrl+O浏览配置文件夹
                self.browse_folder()
                return
            elif event.key() == Qt.Key_S:
                if event.modifiers() & Qt.ShiftModifier:
                    # Ctrl+Shift+S导出所有
                    self.export_all()
                else:
                    # Ctrl+S导出选中
                    self.export_selected()
                return
            elif event.key() == Qt.Key_E:
                # Ctrl+E打开选中的Excel文件
                if self.tab_widget.currentIndex() == 0:
                    selected_items = self.file_list.selectedItems()
                    if selected_items:
                        self.open_excel_file(selected_items[0])
                return
            elif event.key() == Qt.Key_F:
                # Ctrl+F切换强制导出选项
                self.force_gen_checkbox.setChecked(not self.force_gen_checkbox.isChecked())
                return
            elif event.key() == Qt.Key_1:
                # Ctrl+1切换到Excel列表
                self.tab_widget.setCurrentIndex(0)
                return
            elif event.key() == Qt.Key_2:
                # Ctrl+2切换到导出结构
                self.tab_widget.setCurrentIndex(1)
                return
            elif event.key() == Qt.Key_3:
                # Ctrl+3切换到导出SData
                self.tab_widget.setCurrentIndex(2)
                return
            elif event.key() == Qt.Key_4:
                # Ctrl+4切换到加载函数文件
                self.tab_widget.setCurrentIndex(3)
                return
            elif event.key() == Qt.Key_R:
                # Ctrl+R刷新当前选项卡
                self.refresh_current_view()
                return
            elif event.key() == Qt.Key_C:
                # Ctrl+C复制选中的SData文件
                if self.tab_widget.currentIndex() == 2:
                    # 根据当前子标签页确定使用哪个列表
                    is_server = self.sdata_subtabs.currentIndex() == 0
                    list_widget = self.server_sdata_list if is_server else self.client_sdata_list
                    selected_items = list_widget.selectedItems()
                    
                    if selected_items:
                        files_to_copy = []
                        for item in selected_items:
                            file_path = item.data(Qt.UserRole)
                            if file_path and file_path.endswith('.SData'):
                                files_to_copy.append(file_path)
                        if files_to_copy:
                            self.copy_sdata_file(files_to_copy)
                return
            elif event.key() == Qt.Key_F:
                # Ctrl+F 聚焦搜索框
                self.focus_search()
                return
        
        # 当前标签是SData文件列表时，对选中的SData文件按回车键进行解析
        if self.tab_widget.currentIndex() == 2 and (event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter):
            # 根据当前子标签页确定使用哪个列表
            is_server = self.sdata_subtabs.currentIndex() == 0
            list_widget = self.server_sdata_list if is_server else self.client_sdata_list
            selected_items = list_widget.selectedItems()
            
            if selected_items:
                file_path = selected_items[0].data(Qt.UserRole)
                if file_path and file_path.endswith('.SData'):
                    self.parse_sdata_file(file_path)
                    return
        
        # 处理空格键 - 切换当前项的选中状态
        if event.key() == Qt.Key_Space:
            current_tab = self.tab_widget.currentIndex()
            if current_tab == 0:  # Excel文件列表
                current_item = self.file_list.currentItem()
                if current_item:
                    current_item.setSelected(not current_item.isSelected())
                    status = "已选中" if current_item.isSelected() else "已取消选中"
                    self.show_shortcut_tip(f"{status}Excel文件: {current_item.text()}")
                    return
            elif current_tab == 1:  # 结构体文件列表
                is_server = self.struct_subtabs.currentIndex() == 0
                list_widget = self.server_struct_list if is_server else self.client_struct_list
                current_item = list_widget.currentItem()
                if current_item:
                    current_item.setSelected(not current_item.isSelected())
                    list_type = "服务器" if is_server else "客户端"
                    status = "已选中" if current_item.isSelected() else "已取消选中"
                    self.show_shortcut_tip(f"{status}{list_type}结构体文件: {current_item.text()}")
                    return
            elif current_tab == 2:  # SData文件列表
                is_server = self.sdata_subtabs.currentIndex() == 0
                list_widget = self.server_sdata_list if is_server else self.client_sdata_list
                current_item = list_widget.currentItem()
                if current_item:
                    current_item.setSelected(not current_item.isSelected())
                    list_type = "服务器" if is_server else "客户端"
                    status = "已选中" if current_item.isSelected() else "已取消选中"
                    self.show_shortcut_tip(f"{status}{list_type}SData文件: {current_item.text()}")
                    return
        
        # 正常处理其他键盘事件
        super().keyPressEvent(event)

    def show_shortcuts_help(self):
        """显示快捷键帮助对话框"""
        shortcuts_text = """
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                table {
                    border-collapse: collapse;
                    width: 100%;
                }
                th, td {
                    border: 1px solid #ddd; /* Base border color for light mode */
                    padding: 8px;
                }
                tr:nth-child(even) {
                    background-color: #f2f2f2; /* Base even row color for light mode */
                }
                th {
                    background-color: #4CAF50; /* Base header color */
                    color: white;
                    text-align: left;
                }
                h3 {
                    color: #2196F3; /* Base heading color */
                    border-bottom: 1px solid #2196F3; /* Base heading border */
                    padding-bottom: 5px;
                }
            </style>
        </head>
        <body style="color: #333; background-color: #fff;"> <!-- Default light mode body -->
        <h3>键盘快捷键</h3>
        <table>
            <tr><th>快捷键</th><th>功能</th></tr>
            <tr><td><b>Ctrl+O</b></td><td>浏览配置文件夹</td></tr>
            <tr><td><b>Ctrl+S</b></td><td>导出选中的Excel文件</td></tr>
            <tr><td><b>Ctrl+Shift+S</b></td><td>导出所有Excel文件</td></tr>
            <tr><td><b>Ctrl+R</b></td><td>刷新当前标签页的文件列表</td></tr>
            <tr><td><b>Ctrl+E</b></td><td>打开选中的Excel文件</td></tr>
            <tr><td><b>Ctrl+F</b></td><td>聚焦到搜索框</td></tr>
            <tr><td><b>Ctrl+Shift+F</b></td><td>打开搜索过滤器</td></tr>
            <tr><td><b>Ctrl+C</b></td><td>复制选中的SData文件</td></tr>
                <tr><td><b>Ctrl+1 ~ Ctrl+4</b></td><td>切换到对应标签页 (1:表格, 2:结构, 3:SData, 4:函数文件)</td></tr>
            <tr><td><b>F5</b></td><td>强制刷新当前标签页</td></tr>
            <tr><td><b>F1</b></td><td>显示此帮助</td></tr>
            <tr><td><b>Delete</b></td><td>清除日志</td></tr>
            <tr><td><b>Enter</b></td><td>在SData标签页，解析选中的SData文件</td></tr>
            <tr><td><b>空格键</b></td><td>切换当前项的选中状态</td></tr>
        </table>
        
        <h3>鼠标操作</h3>
        <table>
            <tr><th>操作</th><th>功能</th></tr>
            <tr><td><b>拖放Excel文件</b></td><td>将Excel文件拖入窗口以导入并处理</td></tr>
            <tr><td><b>双击Excel文件</b></td><td>用默认程序打开Excel文件</td></tr>
            <tr><td><b>双击结构体文件</b></td><td>在日志区域显示文件内容</td></tr>
            <tr><td><b>双击SData文件</b></td><td>打开文件所在目录</td></tr>
                <!-- TXT related help removed -->
            <tr><td><b>右键SData文件</b></td><td>显示右键菜单，提供解析、复制等操作</td></tr>
        </table>
        
        <h3>SData解析功能</h3>
        <p>您可以通过以下方式解析SData二进制文件：</p>
        <ol>
            <li>在SData标签页选中文件，按Enter键</li>
            <li>右键点击SData文件，选择"解析SData文件"</li>
        </ol>
        <p>解析结果将显示在日志输出区域。</p>
        
        <h3>提示</h3>
        <p>您可以使用拖放功能直接导入Excel文件，无需手动选择路径。</p>
        <p>导出操作完成后，会自动刷新和切换到对应的导出结果标签页。</p>
        <p>在SData标签页，您可以使用Ctrl+C快速复制选中的SData文件。</p>
        <p>使用Ctrl+F可以快速聚焦到搜索框，支持正则表达式搜索。</p>
        </body>
        </html>
        """
        
        # 创建自定义对话框，使用QDialog和QTextBrowser以更好地支持HTML渲染
        help_dialog = QDialog(self)
        help_dialog.setWindowTitle("快捷键帮助")
        help_dialog.setWindowIcon(self.app_icon)
        
        # 设置对话框布局
        layout = QVBoxLayout(help_dialog)
        
        # 创建文本浏览器来显示HTML内容
        text_browser = QTextBrowser(help_dialog)
        text_browser.setOpenExternalLinks(True)  # 允许打开外部链接
        
        # 根据当前主题选择适当的HTML内容
        if self.current_theme == "dark":
            # 深色主题的HTML内容
            shortcuts_text = """
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    table { border-collapse: collapse; width: 100%; }
                    th, td { border: 1px solid #555; padding: 8px; }
                    tr:nth-child(even) { background-color: #333; }
                    th { background-color: #007ACC; color: white; text-align: left; }
                    h3 { color: #6CB5F3; border-bottom: 1px solid #6CB5F3; padding-bottom: 5px; }
                    body { color: #DDD; background-color: #222; }
                </style>
            </head>
            <body>
            <h3>键盘快捷键</h3>
            <table>
                <tr><th>快捷键</th><th>功能</th></tr>
                <tr><td><b>Ctrl+O</b></td><td>浏览配置文件夹</td></tr>
                <tr><td><b>Ctrl+S</b></td><td>导出选中的Excel文件</td></tr>
                <tr><td><b>Ctrl+Shift+S</b></td><td>导出所有Excel文件</td></tr>
                <tr><td><b>Ctrl+R</b></td><td>刷新当前标签页的文件列表</td></tr>
                <tr><td><b>Ctrl+E</b></td><td>打开选中的Excel文件</td></tr>
                <tr><td><b>Ctrl+F</b></td><td>聚焦到搜索框</td></tr>
                <tr><td><b>Ctrl+Shift+F</b></td><td>打开搜索过滤器</td></tr>
                <tr><td><b>Ctrl+C</b></td><td>复制选中的SData文件</td></tr>
                <tr><td><b>Ctrl+1 ~ Ctrl+4</b></td><td>切换到对应标签页 (1:表格, 2:结构, 3:SData, 4:函数文件)</td></tr>
                <tr><td><b>F5</b></td><td>强制刷新当前标签页</td></tr>
                <tr><td><b>F1</b></td><td>显示此帮助</td></tr>
                <tr><td><b>Delete</b></td><td>清除日志</td></tr>
                <tr><td><b>Enter</b></td><td>在SData标签页，解析选中的SData文件</td></tr>
                <tr><td><b>空格键</b></td><td>切换当前项的选中状态</td></tr>
            </table>
            
            <h3>鼠标操作</h3>
            <table>
                <tr><th>操作</th><th>功能</th></tr>
                <tr><td><b>拖放Excel文件</b></td><td>将Excel文件拖入窗口以导入并处理</td></tr>
                <tr><td><b>双击Excel文件</b></td><td>用默认程序打开Excel文件</td></tr>
                <tr><td><b>双击结构体文件</b></td><td>在日志区域显示文件内容</td></tr>
                <tr><td><b>双击SData文件</b></td><td>打开文件所在目录</td></tr>
                <tr><td><b>右键SData文件</b></td><td>显示右键菜单，提供解析、复制等操作</td></tr>
            </table>
            
            <h3>SData解析功能</h3>
            <p>您可以通过以下方式解析SData二进制文件：</p>
            <ol>
                <li>在SData标签页选中文件，按Enter键</li>
                <li>右键点击SData文件，选择"解析SData文件"</li>
            </ol>
            <p>解析结果将显示在日志输出区域。</p>
            
            <h3>提示</h3>
            <p>您可以使用拖放功能直接导入Excel文件，无需手动选择路径。</p>
            <p>导出操作完成后，会自动刷新和切换到对应的导出结果标签页。</p>
            <p>在SData标签页，您可以使用Ctrl+C快速复制选中的SData文件。</p>
            <p>使用Ctrl+F可以快速聚焦到搜索框，支持正则表达式搜索。</p>
            </body>
            </html>
            """
            
            # 为深色主题设置文本浏览器的样式
            text_browser.setStyleSheet("background-color: #222; color: #DDD;")
        else:
            # 为浅色主题设置文本浏览器的样式
            text_browser.setStyleSheet("background-color: #fff; color: #333;")
        
        # 设置HTML内容
        text_browser.setHtml(shortcuts_text)
        
        # 添加文本浏览器到布局
        layout.addWidget(text_browser)
        
        # 添加关闭按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok)
        button_box.accepted.connect(help_dialog.accept)
        layout.addWidget(button_box)
        
        # 设置对话框大小
        help_dialog.resize(600, 500)
        
        # 显示对话框
        help_dialog.exec_()

    def normalize_path(self, path):
        """统一路径分隔符为反斜杠（Windows风格）"""
        if not path:
            return path
        # 将所有正斜杠替换为反斜杠
        return path.replace('/', '\\')

    def create_sdata_path_tab(self):
        """创建SData路径设置页签"""
        sdata_path_tab = QWidget()
        layout = QVBoxLayout(sdata_path_tab)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)



        # 创建表格
        self.sdata_path_table = QTableWidget()
        self.sdata_path_table.setColumnCount(3)
        self.sdata_path_table.setHorizontalHeaderLabels(["Excel文件", "SData文件名", "导出目录"])

        # 设置表格属性
        self.sdata_path_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.sdata_path_table.setAlternatingRowColors(True)

        # 隐藏行号（垂直表头）
        self.sdata_path_table.verticalHeader().setVisible(False)

        # 设置列宽
        header = self.sdata_path_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Excel文件列
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # SData文件名列
        header.setSectionResizeMode(2, QHeaderView.Stretch)           # 导出目录列

        layout.addWidget(self.sdata_path_table)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 刷新按钮
        refresh_button = QPushButton("刷新")
        refresh_button.setIcon(QIcon.fromTheme("view-refresh"))
        refresh_button.setToolTip("刷新SData文件列表")
        refresh_button.clicked.connect(self.refresh_sdata_path_table)
        refresh_button.setMaximumWidth(80)

        # 批量设置Client路径按钮
        batch_set_client_button = QPushButton("批量设置Client路径")
        batch_set_client_button.setIcon(QIcon.fromTheme("folder-open"))
        batch_set_client_button.setToolTip("为所有Client.SData文件设置相同的导出路径")
        batch_set_client_button.clicked.connect(self.batch_set_client_sdata_path)
        batch_set_client_button.setMaximumWidth(140)

        # 批量设置Server路径按钮
        batch_set_server_button = QPushButton("批量设置Server路径")
        batch_set_server_button.setIcon(QIcon.fromTheme("folder-open"))
        batch_set_server_button.setToolTip("为所有Server.SData文件设置相同的导出路径")
        batch_set_server_button.clicked.connect(self.batch_set_server_sdata_path)
        batch_set_server_button.setMaximumWidth(140)

        # 清空所有路径按钮
        clear_all_button = QPushButton("清空所有路径")
        clear_all_button.setIcon(QIcon.fromTheme("edit-clear"))
        clear_all_button.setToolTip("清空所有SData文件的导出路径设置")
        clear_all_button.clicked.connect(self.clear_all_sdata_paths)
        clear_all_button.setMaximumWidth(120)

        # 保存设置按钮
        save_button = QPushButton("保存设置")
        save_button.setIcon(QIcon.fromTheme("document-save"))
        save_button.setToolTip("保存当前的SData路径设置")
        save_button.clicked.connect(self.save_sdata_path_settings)
        save_button.setMaximumWidth(100)

        button_layout.addWidget(refresh_button)
        button_layout.addWidget(batch_set_client_button)
        button_layout.addWidget(batch_set_server_button)
        button_layout.addWidget(clear_all_button)
        button_layout.addStretch()
        button_layout.addWidget(save_button)

        layout.addLayout(button_layout)

        # 初始化表格数据
        self.sdata_items = []  # 存储所有SData项目信息

        return sdata_path_tab

    def refresh_sdata_path_table(self):
        """刷新SData路径设置表格"""
        self.load_sdata_items()
        self.populate_sdata_path_table()
        # 重置修改标志
        self.sdata_paths_modified = False

    def load_sdata_items(self):
        """加载所有SData项目"""
        self.sdata_items.clear()

        # 获取当前的Excel文件列表
        excel_files = []
        for i in range(self.file_list.count()):
            excel_files.append(self.file_list.item(i).text())

        # 扫描所有Excel文件，获取页签信息
        for excel_file in excel_files:
            excel_path = os.path.join(self.path_edit.text(), excel_file)
            if not os.path.exists(excel_path):
                continue

            try:
                import openpyxl
                workbook = openpyxl.load_workbook(excel_path, data_only=True)

                for sheet_name in workbook.sheetnames:
                    # 检查页签是否有效（至少5行数据）
                    sheet = workbook[sheet_name]
                    if sheet.max_row < 5:
                        continue

                    # 读取第4行的生成类型信息
                    field_targets = [cell.value for cell in sheet[4]]

                    # 检查是否有server或client字段
                    has_server = any(target in ['server', 'all'] for target in field_targets if target)
                    has_client = any(target in ['client', 'all'] for target in field_targets if target)

                    excel_name = os.path.splitext(excel_file)[0]

                    if has_server:
                        server_key = f"{excel_name}_{sheet_name}_Server"
                        self.sdata_items.append({
                            'excel_file': excel_file,
                            'sheet_name': sheet_name,
                            'type': 'Server.SData',
                            'key': server_key,
                            'path': self.sdata_paths.get(server_key, '')
                        })

                    if has_client:
                        client_key = f"{excel_name}_{sheet_name}_Client"
                        self.sdata_items.append({
                            'excel_file': excel_file,
                            'sheet_name': sheet_name,
                            'type': 'Client.SData',
                            'key': client_key,
                            'path': self.sdata_paths.get(client_key, '')
                        })

                workbook.close()

            except Exception as e:
                self.logger.error(f"读取Excel文件失败 {excel_file}: {str(e)}")

    def populate_sdata_path_table(self):
        """填充表格数据"""
        self.sdata_path_table.setRowCount(len(self.sdata_items))

        for row, item in enumerate(self.sdata_items):
            # Excel文件名
            excel_item = QTableWidgetItem(item['excel_file'])
            excel_item.setFlags(excel_item.flags() & ~Qt.ItemIsEditable)
            self.sdata_path_table.setItem(row, 0, excel_item)

            # SData文件名（页签名+类型）
            sdata_filename = f"{item['sheet_name']}{item['type'].replace('.SData', '.SData')}"
            sdata_item = QTableWidgetItem(sdata_filename)
            sdata_item.setFlags(sdata_item.flags() & ~Qt.ItemIsEditable)
            self.sdata_path_table.setItem(row, 1, sdata_item)

            # 路径（可编辑）
            path_widget = QWidget()
            path_layout = QHBoxLayout(path_widget)
            path_layout.setContentsMargins(2, 2, 2, 2)

            path_edit = QLineEdit(item['path'])
            path_edit.setPlaceholderText("选择SData文件导出目录...")
            path_edit.textChanged.connect(lambda text, r=row: self.on_sdata_path_changed(r, text))

            browse_button = QPushButton("浏览")
            browse_button.setMaximumWidth(60)
            browse_button.clicked.connect(lambda checked, r=row: self.browse_sdata_path(r))

            open_button = QPushButton("打开")
            open_button.setMaximumWidth(60)
            open_button.setIcon(QIcon.fromTheme("folder-open"))
            open_button.setToolTip("打开导出目录")
            open_button.clicked.connect(lambda checked, r=row: self.open_sdata_path(r))

            path_layout.addWidget(path_edit)
            path_layout.addWidget(browse_button)
            path_layout.addWidget(open_button)

            self.sdata_path_table.setCellWidget(row, 2, path_widget)

    def on_sdata_path_changed(self, row, text):
        """路径文本改变时更新数据"""
        if row < len(self.sdata_items):
            self.sdata_items[row]['path'] = text
            # 标记为已修改
            self.sdata_paths_modified = True

    def browse_sdata_path(self, row):
        """浏览选择路径"""
        if row >= len(self.sdata_items):
            return

        item = self.sdata_items[row]
        current_path = item['path']

        # 如果当前路径为空，使用默认路径
        if not current_path:
            current_path = os.path.expanduser("~")
        elif os.path.isfile(current_path):
            current_path = os.path.dirname(current_path)
        elif not os.path.exists(current_path):
            current_path = os.path.expanduser("~")

        # 选择目录而不是文件
        dir_path = QFileDialog.getExistingDirectory(
            self,
            f"选择{item['type']}导出目录",
            current_path
        )

        if dir_path:
            # 更新路径
            self.sdata_items[row]['path'] = dir_path
            # 标记为已修改
            self.sdata_paths_modified = True

            # 更新界面
            path_widget = self.sdata_path_table.cellWidget(row, 2)
            if path_widget:
                path_edit = path_widget.findChild(QLineEdit)
                if path_edit:
                    path_edit.setText(dir_path)

    def open_sdata_path(self, row):
        """打开SData导出目录并选中对应的SData文件"""
        if row >= len(self.sdata_items):
            return

        item = self.sdata_items[row]
        path = item['path'].strip()

        # self.logger.info(f"尝试打开路径: {path}")

        if not path:
            QMessageBox.information(self, "提示", "请先设置导出目录")
            return

        # 规范化路径
        path = os.path.abspath(path)
        # self.logger.info(f"规范化后的路径: {path}")

        if not os.path.exists(path):
            reply = QMessageBox.question(
                self,
                "目录不存在",
                f"目录不存在：{path}\n\n是否创建该目录？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            if reply == QMessageBox.Yes:
                try:
                    os.makedirs(path, exist_ok=True)
                    self.logger.info(f"已创建目录: {path}")
                except Exception as e:
                    QMessageBox.critical(self, "错误", f"创建目录失败：{str(e)}")
                    return
            else:
                return

        # 构建SData文件名
        sdata_filename = f"{item['sheet_name']}{item['type'].replace('.SData', '.SData')}"
        sdata_file_path = os.path.join(path, sdata_filename)

        # self.logger.info(f"查找SData文件: {sdata_file_path}")

        # 打开目录并尝试选中文件
        try:
            if os.path.exists(sdata_file_path):
                # 如果SData文件存在，选中该文件
                if os.name == 'nt':  # Windows
                    try:
                        # 使用绝对路径并确保路径格式正确
                        abs_file_path = os.path.abspath(sdata_file_path)

                        # 使用explorer命令选中文件
                        import subprocess
                        subprocess.run(f'explorer /select,"{abs_file_path}"', shell=True, capture_output=True, text=True)

                        # explorer /select 命令即使返回非零退出码通常也是成功的
                        # 所以我们直接认为操作成功
                        self.logger.info(f"打开对应文件目录: {sdata_filename}")

                    except Exception as e:
                        self.logger.error(f"选中文件失败: {str(e)}")
                        # 如果出错，尝试只打开目录
                        self._open_folder(path)
                else:
                    # macOS 和 Linux 只能打开目录
                    self._open_folder(path)
                    self.logger.info(f"已打开目录: {path}")
            else:
                # 如果SData文件不存在，只打开目录
                self.logger.info(f"SData文件不存在，只打开目录: {path}")
                self._open_folder(path)
                self.logger.info(f"SData文件尚未生成，已打开目录: {path}")
        except Exception as e:
            self.logger.error(f"打开目录失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开目录失败：{str(e)}")

    def batch_set_client_sdata_path(self):
        """批量设置Client.SData路径"""
        base_path = QFileDialog.getExistingDirectory(self, "选择Client.SData文件导出目录")
        if not base_path:
            return

        updated_count = 0
        for row, item in enumerate(self.sdata_items):
            # 只更新Client.SData类型的项目
            if item['type'] == 'Client.SData':
                # 更新数据
                self.sdata_items[row]['path'] = base_path
                updated_count += 1

                # 更新界面
                path_widget = self.sdata_path_table.cellWidget(row, 2)
                if path_widget:
                    path_edit = path_widget.findChild(QLineEdit)
                    if path_edit:
                        path_edit.setText(base_path)

        if updated_count > 0:
            # 标记为已修改
            self.sdata_paths_modified = True
            QMessageBox.information(self, "批量设置完成", f"已为 {updated_count} 个Client.SData文件设置导出路径")
        else:
            QMessageBox.information(self, "提示", "没有找到Client.SData文件")

    def batch_set_server_sdata_path(self):
        """批量设置Server.SData路径"""
        base_path = QFileDialog.getExistingDirectory(self, "选择Server.SData文件导出目录")
        if not base_path:
            return

        updated_count = 0
        for row, item in enumerate(self.sdata_items):
            # 只更新Server.SData类型的项目
            if item['type'] == 'Server.SData':
                # 更新数据
                self.sdata_items[row]['path'] = base_path
                updated_count += 1

                # 更新界面
                path_widget = self.sdata_path_table.cellWidget(row, 2)
                if path_widget:
                    path_edit = path_widget.findChild(QLineEdit)
                    if path_edit:
                        path_edit.setText(base_path)

        if updated_count > 0:
            # 标记为已修改
            self.sdata_paths_modified = True
            QMessageBox.information(self, "批量设置完成", f"已为 {updated_count} 个Server.SData文件设置导出路径")
        else:
            QMessageBox.information(self, "提示", "没有找到Server.SData文件")

    def clear_all_sdata_paths(self):
        """清空所有路径"""
        reply = QMessageBox.question(
            self,
            "确认清空",
            "确定要清空所有SData文件的导出路径设置吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            for row, item in enumerate(self.sdata_items):
                # 更新数据
                self.sdata_items[row]['path'] = ''

                # 更新界面
                path_widget = self.sdata_path_table.cellWidget(row, 2)
                if path_widget:
                    path_edit = path_widget.findChild(QLineEdit)
                    if path_edit:
                        path_edit.setText('')

            # 标记为已修改
            self.sdata_paths_modified = True

    def save_sdata_path_settings(self):
        """保存SData路径设置"""
        # 检查是否勾选了导出SData到设置位置
        if self.export_sdata_custom_checkbox.isChecked():
            # 检查是否有空路径
            empty_paths = self.check_empty_sdata_paths()
            if empty_paths:
                error_msg = "勾选了'导出SData到设置位置'，但以下SData文件的导出路径为空：\n\n" + "\n".join(empty_paths)
                error_msg += "\n\n请为所有SData文件设置导出路径，或取消勾选'导出SData到设置位置'选项。"
                QMessageBox.warning(self, "路径设置不完整", error_msg)
                return

        # 验证路径
        invalid_paths = self.validate_sdata_path_settings()
        if invalid_paths:
            error_msg = "以下路径无效：\n\n" + "\n".join(invalid_paths)
            QMessageBox.warning(self, "路径验证失败", error_msg)
            return

        # 更新sdata_paths
        for item in self.sdata_items:
            self.sdata_paths[item['key']] = item['path']

        # 保存设置
        self.save_settings()
        # 清除修改标志
        self.sdata_paths_modified = False
        self.logger.info("SData导出位置设置已保存")
        QMessageBox.information(self, "保存成功", "SData导出位置设置已保存")

    def validate_sdata_path_settings(self):
        """验证所有路径的有效性"""
        invalid_paths = []

        for item in self.sdata_items:
            path = item['path'].strip()
            if not path:
                continue  # 空路径跳过验证

            # 检查目录是否存在
            if not os.path.exists(path):
                invalid_paths.append(f"{item['excel_file']}/{item['sheet_name']}/{item['type']}: 目录不存在 {path}")
                continue

            # 检查是否是目录
            if not os.path.isdir(path):
                invalid_paths.append(f"{item['excel_file']}/{item['sheet_name']}/{item['type']}: 路径不是目录 {path}")
                continue

            # 检查是否有写入权限
            try:
                test_file = os.path.join(path, "test.tmp")
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)
            except Exception as e:
                invalid_paths.append(f"{item['excel_file']}/{item['sheet_name']}/{item['type']}: 没有写入权限 {path} (错误: {str(e)})")

        return invalid_paths

    def check_empty_sdata_paths(self):
        """检查是否有空的SData路径设置"""
        empty_paths = []

        for item in self.sdata_items:
            path = item['path'].strip()
            if not path:
                empty_paths.append(f"{item['excel_file']}/{item['sheet_name']}/{item['type']}")

        return empty_paths

    def switch_to_sdata_path_tab(self):
        """切换到SData路径设置页签"""
        self.tab_widget.setCurrentIndex(4)  # SData路径设置页签的索引

    # ==================== 搜索功能实现 ====================

    def on_search_text_changed(self, text):
        """搜索文本改变时的处理"""
        if not text.strip():
            self.clear_search_highlighting()

    def perform_search(self):
        """执行搜索"""
        search_text = self.search_edit.text().strip()
        if not search_text:
            self.clear_search()
            return

        # 添加到搜索历史
        if search_text not in self.search_history:
            self.search_history.insert(0, search_text)
            # 限制历史记录数量
            if len(self.search_history) > 20:
                self.search_history = self.search_history[:20]

        # 执行搜索
        results = self.search_files(search_text)

        if results:
            self.show_search_results(results)
            self.logger.info(f"搜索 '{search_text}' 找到 {len(results)} 个结果")
        else:
            self.logger.info(f"搜索 '{search_text}' 未找到匹配的文件")
            QMessageBox.information(self, "搜索结果", f"未找到匹配 '{search_text}' 的文件")

    def search_files(self, search_text):
        """搜索文件"""
        results = []

        try:
            # 判断是否为正则表达式搜索
            import re
            try:
                pattern = re.compile(search_text, re.IGNORECASE)
                use_regex = True
            except re.error:
                # 如果不是有效的正则表达式，使用普通字符串搜索
                pattern = None
                use_regex = False

            # 搜索Excel文件
            if self.search_filters['excel_files']:
                for i in range(self.file_list.count()):
                    item = self.file_list.item(i)
                    filename = item.text()
                    if self.match_search_pattern(filename, search_text, pattern, use_regex):
                        results.append({
                            'type': 'excel',
                            'filename': filename,
                            'tab_index': 0,
                            'list_widget': self.file_list,
                            'item_index': i
                        })

            # 搜索结构体文件
            if self.search_filters['struct_files']:
                # 搜索服务端结构体文件
                for i in range(self.server_struct_list.count()):
                    item = self.server_struct_list.item(i)
                    filename = item.text()
                    if self.match_search_pattern(filename, search_text, pattern, use_regex):
                        results.append({
                            'type': 'struct_server',
                            'filename': filename,
                            'tab_index': 1,
                            'list_widget': self.server_struct_list,
                            'item_index': i,
                            'subtab_index': 0
                        })

                # 搜索客户端结构体文件
                for i in range(self.client_struct_list.count()):
                    item = self.client_struct_list.item(i)
                    filename = item.text()
                    if self.match_search_pattern(filename, search_text, pattern, use_regex):
                        results.append({
                            'type': 'struct_client',
                            'filename': filename,
                            'tab_index': 1,
                            'list_widget': self.client_struct_list,
                            'item_index': i,
                            'subtab_index': 1
                        })

            # 搜索SData文件
            if self.search_filters['sdata_files']:
                # 搜索服务端SData文件
                for i in range(self.server_sdata_list.count()):
                    item = self.server_sdata_list.item(i)
                    filename = item.text()
                    if self.match_search_pattern(filename, search_text, pattern, use_regex):
                        results.append({
                            'type': 'sdata_server',
                            'filename': filename,
                            'tab_index': 2,
                            'list_widget': self.server_sdata_list,
                            'item_index': i,
                            'subtab_index': 0
                        })

                # 搜索客户端SData文件
                for i in range(self.client_sdata_list.count()):
                    item = self.client_sdata_list.item(i)
                    filename = item.text()
                    if self.match_search_pattern(filename, search_text, pattern, use_regex):
                        results.append({
                            'type': 'sdata_client',
                            'filename': filename,
                            'tab_index': 2,
                            'list_widget': self.client_sdata_list,
                            'item_index': i,
                            'subtab_index': 1
                        })

            # 搜索加载函数文件
            if self.search_filters['load_function_files']:
                # 搜索服务端加载函数文件
                for i in range(self.server_load_function_list.count()):
                    item = self.server_load_function_list.item(i)
                    filename = item.text()
                    if self.match_search_pattern(filename, search_text, pattern, use_regex):
                        results.append({
                            'type': 'load_function_server',
                            'filename': filename,
                            'tab_index': 3,
                            'list_widget': self.server_load_function_list,
                            'item_index': i,
                            'subtab_index': 0
                        })

                # 搜索客户端加载函数文件
                for i in range(self.client_load_function_list.count()):
                    item = self.client_load_function_list.item(i)
                    filename = item.text()
                    if self.match_search_pattern(filename, search_text, pattern, use_regex):
                        results.append({
                            'type': 'load_function_client',
                            'filename': filename,
                            'tab_index': 3,
                            'list_widget': self.client_load_function_list,
                            'item_index': i,
                            'subtab_index': 1
                        })

        except Exception as e:
            self.logger.error(f"搜索过程中发生错误: {str(e)}")

        return results

    def match_search_pattern(self, filename, search_text, pattern, use_regex):
        """匹配搜索模式"""
        if use_regex and pattern:
            return pattern.search(filename) is not None
        else:
            return search_text.lower() in filename.lower()

    def show_search_results(self, results):
        """显示搜索结果"""
        if not results:
            return

        # 如果只有一个结果，直接跳转
        if len(results) == 1:
            self.jump_to_file(results[0])
            return

        # 多个结果时显示选择对话框
        self.show_search_results_dialog(results)

    def show_search_results_dialog(self, results):
        """显示搜索结果选择对话框"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QListWidget, QDialogButtonBox, QPushButton

        dialog = QDialog(self)
        dialog.setWindowTitle(f"搜索结果 ({len(results)} 个)")
        dialog.setModal(True)
        dialog.resize(500, 400)

        layout = QVBoxLayout(dialog)

        # 结果列表
        results_list = QListWidget()
        for i, result in enumerate(results):
            type_name = self.get_file_type_display_name(result['type'])
            item_text = f"[{type_name}] {result['filename']}"
            results_list.addItem(item_text)

        results_list.itemDoubleClicked.connect(lambda item: self.on_search_result_selected(results, results_list.currentRow(), dialog))
        layout.addWidget(results_list)

        # 按钮
        button_box = QDialogButtonBox()
        jump_button = QPushButton("跳转")
        jump_button.clicked.connect(lambda: self.on_search_result_selected(results, results_list.currentRow(), dialog))
        button_box.addButton(jump_button, QDialogButtonBox.AcceptRole)

        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(dialog.reject)
        button_box.addButton(cancel_button, QDialogButtonBox.RejectRole)

        layout.addWidget(button_box)

        # 默认选中第一项
        if results_list.count() > 0:
            results_list.setCurrentRow(0)

        dialog.exec_()

    def get_file_type_display_name(self, file_type):
        """获取文件类型的显示名称"""
        type_names = {
            'excel': 'Excel文件',
            'struct_server': '服务端结构体',
            'struct_client': '客户端结构体',
            'sdata_server': '服务端SData',
            'sdata_client': '客户端SData',
            'load_function_server': '服务端加载函数',
            'load_function_client': '客户端加载函数'
        }
        return type_names.get(file_type, '未知类型')

    def on_search_result_selected(self, results, index, dialog):
        """搜索结果被选中时的处理"""
        if 0 <= index < len(results):
            self.jump_to_file(results[index])
            dialog.accept()

    def jump_to_file(self, result):
        """跳转到指定文件"""
        try:
            # 切换到对应的主标签页
            self.tab_widget.setCurrentIndex(result['tab_index'])

            # 如果有子标签页，切换到对应的子标签页
            if 'subtab_index' in result:
                if result['tab_index'] == 1:  # 结构体标签页
                    self.struct_subtabs.setCurrentIndex(result['subtab_index'])
                elif result['tab_index'] == 2:  # SData标签页
                    self.sdata_subtabs.setCurrentIndex(result['subtab_index'])
                elif result['tab_index'] == 3:  # 加载函数文件标签页
                    self.load_function_subtabs.setCurrentIndex(result['subtab_index'])

            # 选中对应的文件项
            list_widget = result['list_widget']
            item_index = result['item_index']

            # 清除之前的选择
            list_widget.clearSelection()

            # 选中目标项
            if 0 <= item_index < list_widget.count():
                item = list_widget.item(item_index)
                list_widget.setCurrentItem(item)
                item.setSelected(True)

                # 确保项目可见
                list_widget.scrollToItem(item)

                # 设置焦点
                list_widget.setFocus()

                self.logger.info(f"已跳转到 {self.get_file_type_display_name(result['type'])}: {result['filename']}")

        except Exception as e:
            self.logger.error(f"跳转到文件时发生错误: {str(e)}")

    def clear_search(self):
        """清除搜索"""
        self.search_edit.clear()
        # 隐藏智能搜索的建议列表
        if hasattr(self.search_widget, 'hide_suggestions'):
            self.search_widget.hide_suggestions()
        self.clear_search_highlighting()
        self.current_search_results.clear()
        self.logger.info("已清除搜索")

    def clear_search_highlighting(self):
        """清除搜索高亮"""
        # 这里可以添加清除高亮显示的逻辑
        pass

    def show_search_filters(self):
        """显示搜索过滤器对话框"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QCheckBox, QDialogButtonBox, QPushButton, QGroupBox

        dialog = QDialog(self)
        dialog.setWindowTitle("搜索过滤器")
        dialog.setModal(True)
        dialog.resize(300, 250)

        layout = QVBoxLayout(dialog)

        # 文件类型过滤器组
        filter_group = QGroupBox("搜索文件类型")
        filter_layout = QVBoxLayout(filter_group)

        # 创建复选框
        self.excel_filter_cb = QCheckBox("Excel文件")
        self.excel_filter_cb.setChecked(self.search_filters['excel_files'])
        filter_layout.addWidget(self.excel_filter_cb)

        self.struct_filter_cb = QCheckBox("结构体文件")
        self.struct_filter_cb.setChecked(self.search_filters['struct_files'])
        filter_layout.addWidget(self.struct_filter_cb)

        self.sdata_filter_cb = QCheckBox("SData文件")
        self.sdata_filter_cb.setChecked(self.search_filters['sdata_files'])
        filter_layout.addWidget(self.sdata_filter_cb)

        self.load_function_filter_cb = QCheckBox("加载函数文件")
        self.load_function_filter_cb.setChecked(self.search_filters['load_function_files'])
        filter_layout.addWidget(self.load_function_filter_cb)

        layout.addWidget(filter_group)

        # 快捷操作按钮
        quick_actions_layout = QVBoxLayout()

        select_all_button = QPushButton("全选")
        select_all_button.clicked.connect(lambda: self.set_all_filters(True))
        quick_actions_layout.addWidget(select_all_button)

        select_none_button = QPushButton("全不选")
        select_none_button.clicked.connect(lambda: self.set_all_filters(False))
        quick_actions_layout.addWidget(select_none_button)

        layout.addLayout(quick_actions_layout)

        # 按钮
        button_box = QDialogButtonBox()
        ok_button = QPushButton("确定")
        ok_button.clicked.connect(lambda: self.apply_search_filters(dialog))
        button_box.addButton(ok_button, QDialogButtonBox.AcceptRole)

        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(dialog.reject)
        button_box.addButton(cancel_button, QDialogButtonBox.RejectRole)

        layout.addWidget(button_box)

        dialog.exec_()

    def set_all_filters(self, checked):
        """设置所有过滤器的状态"""
        self.excel_filter_cb.setChecked(checked)
        self.struct_filter_cb.setChecked(checked)
        self.sdata_filter_cb.setChecked(checked)
        self.load_function_filter_cb.setChecked(checked)

    def apply_search_filters(self, dialog):
        """应用搜索过滤器设置"""
        self.search_filters['excel_files'] = self.excel_filter_cb.isChecked()
        self.search_filters['struct_files'] = self.struct_filter_cb.isChecked()
        self.search_filters['sdata_files'] = self.sdata_filter_cb.isChecked()
        self.search_filters['load_function_files'] = self.load_function_filter_cb.isChecked()

        # 检查是否至少选择了一种文件类型
        if not any(self.search_filters.values()):
            QMessageBox.warning(self, "过滤器设置", "请至少选择一种文件类型进行搜索")
            return

        dialog.accept()
        self.logger.info("搜索过滤器设置已更新")

    def focus_search(self):
        """将焦点设置到搜索框"""
        self.search_edit.setFocus()
        self.search_edit.selectAll()
        self.logger.info("已聚焦到搜索框")

    def create_smart_search_widget(self):
        """创建智能搜索组件"""
        return SmartSearchWidget(self)