#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                           QPushButton, QLabel, QTextEdit, QApplication,
                           QSplitter, QTreeWidget, QTreeWidgetItem, QHeaderView)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QIcon, QFont, QTextCursor

from utils.sdata_parser import SDataParser

class SDataViewer(QMainWindow):
    def __init__(self, file_path=None, parent=None):
        super().__init__(parent)
        self.file_path = file_path
        self.parser = SDataParser()
        
        self.init_ui()
        if file_path:
            self.load_file(file_path)
    
    def init_ui(self):
        self.setWindowTitle('SData 查看器')
        self.setMinimumSize(800, 600)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 创建文件信息区
        file_info_layout = QHBoxLayout()
        self.file_label = QLabel("文件: 未加载")
        file_info_layout.addWidget(self.file_label)
        file_info_layout.addStretch()
        
        # 添加按钮区域
        button_layout = QHBoxLayout()
        
        self.reload_button = QPushButton("重新加载")
        self.reload_button.clicked.connect(self.reload_file)
        button_layout.addWidget(self.reload_button)
        
        button_layout.addStretch()
        
        # 添加到主布局
        main_layout.addLayout(file_info_layout)
        
        # 创建文本查看器
        self.text_edit = QTextEdit()
        self.text_edit.setReadOnly(True)
        self.text_edit.setFont(QFont("Consolas", 10))
        
        # 添加到分割器
        main_layout.addWidget(self.text_edit)
        
        # 添加按钮区域
        main_layout.addLayout(button_layout)
    
    def load_file(self, file_path):
        """加载并解析SData文件"""
        if not file_path or not os.path.exists(file_path):
            self.text_edit.setText("文件不存在")
            return
        
        self.file_path = file_path
        self.file_label.setText(f"文件: {os.path.basename(file_path)}")
        
        try:
            # 解析文件
            parsed_text = self.parser.parse_sdata_file(file_path)
            self.text_edit.setText(parsed_text)
            
        except Exception as e:
            self.text_edit.setText(f"解析文件出错: {str(e)}")
    
    def reload_file(self):
        """重新加载当前文件"""
        if self.file_path:
            self.load_file(self.file_path)

def main():
    app = QApplication(sys.argv)
    
    # 获取命令行参数中的文件路径
    file_path = sys.argv[1] if len(sys.argv) > 1 else None
    
    viewer = SDataViewer(file_path)
    viewer.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 