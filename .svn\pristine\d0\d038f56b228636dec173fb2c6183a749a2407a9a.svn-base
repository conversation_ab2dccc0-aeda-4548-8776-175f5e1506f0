2025-05-27 15:28:59,442 - INFO - 正在扫描路径: F:/配置表/新导表文件/Config
2025-05-27 15:28:59,442 - INFO - 正在扫描路径: F:/配置表/新导表文件/Config
2025-05-27 15:28:59,443 - INFO - 找到 2 个Excel配置文件
2025-05-27 15:28:59,443 - INFO - 找到 2 个Excel配置文件
2025-05-27 15:28:59,569 - INFO - 找到 26 个SData文件 (服务端: 13, 客户端: 13)
2025-05-27 15:28:59,569 - INFO - 找到 26 个SData文件 (服务端: 13, 客户端: 13)
2025-05-27 15:28:59,570 - INFO - 未找到加载函数文件
2025-05-27 15:28:59,570 - INFO - 未找到加载函数文件
2025-05-27 15:29:08,018 - INFO - 正在打开目录: F:\配置表\新导表文件\Output\节日活动配置(适配端午)\DragonBoatBoard\Server
2025-05-27 15:29:08,018 - INFO - 正在打开目录: F:\配置表\新导表文件\Output\节日活动配置(适配端午)\DragonBoatBoard\Server
2025-05-27 15:29:08,095 - INFO - 已打开文件夹: F:\配置表\新导表文件\Output\节日活动配置(适配端午)\DragonBoatBoard\Server
2025-05-27 15:29:08,095 - INFO - 已打开文件夹: F:\配置表\新导表文件\Output\节日活动配置(适配端午)\DragonBoatBoard\Server
2025-05-27 15:29:19,786 - INFO - 未找到SData文件
2025-05-27 15:29:19,786 - INFO - 未找到SData文件
2025-05-27 15:29:22,060 - INFO - 正在扫描路径: F:/配置表/新导表文件/Config
2025-05-27 15:29:22,060 - INFO - 正在扫描路径: F:/配置表/新导表文件/Config
2025-05-27 15:29:22,061 - INFO - 找到 2 个Excel配置文件
2025-05-27 15:29:22,061 - INFO - 找到 2 个Excel配置文件
2025-05-27 15:29:23,215 - INFO - 未找到SData文件
2025-05-27 15:29:23,215 - INFO - 未找到SData文件
2025-05-27 15:29:23,982 - INFO - 未找到加载函数文件
2025-05-27 15:29:23,982 - INFO - 未找到加载函数文件
2025-05-27 15:29:26,166 - INFO - 开始导出全部 2 个文件...
2025-05-27 15:29:26,166 - INFO - 开始导出全部 2 个文件...
2025-05-27 15:29:26,177 - INFO - 开始处理: 活动货币表.xlsx
2025-05-27 15:29:26,177 - INFO - 开始处理: 活动货币表.xlsx
2025-05-27 15:29:26,210 - WARNING - 页签 'CurrencySystem' 忽略列 A (说明: '货币表（非道具不在背包内）')，没有类型或字段名
2025-05-27 15:29:26,210 - WARNING - 页签 'CurrencySystem' 忽略列 A (说明: '货币表（非道具不在背包内）')，没有类型或字段名
2025-05-27 15:29:26,215 - INFO - 为页签 CurrencySystem 生成加载函数文件...
2025-05-27 15:29:26,215 - INFO - 为页签 CurrencySystem 生成加载函数文件...
2025-05-27 15:29:26,216 - INFO - 服务端加载函数文件 LoadCurrencySystemServer.txt 生成成功
2025-05-27 15:29:26,216 - INFO - 服务端加载函数文件 LoadCurrencySystemServer.txt 生成成功
2025-05-27 15:29:26,217 - INFO - 客户端加载函数文件 LoadCurrencySystemClient.txt 生成成功
2025-05-27 15:29:26,217 - INFO - 客户端加载函数文件 LoadCurrencySystemClient.txt 生成成功
2025-05-27 15:29:26,218 - INFO - 页签 CurrencySystem 处理完成
2025-05-27 15:29:26,218 - INFO - 页签 CurrencySystem 处理完成
2025-05-27 15:29:26,240 - INFO - 开始处理: 节日活动配置(适配端午).xlsx
2025-05-27 15:29:26,240 - INFO - 开始处理: 节日活动配置(适配端午).xlsx
2025-05-27 15:29:26,304 - WARNING - 页签 'FestivalActController' 忽略列 A (说明: '活动管理总表')，没有类型或字段名
2025-05-27 15:29:26,306 - WARNING - 页签 'FestivalAct' 忽略列 A (说明: '活动预告页面信息表')，没有类型或字段名
2025-05-27 15:29:26,304 - WARNING - 页签 'FestivalActController' 忽略列 A (说明: '活动管理总表')，没有类型或字段名
2025-05-27 15:29:26,308 - WARNING - 页签 'FestivalActSignIn' 忽略列 A (说明: '签到奖励表')，没有类型或字段名
2025-05-27 15:29:26,311 - WARNING - 页签 'FestivalQuest' 忽略列 A (说明: '活动任务表')，没有类型或字段名
2025-05-27 15:29:26,306 - WARNING - 页签 'FestivalAct' 忽略列 A (说明: '活动预告页面信息表')，没有类型或字段名
2025-05-27 15:29:26,312 - WARNING - 页签 'FestivalActController' 忽略列 O (说明: '类型备注(策划自用)')，没有类型或字段名
2025-05-27 15:29:26,308 - WARNING - 页签 'FestivalActSignIn' 忽略列 A (说明: '签到奖励表')，没有类型或字段名
2025-05-27 15:29:26,313 - WARNING - 页签 'FestivalAct' 忽略列 H (说明: '备注
策划自用')，没有类型或字段名
2025-05-27 15:29:26,311 - WARNING - 页签 'FestivalQuest' 忽略列 A (说明: '活动任务表')，没有类型或字段名
2025-05-27 15:29:26,312 - WARNING - 页签 'FestivalActController' 忽略列 O (说明: '类型备注(策划自用)')，没有类型或字段名
2025-05-27 15:29:26,313 - WARNING - 页签 'FestivalAct' 忽略列 H (说明: '备注
策划自用')，没有类型或字段名
2025-05-27 15:29:26,315 - WARNING - 页签 'FestivalQuest' 忽略列 K，没有类型或字段名
2025-05-27 15:29:26,315 - WARNING - 页签 'FestivalQuest' 忽略列 K，没有类型或字段名
2025-05-27 15:29:26,317 - WARNING - 页签 'FestivalQuest' 忽略列 L，没有类型或字段名
2025-05-27 15:29:26,317 - WARNING - 页签 'FestivalQuest' 忽略列 L，没有类型或字段名
2025-05-27 15:29:26,319 - WARNING - 页签 'FestivalQuest' 忽略列 M，没有类型或字段名
2025-05-27 15:29:26,319 - WARNING - 页签 'FestivalQuest' 忽略列 M，没有类型或字段名
2025-05-27 15:29:26,321 - WARNING - 页签 'FestivalQuest' 忽略列 N，没有类型或字段名
2025-05-27 15:29:26,321 - WARNING - 页签 'FestivalQuest' 忽略列 N，没有类型或字段名
2025-05-27 15:29:26,325 - INFO - 为页签 FestivalActSignIn 生成加载函数文件...
2025-05-27 15:29:26,325 - INFO - 为页签 FestivalActSignIn 生成加载函数文件...
2025-05-27 15:29:26,331 - INFO - 为页签 FestivalAct 生成加载函数文件...
2025-05-27 15:29:26,331 - INFO - 服务端加载函数文件 LoadFestivalActSignInServer.txt 生成成功
2025-05-27 15:29:26,331 - INFO - 为页签 FestivalAct 生成加载函数文件...
2025-05-27 15:29:26,331 - INFO - 服务端加载函数文件 LoadFestivalActSignInServer.txt 生成成功
2025-05-27 15:29:26,333 - INFO - 服务端加载函数文件 LoadFestivalActServer.txt 生成成功
2025-05-27 15:29:26,333 - INFO - 服务端加载函数文件 LoadFestivalActServer.txt 生成成功
2025-05-27 15:29:26,336 - INFO - 客户端加载函数文件 LoadFestivalActSignInClient.txt 生成成功
2025-05-27 15:29:26,336 - INFO - 客户端加载函数文件 LoadFestivalActSignInClient.txt 生成成功
2025-05-27 15:29:26,339 - INFO - 为页签 FestivalQuest 生成加载函数文件...
2025-05-27 15:29:26,339 - INFO - 页签 FestivalActSignIn 处理完成
2025-05-27 15:29:26,339 - INFO - 为页签 FestivalActController 生成加载函数文件...
2025-05-27 15:29:26,339 - INFO - 为页签 FestivalQuest 生成加载函数文件...
2025-05-27 15:29:26,339 - INFO - 客户端加载函数文件 LoadFestivalActClient.txt 生成成功
2025-05-27 15:29:26,339 - INFO - 页签 FestivalActSignIn 处理完成
2025-05-27 15:29:26,339 - INFO - 为页签 FestivalActController 生成加载函数文件...
2025-05-27 15:29:26,341 - WARNING - 页签 'FestivalQuestActive' 忽略列 A (说明: '任务进度奖励表')，没有类型或字段名
2025-05-27 15:29:26,341 - INFO - 服务端加载函数文件 LoadFestivalQuestServer.txt 生成成功
2025-05-27 15:29:26,339 - INFO - 客户端加载函数文件 LoadFestivalActClient.txt 生成成功
2025-05-27 15:29:26,341 - WARNING - 页签 'FestivalQuestActive' 忽略列 A (说明: '任务进度奖励表')，没有类型或字段名
2025-05-27 15:29:26,342 - INFO - 页签 FestivalAct 处理完成
2025-05-27 15:29:26,342 - INFO - 服务端加载函数文件 LoadFestivalActControllerServer.txt 生成成功
2025-05-27 15:29:26,341 - INFO - 服务端加载函数文件 LoadFestivalQuestServer.txt 生成成功
2025-05-27 15:29:26,342 - INFO - 页签 FestivalAct 处理完成
2025-05-27 15:29:26,342 - INFO - 服务端加载函数文件 LoadFestivalActControllerServer.txt 生成成功
2025-05-27 15:29:26,344 - INFO - 客户端加载函数文件 LoadFestivalQuestClient.txt 生成成功
2025-05-27 15:29:26,345 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 A (说明: '活动通行证等级')，没有类型或字段名
2025-05-27 15:29:26,344 - INFO - 客户端加载函数文件 LoadFestivalQuestClient.txt 生成成功
2025-05-27 15:29:26,345 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 A (说明: '活动通行证等级')，没有类型或字段名
2025-05-27 15:29:26,346 - INFO - 页签 FestivalQuest 处理完成
2025-05-27 15:29:26,347 - INFO - 客户端加载函数文件 LoadFestivalActControllerClient.txt 生成成功
2025-05-27 15:29:26,346 - INFO - 页签 FestivalQuest 处理完成
2025-05-27 15:29:26,347 - INFO - 客户端加载函数文件 LoadFestivalActControllerClient.txt 生成成功
2025-05-27 15:29:26,350 - WARNING - 页签 'FestivalShop' 忽略列 A (说明: '活动商店表')，没有类型或字段名
2025-05-27 15:29:26,350 - INFO - 页签 FestivalActController 处理完成
2025-05-27 15:29:26,350 - WARNING - 页签 'FestivalShop' 忽略列 A (说明: '活动商店表')，没有类型或字段名
2025-05-27 15:29:26,350 - INFO - 页签 FestivalActController 处理完成
2025-05-27 15:29:26,352 - WARNING - 页签 'FestivalShop' 忽略列 O (说明: '备注')，没有类型或字段名
2025-05-27 15:29:26,356 - WARNING - 页签 'DragonBoatBoard' 忽略列 A (说明: '棋盘参数总表')，没有类型或字段名
2025-05-27 15:29:26,352 - WARNING - 页签 'FestivalShop' 忽略列 O (说明: '备注')，没有类型或字段名
2025-05-27 15:29:26,356 - WARNING - 页签 'DragonBoatBoard' 忽略列 A (说明: '棋盘参数总表')，没有类型或字段名
2025-05-27 15:29:26,364 - INFO - 为页签 FestivalQuestActive 生成加载函数文件...
2025-05-27 15:29:26,364 - INFO - 为页签 FestivalQuestActive 生成加载函数文件...
2025-05-27 15:29:26,369 - INFO - 服务端加载函数文件 LoadFestivalQuestActiveServer.txt 生成成功
2025-05-27 15:29:26,369 - INFO - 为页签 FestivalActWarOrderLevel 生成加载函数文件...
2025-05-27 15:29:26,369 - INFO - 服务端加载函数文件 LoadFestivalQuestActiveServer.txt 生成成功
2025-05-27 15:29:26,369 - INFO - 为页签 FestivalActWarOrderLevel 生成加载函数文件...
2025-05-27 15:29:26,372 - INFO - 客户端加载函数文件 LoadFestivalQuestActiveClient.txt 生成成功
2025-05-27 15:29:26,372 - INFO - 为页签 DragonBoatBoard 生成加载函数文件...
2025-05-27 15:29:26,372 - INFO - 客户端加载函数文件 LoadFestivalQuestActiveClient.txt 生成成功
2025-05-27 15:29:26,373 - INFO - 服务端加载函数文件 LoadFestivalActWarOrderLevelServer.txt 生成成功
2025-05-27 15:29:26,372 - INFO - 为页签 DragonBoatBoard 生成加载函数文件...
2025-05-27 15:29:26,374 - INFO - 页签 FestivalQuestActive 处理完成
2025-05-27 15:29:26,374 - INFO - 为页签 FestivalShop 生成加载函数文件...
2025-05-27 15:29:26,373 - INFO - 服务端加载函数文件 LoadFestivalActWarOrderLevelServer.txt 生成成功
2025-05-27 15:29:26,374 - INFO - 页签 FestivalQuestActive 处理完成
2025-05-27 15:29:26,374 - INFO - 为页签 FestivalShop 生成加载函数文件...
2025-05-27 15:29:26,376 - INFO - 服务端加载函数文件 LoadDragonBoatBoardServer.txt 生成成功
2025-05-27 15:29:26,377 - WARNING - 页签 'DragonBoatGrid' 忽略列 A (说明: '龙舟棋盘表')，没有类型或字段名
2025-05-27 15:29:26,376 - INFO - 服务端加载函数文件 LoadDragonBoatBoardServer.txt 生成成功
2025-05-27 15:29:26,377 - INFO - 客户端加载函数文件 LoadFestivalActWarOrderLevelClient.txt 生成成功
2025-05-27 15:29:26,377 - WARNING - 页签 'DragonBoatGrid' 忽略列 A (说明: '龙舟棋盘表')，没有类型或字段名
2025-05-27 15:29:26,378 - INFO - 服务端加载函数文件 LoadFestivalShopServer.txt 生成成功
2025-05-27 15:29:26,377 - INFO - 客户端加载函数文件 LoadFestivalActWarOrderLevelClient.txt 生成成功
2025-05-27 15:29:26,378 - INFO - 客户端加载函数文件 LoadDragonBoatBoardClient.txt 生成成功
2025-05-27 15:29:26,378 - INFO - 服务端加载函数文件 LoadFestivalShopServer.txt 生成成功
2025-05-27 15:29:26,387 - INFO - 页签 FestivalActWarOrderLevel 处理完成
2025-05-27 15:29:26,378 - INFO - 客户端加载函数文件 LoadDragonBoatBoardClient.txt 生成成功
2025-05-27 15:29:26,387 - INFO - 页签 FestivalActWarOrderLevel 处理完成
2025-05-27 15:29:26,388 - INFO - 页签 DragonBoatBoard 处理完成
2025-05-27 15:29:26,388 - INFO - 页签 DragonBoatBoard 处理完成
2025-05-27 15:29:26,393 - INFO - 客户端加载函数文件 LoadFestivalShopClient.txt 生成成功
2025-05-27 15:29:26,395 - WARNING - 页签 'DragonBoatEvent' 忽略列 A (说明: '龙舟事件表')，没有类型或字段名
2025-05-27 15:29:26,393 - INFO - 客户端加载函数文件 LoadFestivalShopClient.txt 生成成功
2025-05-27 15:29:26,397 - WARNING - 页签 'DragonBoatRankReward' 忽略列 A (说明: '排名档位表')，没有类型或字段名
2025-05-27 15:29:26,395 - WARNING - 页签 'DragonBoatEvent' 忽略列 A (说明: '龙舟事件表')，没有类型或字段名
2025-05-27 15:29:26,403 - INFO - 页签 FestivalShop 处理完成
2025-05-27 15:29:26,397 - WARNING - 页签 'DragonBoatRankReward' 忽略列 A (说明: '排名档位表')，没有类型或字段名
2025-05-27 15:29:26,405 - WARNING - 页签 'DragonBoatEvent' 忽略列 I (说明: '自用备注')，没有类型或字段名
2025-05-27 15:29:26,403 - INFO - 页签 FestivalShop 处理完成
2025-05-27 15:29:26,405 - WARNING - 页签 'DragonBoatEvent' 忽略列 I (说明: '自用备注')，没有类型或字段名
2025-05-27 15:29:26,407 - WARNING - 页签 'DragonBoatEvent' 忽略列 J (说明: '自用备注')，没有类型或字段名
2025-05-27 15:29:26,407 - WARNING - 页签 'DragonBoatEvent' 忽略列 J (说明: '自用备注')，没有类型或字段名
2025-05-27 15:29:26,411 - WARNING - 页签 'DragonBoatEvent' 忽略列 K (说明: '自用备注')，没有类型或字段名
2025-05-27 15:29:26,411 - WARNING - 页签 'DragonBoatEvent' 忽略列 K (说明: '自用备注')，没有类型或字段名
2025-05-27 15:29:26,425 - INFO - 为页签 DragonBoatGrid 生成加载函数文件...
2025-05-27 15:29:26,425 - INFO - 为页签 DragonBoatGrid 生成加载函数文件...
2025-05-27 15:29:26,429 - INFO - 为页签 DragonBoatRankReward 生成加载函数文件...
2025-05-27 15:29:26,429 - INFO - 为页签 DragonBoatRankReward 生成加载函数文件...
2025-05-27 15:29:26,430 - INFO - 服务端加载函数文件 LoadDragonBoatGridServer.txt 生成成功
2025-05-27 15:29:26,430 - INFO - 为页签 FestivalZongzi 生成加载函数文件...
2025-05-27 15:29:26,430 - INFO - 服务端加载函数文件 LoadDragonBoatGridServer.txt 生成成功
2025-05-27 15:29:26,430 - INFO - 为页签 FestivalZongzi 生成加载函数文件...
2025-05-27 15:29:26,431 - INFO - 服务端加载函数文件 LoadDragonBoatRankRewardServer.txt 生成成功
2025-05-27 15:29:26,431 - INFO - 服务端加载函数文件 LoadDragonBoatRankRewardServer.txt 生成成功
2025-05-27 15:29:26,435 - INFO - 客户端加载函数文件 LoadDragonBoatGridClient.txt 生成成功
2025-05-27 15:29:26,435 - INFO - 服务端加载函数文件 LoadFestivalZongziServer.txt 生成成功
2025-05-27 15:29:26,435 - INFO - 为页签 DragonBoatEvent 生成加载函数文件...
2025-05-27 15:29:26,435 - INFO - 客户端加载函数文件 LoadDragonBoatGridClient.txt 生成成功
2025-05-27 15:29:26,436 - INFO - 客户端加载函数文件 LoadDragonBoatRankRewardClient.txt 生成成功
2025-05-27 15:29:26,435 - INFO - 服务端加载函数文件 LoadFestivalZongziServer.txt 生成成功
2025-05-27 15:29:26,436 - INFO - 页签 DragonBoatGrid 处理完成
2025-05-27 15:29:26,435 - INFO - 为页签 DragonBoatEvent 生成加载函数文件...
2025-05-27 15:29:26,436 - INFO - 客户端加载函数文件 LoadDragonBoatRankRewardClient.txt 生成成功
2025-05-27 15:29:26,437 - INFO - 客户端加载函数文件 LoadFestivalZongziClient.txt 生成成功
2025-05-27 15:29:26,436 - INFO - 页签 DragonBoatGrid 处理完成
2025-05-27 15:29:26,437 - INFO - 页签 DragonBoatRankReward 处理完成
2025-05-27 15:29:26,437 - INFO - 客户端加载函数文件 LoadFestivalZongziClient.txt 生成成功
2025-05-27 15:29:26,438 - INFO - 服务端加载函数文件 LoadDragonBoatEventServer.txt 生成成功
2025-05-27 15:29:26,437 - INFO - 页签 DragonBoatRankReward 处理完成
2025-05-27 15:29:26,438 - INFO - 页签 FestivalZongzi 处理完成
2025-05-27 15:29:26,438 - INFO - 服务端加载函数文件 LoadDragonBoatEventServer.txt 生成成功
2025-05-27 15:29:26,438 - INFO - 页签 FestivalZongzi 处理完成
2025-05-27 15:29:26,439 - INFO - 客户端加载函数文件 LoadDragonBoatEventClient.txt 生成成功
2025-05-27 15:29:26,439 - INFO - 客户端加载函数文件 LoadDragonBoatEventClient.txt 生成成功
2025-05-27 15:29:26,439 - INFO - 页签 DragonBoatEvent 处理完成
2025-05-27 15:29:26,439 - INFO - 页签 DragonBoatEvent 处理完成
2025-05-27 15:29:26,452 - INFO - 导出完成! 成功: 2, 跳过: 0, 失败: 0
2025-05-27 15:29:26,452 - INFO - 导出完成! 成功: 2, 跳过: 0, 失败: 0
2025-05-27 15:29:26,464 - INFO - 找到 26 个SData文件 (服务端: 13, 客户端: 13)
2025-05-27 15:29:26,464 - INFO - 找到 26 个SData文件 (服务端: 13, 客户端: 13)
2025-05-27 15:29:26,466 - INFO - 找到 26 个加载函数文件
2025-05-27 15:29:26,466 - INFO - 找到 26 个加载函数文件
2025-05-27 15:29:45,583 - INFO - 加载函数文件 节日活动配置(适配端午)/DragonBoatBoard/LoadDragonBoatBoardClient.txt 内容:
2025-05-27 15:29:45,583 - INFO - 加载函数文件 节日活动配置(适配端午)/DragonBoatBoard/LoadDragonBoatBoardClient.txt 内容:
2025-05-27 15:29:49,045 - INFO - 加载函数文件 节日活动配置(适配端午)/DragonBoatBoard/LoadDragonBoatBoardServer.txt 内容:
2025-05-27 15:29:49,045 - INFO - 加载函数文件 节日活动配置(适配端午)/DragonBoatBoard/LoadDragonBoatBoardServer.txt 内容:
2025-05-27 15:29:50,978 - INFO - 加载函数文件 节日活动配置(适配端午)/DragonBoatBoard/LoadDragonBoatBoardClient.txt 内容:
2025-05-27 15:29:50,978 - INFO - 加载函数文件 节日活动配置(适配端午)/DragonBoatBoard/LoadDragonBoatBoardClient.txt 内容:
2025-05-27 15:29:52,568 - INFO - 加载函数文件 节日活动配置(适配端午)/DragonBoatBoard/LoadDragonBoatBoardClient.txt 内容:
2025-05-27 15:29:52,568 - INFO - 加载函数文件 节日活动配置(适配端午)/DragonBoatBoard/LoadDragonBoatBoardClient.txt 内容:
2025-05-27 15:30:27,885 - INFO - 加载函数文件 节日活动配置(适配端午)/FestivalZongzi/LoadFestivalZongziClient.txt 内容:
2025-05-27 15:30:27,885 - INFO - 加载函数文件 节日活动配置(适配端午)/FestivalZongzi/LoadFestivalZongziClient.txt 内容:
2025-05-27 15:30:47,573 - INFO - 开始导出全部 2 个文件...
2025-05-27 15:30:47,573 - INFO - 开始导出全部 2 个文件...
2025-05-27 15:30:47,576 - INFO - 开始处理: 活动货币表.xlsx
2025-05-27 15:30:47,576 - INFO - 开始处理: 活动货币表.xlsx
2025-05-27 15:30:47,591 - WARNING - 页签 'CurrencySystem' 忽略列 A (说明: '货币表（非道具不在背包内）')，没有类型或字段名
2025-05-27 15:30:47,591 - WARNING - 页签 'CurrencySystem' 忽略列 A (说明: '货币表（非道具不在背包内）')，没有类型或字段名
2025-05-27 15:30:47,593 - INFO - 为页签 CurrencySystem 生成加载函数文件...
2025-05-27 15:30:47,593 - INFO - 为页签 CurrencySystem 生成加载函数文件...
2025-05-27 15:30:47,594 - INFO - 服务端加载函数文件 LoadCurrencySystemServer.txt 生成成功
2025-05-27 15:30:47,594 - INFO - 服务端加载函数文件 LoadCurrencySystemServer.txt 生成成功
2025-05-27 15:30:47,595 - INFO - 客户端加载函数文件 LoadCurrencySystemClient.txt 生成成功
2025-05-27 15:30:47,595 - INFO - 客户端加载函数文件 LoadCurrencySystemClient.txt 生成成功
2025-05-27 15:30:47,596 - INFO - 页签 CurrencySystem 处理完成
2025-05-27 15:30:47,596 - INFO - 页签 CurrencySystem 处理完成
2025-05-27 15:30:47,602 - INFO - 开始处理: 节日活动配置(适配端午).xlsx
2025-05-27 15:30:47,602 - INFO - 开始处理: 节日活动配置(适配端午).xlsx
2025-05-27 15:30:47,638 - WARNING - 页签 'FestivalActController' 忽略列 A (说明: '活动管理总表')，没有类型或字段名
2025-05-27 15:30:47,641 - WARNING - 页签 'FestivalAct' 忽略列 A (说明: '活动预告页面信息表')，没有类型或字段名
2025-05-27 15:30:47,638 - WARNING - 页签 'FestivalActController' 忽略列 A (说明: '活动管理总表')，没有类型或字段名
2025-05-27 15:30:47,644 - WARNING - 页签 'FestivalActSignIn' 忽略列 A (说明: '签到奖励表')，没有类型或字段名
2025-05-27 15:30:47,646 - WARNING - 页签 'FestivalQuest' 忽略列 A (说明: '活动任务表')，没有类型或字段名
2025-05-27 15:30:47,641 - WARNING - 页签 'FestivalAct' 忽略列 A (说明: '活动预告页面信息表')，没有类型或字段名
2025-05-27 15:30:47,647 - WARNING - 页签 'FestivalActController' 忽略列 O (说明: '类型备注(策划自用)')，没有类型或字段名
2025-05-27 15:30:47,644 - WARNING - 页签 'FestivalActSignIn' 忽略列 A (说明: '签到奖励表')，没有类型或字段名
2025-05-27 15:30:47,648 - WARNING - 页签 'FestivalAct' 忽略列 H (说明: '备注
策划自用')，没有类型或字段名
2025-05-27 15:30:47,646 - WARNING - 页签 'FestivalQuest' 忽略列 A (说明: '活动任务表')，没有类型或字段名
2025-05-27 15:30:47,647 - WARNING - 页签 'FestivalActController' 忽略列 O (说明: '类型备注(策划自用)')，没有类型或字段名
2025-05-27 15:30:47,648 - WARNING - 页签 'FestivalAct' 忽略列 H (说明: '备注
策划自用')，没有类型或字段名
2025-05-27 15:30:47,650 - WARNING - 页签 'FestivalQuest' 忽略列 K，没有类型或字段名
2025-05-27 15:30:47,650 - WARNING - 页签 'FestivalQuest' 忽略列 K，没有类型或字段名
2025-05-27 15:30:47,653 - WARNING - 页签 'FestivalQuest' 忽略列 L，没有类型或字段名
2025-05-27 15:30:47,653 - WARNING - 页签 'FestivalQuest' 忽略列 L，没有类型或字段名
2025-05-27 15:30:47,654 - WARNING - 页签 'FestivalQuest' 忽略列 M，没有类型或字段名
2025-05-27 15:30:47,654 - WARNING - 页签 'FestivalQuest' 忽略列 M，没有类型或字段名
2025-05-27 15:30:47,655 - WARNING - 页签 'FestivalQuest' 忽略列 N，没有类型或字段名
2025-05-27 15:30:47,656 - INFO - 为页签 FestivalActSignIn 生成加载函数文件...
2025-05-27 15:30:47,655 - WARNING - 页签 'FestivalQuest' 忽略列 N，没有类型或字段名
2025-05-27 15:30:47,656 - INFO - 为页签 FestivalActSignIn 生成加载函数文件...
2025-05-27 15:30:47,660 - INFO - 为页签 FestivalActController 生成加载函数文件...
2025-05-27 15:30:47,660 - INFO - 为页签 FestivalAct 生成加载函数文件...
2025-05-27 15:30:47,660 - INFO - 为页签 FestivalActController 生成加载函数文件...
2025-05-27 15:30:47,661 - INFO - 服务端加载函数文件 LoadFestivalActSignInServer.txt 生成成功
2025-05-27 15:30:47,660 - INFO - 为页签 FestivalAct 生成加载函数文件...
2025-05-27 15:30:47,662 - INFO - 服务端加载函数文件 LoadFestivalActControllerServer.txt 生成成功
2025-05-27 15:30:47,661 - INFO - 服务端加载函数文件 LoadFestivalActSignInServer.txt 生成成功
2025-05-27 15:30:47,662 - INFO - 服务端加载函数文件 LoadFestivalActControllerServer.txt 生成成功
2025-05-27 15:30:47,664 - INFO - 服务端加载函数文件 LoadFestivalActServer.txt 生成成功
2025-05-27 15:30:47,664 - INFO - 服务端加载函数文件 LoadFestivalActServer.txt 生成成功
2025-05-27 15:30:47,665 - INFO - 客户端加载函数文件 LoadFestivalActSignInClient.txt 生成成功
2025-05-27 15:30:47,666 - INFO - 客户端加载函数文件 LoadFestivalActControllerClient.txt 生成成功
2025-05-27 15:30:47,665 - INFO - 客户端加载函数文件 LoadFestivalActSignInClient.txt 生成成功
2025-05-27 15:30:47,668 - INFO - 客户端加载函数文件 LoadFestivalActClient.txt 生成成功
2025-05-27 15:30:47,666 - INFO - 客户端加载函数文件 LoadFestivalActControllerClient.txt 生成成功
2025-05-27 15:30:47,669 - INFO - 页签 FestivalActSignIn 处理完成
2025-05-27 15:30:47,669 - INFO - 为页签 FestivalQuest 生成加载函数文件...
2025-05-27 15:30:47,668 - INFO - 客户端加载函数文件 LoadFestivalActClient.txt 生成成功
2025-05-27 15:30:47,670 - INFO - 页签 FestivalActController 处理完成
2025-05-27 15:30:47,669 - INFO - 页签 FestivalActSignIn 处理完成
2025-05-27 15:30:47,670 - INFO - 页签 FestivalAct 处理完成
2025-05-27 15:30:47,669 - INFO - 为页签 FestivalQuest 生成加载函数文件...
2025-05-27 15:30:47,671 - WARNING - 页签 'FestivalQuestActive' 忽略列 A (说明: '任务进度奖励表')，没有类型或字段名
2025-05-27 15:30:47,670 - INFO - 页签 FestivalActController 处理完成
2025-05-27 15:30:47,670 - INFO - 页签 FestivalAct 处理完成
2025-05-27 15:30:47,672 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 A (说明: '活动通行证等级')，没有类型或字段名
2025-05-27 15:30:47,671 - WARNING - 页签 'FestivalQuestActive' 忽略列 A (说明: '任务进度奖励表')，没有类型或字段名
2025-05-27 15:30:47,673 - INFO - 服务端加载函数文件 LoadFestivalQuestServer.txt 生成成功
2025-05-27 15:30:47,674 - WARNING - 页签 'FestivalShop' 忽略列 A (说明: '活动商店表')，没有类型或字段名
2025-05-27 15:30:47,672 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 A (说明: '活动通行证等级')，没有类型或字段名
2025-05-27 15:30:47,673 - INFO - 服务端加载函数文件 LoadFestivalQuestServer.txt 生成成功
2025-05-27 15:30:47,674 - WARNING - 页签 'FestivalShop' 忽略列 A (说明: '活动商店表')，没有类型或字段名
2025-05-27 15:30:47,678 - WARNING - 页签 'FestivalShop' 忽略列 O (说明: '备注')，没有类型或字段名
2025-05-27 15:30:47,678 - INFO - 客户端加载函数文件 LoadFestivalQuestClient.txt 生成成功
2025-05-27 15:30:47,678 - WARNING - 页签 'FestivalShop' 忽略列 O (说明: '备注')，没有类型或字段名
2025-05-27 15:30:47,678 - INFO - 客户端加载函数文件 LoadFestivalQuestClient.txt 生成成功
2025-05-27 15:30:47,686 - INFO - 页签 FestivalQuest 处理完成
2025-05-27 15:30:47,686 - INFO - 页签 FestivalQuest 处理完成
2025-05-27 15:30:47,688 - INFO - 为页签 FestivalQuestActive 生成加载函数文件...
2025-05-27 15:30:47,689 - WARNING - 页签 'DragonBoatBoard' 忽略列 A (说明: '棋盘参数总表')，没有类型或字段名
2025-05-27 15:30:47,688 - INFO - 为页签 FestivalQuestActive 生成加载函数文件...
2025-05-27 15:30:47,689 - WARNING - 页签 'DragonBoatBoard' 忽略列 A (说明: '棋盘参数总表')，没有类型或字段名
2025-05-27 15:30:47,694 - INFO - 为页签 FestivalActWarOrderLevel 生成加载函数文件...
2025-05-27 15:30:47,694 - INFO - 服务端加载函数文件 LoadFestivalQuestActiveServer.txt 生成成功
2025-05-27 15:30:47,694 - INFO - 为页签 FestivalActWarOrderLevel 生成加载函数文件...
2025-05-27 15:30:47,694 - INFO - 服务端加载函数文件 LoadFestivalQuestActiveServer.txt 生成成功
2025-05-27 15:30:47,697 - INFO - 服务端加载函数文件 LoadFestivalActWarOrderLevelServer.txt 生成成功
2025-05-27 15:30:47,697 - INFO - 为页签 FestivalShop 生成加载函数文件...
2025-05-27 15:30:47,697 - INFO - 服务端加载函数文件 LoadFestivalActWarOrderLevelServer.txt 生成成功
2025-05-27 15:30:47,697 - INFO - 客户端加载函数文件 LoadFestivalQuestActiveClient.txt 生成成功
2025-05-27 15:30:47,697 - INFO - 为页签 FestivalShop 生成加载函数文件...
2025-05-27 15:30:47,697 - INFO - 客户端加载函数文件 LoadFestivalQuestActiveClient.txt 生成成功
2025-05-27 15:30:47,698 - INFO - 页签 FestivalQuestActive 处理完成
2025-05-27 15:30:47,699 - INFO - 客户端加载函数文件 LoadFestivalActWarOrderLevelClient.txt 生成成功
2025-05-27 15:30:47,698 - INFO - 页签 FestivalQuestActive 处理完成
2025-05-27 15:30:47,699 - INFO - 服务端加载函数文件 LoadFestivalShopServer.txt 生成成功
2025-05-27 15:30:47,699 - INFO - 为页签 DragonBoatBoard 生成加载函数文件...
2025-05-27 15:30:47,699 - INFO - 客户端加载函数文件 LoadFestivalActWarOrderLevelClient.txt 生成成功
2025-05-27 15:30:47,700 - WARNING - 页签 'DragonBoatGrid' 忽略列 A (说明: '龙舟棋盘表')，没有类型或字段名
2025-05-27 15:30:47,699 - INFO - 服务端加载函数文件 LoadFestivalShopServer.txt 生成成功
2025-05-27 15:30:47,701 - INFO - 页签 FestivalActWarOrderLevel 处理完成
2025-05-27 15:30:47,699 - INFO - 为页签 DragonBoatBoard 生成加载函数文件...
2025-05-27 15:30:47,700 - WARNING - 页签 'DragonBoatGrid' 忽略列 A (说明: '龙舟棋盘表')，没有类型或字段名
2025-05-27 15:30:47,701 - INFO - 页签 FestivalActWarOrderLevel 处理完成
2025-05-27 15:30:47,702 - INFO - 客户端加载函数文件 LoadFestivalShopClient.txt 生成成功
2025-05-27 15:30:47,711 - INFO - 服务端加载函数文件 LoadDragonBoatBoardServer.txt 生成成功
2025-05-27 15:30:47,702 - INFO - 客户端加载函数文件 LoadFestivalShopClient.txt 生成成功
2025-05-27 15:30:47,712 - WARNING - 页签 'DragonBoatEvent' 忽略列 A (说明: '龙舟事件表')，没有类型或字段名
2025-05-27 15:30:47,712 - INFO - 页签 FestivalShop 处理完成
2025-05-27 15:30:47,711 - INFO - 服务端加载函数文件 LoadDragonBoatBoardServer.txt 生成成功
2025-05-27 15:30:47,712 - WARNING - 页签 'DragonBoatEvent' 忽略列 A (说明: '龙舟事件表')，没有类型或字段名
2025-05-27 15:30:47,712 - INFO - 页签 FestivalShop 处理完成
2025-05-27 15:30:47,720 - WARNING - 页签 'DragonBoatEvent' 忽略列 I (说明: '自用备注')，没有类型或字段名
2025-05-27 15:30:47,722 - WARNING - 页签 'DragonBoatRankReward' 忽略列 A (说明: '排名档位表')，没有类型或字段名
2025-05-27 15:30:47,720 - WARNING - 页签 'DragonBoatEvent' 忽略列 I (说明: '自用备注')，没有类型或字段名
2025-05-27 15:30:47,722 - WARNING - 页签 'DragonBoatRankReward' 忽略列 A (说明: '排名档位表')，没有类型或字段名
2025-05-27 15:30:47,728 - INFO - 客户端加载函数文件 LoadDragonBoatBoardClient.txt 生成成功
2025-05-27 15:30:47,730 - WARNING - 页签 'DragonBoatEvent' 忽略列 J (说明: '自用备注')，没有类型或字段名
2025-05-27 15:30:47,728 - INFO - 客户端加载函数文件 LoadDragonBoatBoardClient.txt 生成成功
2025-05-27 15:30:47,730 - WARNING - 页签 'DragonBoatEvent' 忽略列 J (说明: '自用备注')，没有类型或字段名
2025-05-27 15:30:47,731 - INFO - 页签 DragonBoatBoard 处理完成
2025-05-27 15:30:47,732 - WARNING - 页签 'DragonBoatEvent' 忽略列 K (说明: '自用备注')，没有类型或字段名
2025-05-27 15:30:47,731 - INFO - 页签 DragonBoatBoard 处理完成
2025-05-27 15:30:47,732 - WARNING - 页签 'DragonBoatEvent' 忽略列 K (说明: '自用备注')，没有类型或字段名
2025-05-27 15:30:47,748 - INFO - 为页签 DragonBoatGrid 生成加载函数文件...
2025-05-27 15:30:47,748 - INFO - 为页签 DragonBoatGrid 生成加载函数文件...
2025-05-27 15:30:47,752 - INFO - 为页签 DragonBoatRankReward 生成加载函数文件...
2025-05-27 15:30:47,752 - INFO - 为页签 DragonBoatRankReward 生成加载函数文件...
2025-05-27 15:30:47,753 - INFO - 服务端加载函数文件 LoadDragonBoatGridServer.txt 生成成功
2025-05-27 15:30:47,753 - INFO - 服务端加载函数文件 LoadDragonBoatGridServer.txt 生成成功
2025-05-27 15:30:47,753 - INFO - 服务端加载函数文件 LoadDragonBoatRankRewardServer.txt 生成成功
2025-05-27 15:30:47,755 - INFO - 为页签 FestivalZongzi 生成加载函数文件...
2025-05-27 15:30:47,753 - INFO - 服务端加载函数文件 LoadDragonBoatRankRewardServer.txt 生成成功
2025-05-27 15:30:47,757 - INFO - 为页签 DragonBoatEvent 生成加载函数文件...
2025-05-27 15:30:47,758 - INFO - 客户端加载函数文件 LoadDragonBoatGridClient.txt 生成成功
2025-05-27 15:30:47,755 - INFO - 为页签 FestivalZongzi 生成加载函数文件...
2025-05-27 15:30:47,757 - INFO - 为页签 DragonBoatEvent 生成加载函数文件...
2025-05-27 15:30:47,758 - INFO - 客户端加载函数文件 LoadDragonBoatRankRewardClient.txt 生成成功
2025-05-27 15:30:47,758 - INFO - 客户端加载函数文件 LoadDragonBoatGridClient.txt 生成成功
2025-05-27 15:30:47,758 - INFO - 客户端加载函数文件 LoadDragonBoatRankRewardClient.txt 生成成功
2025-05-27 15:30:47,759 - INFO - 服务端加载函数文件 LoadFestivalZongziServer.txt 生成成功
2025-05-27 15:30:47,759 - INFO - 页签 DragonBoatGrid 处理完成
2025-05-27 15:30:47,759 - INFO - 服务端加载函数文件 LoadDragonBoatEventServer.txt 生成成功
2025-05-27 15:30:47,759 - INFO - 服务端加载函数文件 LoadFestivalZongziServer.txt 生成成功
2025-05-27 15:30:47,760 - INFO - 页签 DragonBoatRankReward 处理完成
2025-05-27 15:30:47,759 - INFO - 页签 DragonBoatGrid 处理完成
2025-05-27 15:30:47,759 - INFO - 服务端加载函数文件 LoadDragonBoatEventServer.txt 生成成功
2025-05-27 15:30:47,760 - INFO - 页签 DragonBoatRankReward 处理完成
2025-05-27 15:30:47,760 - INFO - 客户端加载函数文件 LoadFestivalZongziClient.txt 生成成功
2025-05-27 15:30:47,760 - INFO - 客户端加载函数文件 LoadFestivalZongziClient.txt 生成成功
2025-05-27 15:30:47,761 - INFO - 页签 FestivalZongzi 处理完成
2025-05-27 15:30:47,761 - INFO - 客户端加载函数文件 LoadDragonBoatEventClient.txt 生成成功
2025-05-27 15:30:47,761 - INFO - 页签 FestivalZongzi 处理完成
2025-05-27 15:30:47,761 - INFO - 客户端加载函数文件 LoadDragonBoatEventClient.txt 生成成功
2025-05-27 15:30:47,762 - INFO - 页签 DragonBoatEvent 处理完成
2025-05-27 15:30:47,762 - INFO - 页签 DragonBoatEvent 处理完成
2025-05-27 15:30:47,772 - INFO - 导出完成! 成功: 2, 跳过: 0, 失败: 0
2025-05-27 15:30:47,772 - INFO - 导出完成! 成功: 2, 跳过: 0, 失败: 0
2025-05-27 15:30:47,781 - INFO - 找到 26 个SData文件 (服务端: 13, 客户端: 13)
2025-05-27 15:30:47,781 - INFO - 找到 26 个SData文件 (服务端: 13, 客户端: 13)
2025-05-27 15:30:47,783 - INFO - 找到 26 个加载函数文件
2025-05-27 15:30:47,783 - INFO - 找到 26 个加载函数文件
