BOOL CGameData::LoadDragonBoatRankRewardServerCfg()
{
    std::string DataPath = "data/DragonBoatRankRewardServer.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        ::g_pClientToLog->PrintFileDirct("CDataFile::LoadDragonBoatRankRewardCfg fopen error");
        return FALSE;
    }
    
    m_mapDragonBoatRankRewardCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stDragonBoatRankRewardServerCfg cfg;
        fread(&cfg, sizeof(stDragonBoatRankRewardServerCfg), 1, fp);

        m_mapDragonBoatRankRewardCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}