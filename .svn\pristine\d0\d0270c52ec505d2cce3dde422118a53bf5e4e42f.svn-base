数据条数: 40
dwGridId (uint):1
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):1
szTest (char[36]):"12312"
szTest2 (char[2][36]):['', '']

dwGridId (uint):2
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):2
szTest (char[36]):"df"
szTest2 (char[2][36]):['', '']

dwGridId (uint):3
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):3
szTest (char[36]):"gg"
szTest2 (char[2][36]):['', '']

dwGridId (uint):4
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):4
szTest (char[36]):"0"
szTest2 (char[2][36]):['', '']

dwGridId (uint):5
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):d
szTest (char[36]):"11"
szTest2 (char[2][36]):['', '']

dwGridId (uint):6
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):6
szTest (char[36]):"ddd"
szTest2 (char[2][36]):['', '']

dwGridId (uint):7
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):7
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):8
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):8
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):9
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):9
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):10
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):10
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):11
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):11
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):12
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):12
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):13
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):13
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):14
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):14
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):15
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):15
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):16
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):16
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):17
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):17
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):18
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):18
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):19
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):19
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):20
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):20
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):21
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):21
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):22
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):22
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):23
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):23
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):24
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):24
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):25
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):25
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):26
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):26
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):27
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):27
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):28
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):28
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):29
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):29
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):30
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):30
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):31
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):31
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):32
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):32
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):33
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):33
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):34
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):34
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):35
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):35
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):36
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):36
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):37
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):37
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):38
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):38
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):39
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):39
szTest (char[36]):
szTest2 (char[2][36]):['', '']

dwGridId (uint):40
dwBoardId (uint):1005
dwNextId (uint):0
dwRefreshId (uint):40
szTest (char[36]):
szTest2 (char[2][36]):['', '']

