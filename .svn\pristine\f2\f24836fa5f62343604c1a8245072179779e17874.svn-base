2025-05-06 16:01:26,066 - INFO - 正在扫描路径: C:/MyFileAudit/导表工具/config
2025-05-06 16:01:26,066 - INFO - 正在扫描路径: C:/MyFileAudit/导表工具/config
2025-05-06 16:01:26,067 - INFO - 找到 1 个Excel配置文件
2025-05-06 16:01:26,067 - INFO - 找到 1 个Excel配置文件
2025-05-06 16:01:28,810 - INFO - 开始导出全部 1 个文件...
2025-05-06 16:01:28,810 - INFO - 开始导出全部 1 个文件...
2025-05-06 16:01:28,822 - INFO - 开始处理: DragonBoatFestival.xlsx
2025-05-06 16:01:28,822 - INFO - 开始处理: DragonBoatFestival.xlsx
2025-05-06 16:01:28,869 - WARNING - 页签 'DragonBoatGrid' 忽略列 S，因为该列缺少类型或字段名
2025-05-06 16:01:28,869 - WARNING - 页签 'DragonBoatGrid' 忽略列 S，因为该列缺少类型或字段名
2025-05-06 16:01:28,871 - WARNING - 页签 'DragonBoatGrid' 忽略列 T，因为该列缺少类型或字段名
2025-05-06 16:01:28,871 - WARNING - 页签 'DragonBoatGrid' 忽略列 T，因为该列缺少类型或字段名
2025-05-06 16:01:28,872 - WARNING - {None}
2025-05-06 16:01:28,872 - WARNING - {None}
2025-05-06 16:01:28,874 - WARNING - {None}
2025-05-06 16:01:28,874 - WARNING - {None}
2025-05-06 16:01:28,876 - WARNING - {None}
2025-05-06 16:01:28,876 - WARNING - {None}
2025-05-06 16:01:28,877 - WARNING - {None}
2025-05-06 16:01:28,877 - WARNING - {None}
2025-05-06 16:01:28,882 - WARNING - 表格 'DragonBoatEvent' 记录 #7 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,882 - WARNING - 表格 'DragonBoatEvent' 记录 #7 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,884 - WARNING - {None}
2025-05-06 16:01:28,885 - WARNING - 表格 'DragonBoatEvent' 记录 #8 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,884 - WARNING - {None}
2025-05-06 16:01:28,885 - WARNING - 表格 'DragonBoatEvent' 记录 #8 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,887 - WARNING - {None}
2025-05-06 16:01:28,887 - WARNING - {None}
2025-05-06 16:01:28,888 - WARNING - 表格 'DragonBoatEvent' 记录 #9 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,890 - WARNING - {None}
2025-05-06 16:01:28,888 - WARNING - 表格 'DragonBoatEvent' 记录 #9 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,890 - WARNING - {None}
2025-05-06 16:01:28,892 - WARNING - {None}
2025-05-06 16:01:28,892 - WARNING - {None}
2025-05-06 16:01:28,893 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,893 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,895 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,895 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,896 - WARNING - {None}
2025-05-06 16:01:28,896 - WARNING - {None}
2025-05-06 16:01:28,897 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,900 - INFO - 页签 FestivalQuestActive 处理完成
2025-05-06 16:01:28,897 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,900 - INFO - 页签 FestivalQuestActive 处理完成
2025-05-06 16:01:28,901 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,903 - INFO - 页签 CurrencySystem 处理完成
2025-05-06 16:01:28,901 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,903 - INFO - 页签 CurrencySystem 处理完成
2025-05-06 16:01:28,906 - WARNING - {None}
2025-05-06 16:01:28,906 - WARNING - {None}
2025-05-06 16:01:28,908 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,908 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,910 - WARNING - 表格 'DragonBoatEvent' 记录 #7 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,911 - WARNING - {None}
2025-05-06 16:01:28,910 - WARNING - 表格 'DragonBoatEvent' 记录 #7 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,911 - WARNING - {None}
2025-05-06 16:01:28,912 - WARNING - 表格 'DragonBoatEvent' 记录 #8 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,913 - WARNING - 页签 'DragonBoatGrid' 记录 #6 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:28,912 - WARNING - 表格 'DragonBoatEvent' 记录 #8 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,913 - WARNING - 页签 'DragonBoatGrid' 记录 #6 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:28,915 - WARNING - 表格 'DragonBoatRankReward' 记录 #1 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,915 - WARNING - 表格 'DragonBoatEvent' 记录 #9 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,915 - WARNING - 表格 'DragonBoatRankReward' 记录 #1 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,915 - WARNING - 表格 'DragonBoatEvent' 记录 #9 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,916 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,917 - WARNING - 表格 'DragonBoatRankReward' 记录 #2 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,917 - WARNING - 表格 'DragonBoatRankReward' 记录 #2 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,919 - WARNING - {None}
2025-05-06 16:01:28,919 - WARNING - 表格 'DragonBoatRankReward' 记录 #3 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,919 - WARNING - {None}
2025-05-06 16:01:28,919 - WARNING - 表格 'DragonBoatRankReward' 记录 #3 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,921 - WARNING - {None}
2025-05-06 16:01:28,922 - INFO - 页签 DragonBoatEvent 处理完成
2025-05-06 16:01:28,921 - WARNING - {None}
2025-05-06 16:01:28,922 - WARNING - 表格 'DragonBoatRankReward' 记录 #4 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,922 - INFO - 页签 DragonBoatBoard 处理完成
2025-05-06 16:01:28,922 - INFO - 页签 DragonBoatEvent 处理完成
2025-05-06 16:01:28,923 - WARNING - {None}
2025-05-06 16:01:28,922 - WARNING - 表格 'DragonBoatRankReward' 记录 #4 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,922 - INFO - 页签 DragonBoatBoard 处理完成
2025-05-06 16:01:28,925 - WARNING - 页签 'FestivalAct' 忽略列 G (说明: '备注
策划自用')，因为该列缺少类型或字段名
2025-05-06 16:01:28,923 - WARNING - {None}
2025-05-06 16:01:28,926 - WARNING - 表格 'DragonBoatRankReward' 记录 #5 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,925 - WARNING - 页签 'FestivalAct' 忽略列 G (说明: '备注
策划自用')，因为该列缺少类型或字段名
2025-05-06 16:01:28,927 - WARNING - 页签 'FestivalActController' 忽略列 M (说明: '类型备注(策划自用)')，因为该列缺少类型或字段名
2025-05-06 16:01:28,926 - WARNING - 表格 'DragonBoatRankReward' 记录 #5 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,928 - WARNING - {None}
2025-05-06 16:01:28,927 - WARNING - 页签 'FestivalActController' 忽略列 M (说明: '类型备注(策划自用)')，因为该列缺少类型或字段名
2025-05-06 16:01:28,928 - WARNING - {None}
2025-05-06 16:01:28,929 - WARNING - 表格 'DragonBoatRankReward' 记录 #6 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,929 - WARNING - 表格 'DragonBoatRankReward' 记录 #6 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,931 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwPreviewStartTime' 字段的值 '2025-05-01 12:34:56' 不是有效的数值
2025-05-06 16:01:28,931 - WARNING - {None}
2025-05-06 16:01:28,931 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwPreviewStartTime' 字段的值 '2025-05-01 12:34:56' 不是有效的数值
2025-05-06 16:01:28,932 - WARNING - 表格 'DragonBoatRankReward' 记录 #7 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,931 - WARNING - {None}
2025-05-06 16:01:28,933 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwStartTime' 字段的值 '2025-05-02 12:34:56' 不是有效的数值
2025-05-06 16:01:28,932 - WARNING - 表格 'DragonBoatRankReward' 记录 #7 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,934 - WARNING - {None}
2025-05-06 16:01:28,933 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwStartTime' 字段的值 '2025-05-02 12:34:56' 不是有效的数值
2025-05-06 16:01:28,934 - WARNING - {None}
2025-05-06 16:01:28,935 - WARNING - 表格 'DragonBoatRankReward' 记录 #8 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,936 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwEndTime' 字段的值 '2025-05-03 12:34:56' 不是有效的数值
2025-05-06 16:01:28,935 - WARNING - 表格 'DragonBoatRankReward' 记录 #8 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,937 - WARNING - {None}
2025-05-06 16:01:28,936 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwEndTime' 字段的值 '2025-05-03 12:34:56' 不是有效的数值
2025-05-06 16:01:28,938 - WARNING - 表格 'DragonBoatRankReward' 记录 #9 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,937 - WARNING - {None}
2025-05-06 16:01:28,940 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwRewardEndTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:01:28,938 - WARNING - 表格 'DragonBoatRankReward' 记录 #9 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,941 - WARNING - {None}
2025-05-06 16:01:28,940 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwRewardEndTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:01:28,942 - WARNING - 表格 'DragonBoatRankReward' 记录 #10 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,941 - WARNING - {None}
2025-05-06 16:01:28,943 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwPreviewStartTime' 字段的值 '2025-05-02 12:34:56' 不是有效的数值
2025-05-06 16:01:28,942 - WARNING - 表格 'DragonBoatRankReward' 记录 #10 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,944 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,943 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwPreviewStartTime' 字段的值 '2025-05-02 12:34:56' 不是有效的数值
2025-05-06 16:01:28,945 - INFO - 页签 FestivalAct 处理完成
2025-05-06 16:01:28,945 - WARNING - 表格 'DragonBoatRankReward' 记录 #11 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,944 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,946 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwStartTime' 字段的值 '2025-05-03 12:34:56' 不是有效的数值
2025-05-06 16:01:28,945 - INFO - 页签 FestivalAct 处理完成
2025-05-06 16:01:28,946 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,945 - WARNING - 表格 'DragonBoatRankReward' 记录 #11 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,947 - WARNING - 页签 'FestivalShop' 忽略列 N (说明: '备注')，因为该列缺少类型或字段名
2025-05-06 16:01:28,946 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwStartTime' 字段的值 '2025-05-03 12:34:56' 不是有效的数值
2025-05-06 16:01:28,948 - WARNING - 表格 'DragonBoatRankReward' 记录 #12 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,946 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,949 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwEndTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:01:28,947 - WARNING - 页签 'FestivalShop' 忽略列 N (说明: '备注')，因为该列缺少类型或字段名
2025-05-06 16:01:28,950 - WARNING - {None}
2025-05-06 16:01:28,948 - WARNING - 表格 'DragonBoatRankReward' 记录 #12 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,950 - WARNING - 页签 'FestivalShop' 记录 #6 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:01:28,949 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwEndTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:01:28,951 - WARNING - 表格 'DragonBoatRankReward' 记录 #13 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,950 - WARNING - {None}
2025-05-06 16:01:28,952 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwRewardEndTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:01:28,950 - WARNING - 页签 'FestivalShop' 记录 #6 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:01:28,951 - WARNING - 表格 'DragonBoatRankReward' 记录 #13 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,953 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,952 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwRewardEndTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:01:28,954 - WARNING - 页签 'FestivalShop' 记录 #6 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:01:28,955 - WARNING - 表格 'DragonBoatRankReward' 记录 #14 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,953 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,956 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwPreviewStartTime' 字段的值 '2025-05-03 12:34:56' 不是有效的数值
2025-05-06 16:01:28,954 - WARNING - 页签 'FestivalShop' 记录 #6 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:01:28,956 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,955 - WARNING - 表格 'DragonBoatRankReward' 记录 #14 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,957 - WARNING - 页签 'FestivalShop' 记录 #7 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:01:28,956 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwPreviewStartTime' 字段的值 '2025-05-03 12:34:56' 不是有效的数值
2025-05-06 16:01:28,958 - WARNING - 表格 'DragonBoatRankReward' 记录 #15 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,956 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,959 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwStartTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:01:28,957 - WARNING - 页签 'FestivalShop' 记录 #7 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:01:28,960 - WARNING - {None}
2025-05-06 16:01:28,958 - WARNING - 表格 'DragonBoatRankReward' 记录 #15 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,961 - WARNING - 页签 'FestivalShop' 记录 #7 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:01:28,959 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwStartTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:01:28,961 - WARNING - 表格 'DragonBoatRankReward' 记录 #16 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,960 - WARNING - {None}
2025-05-06 16:01:28,962 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwEndTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:01:28,961 - WARNING - 页签 'FestivalShop' 记录 #7 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:01:28,963 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,961 - WARNING - 表格 'DragonBoatRankReward' 记录 #16 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,964 - WARNING - 页签 'FestivalShop' 记录 #8 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:01:28,962 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwEndTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:01:28,964 - WARNING - 表格 'DragonBoatRankReward' 记录 #17 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,963 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,965 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwRewardEndTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:01:28,964 - WARNING - 页签 'FestivalShop' 记录 #8 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:01:28,966 - WARNING - {None}
2025-05-06 16:01:28,964 - WARNING - 表格 'DragonBoatRankReward' 记录 #17 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,967 - WARNING - 页签 'FestivalShop' 记录 #8 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:01:28,965 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwRewardEndTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:01:28,967 - WARNING - 表格 'DragonBoatRankReward' 记录 #18 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,966 - WARNING - {None}
2025-05-06 16:01:28,968 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwPreviewStartTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:01:28,969 - WARNING - 页签 'DragonBoatGrid' 记录 #7 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:28,967 - WARNING - 页签 'FestivalShop' 记录 #8 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:01:28,967 - WARNING - 表格 'DragonBoatRankReward' 记录 #18 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,970 - WARNING - 页签 'FestivalShop' 记录 #9 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:01:28,968 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwPreviewStartTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:01:28,969 - WARNING - 页签 'DragonBoatGrid' 记录 #7 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:28,970 - WARNING - 页签 'FestivalShop' 记录 #9 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:01:28,972 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwStartTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:01:28,972 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,972 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwStartTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:01:28,973 - WARNING - 页签 'FestivalShop' 记录 #9 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:01:28,972 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:28,974 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwEndTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:01:28,973 - WARNING - 页签 'FestivalShop' 记录 #9 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:01:28,974 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwEndTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:01:28,975 - WARNING - {None}
2025-05-06 16:01:28,975 - WARNING - {None}
2025-05-06 16:01:28,976 - WARNING - 页签 'FestivalShop' 记录 #10 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:01:28,977 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwRewardEndTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:01:28,976 - WARNING - 页签 'FestivalShop' 记录 #10 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:01:28,978 - WARNING - {None}
2025-05-06 16:01:28,977 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwRewardEndTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:01:28,979 - WARNING - 页签 'FestivalShop' 记录 #10 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:01:28,979 - WARNING - 表格 'DragonBoatRankReward' 记录 #1 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,978 - WARNING - {None}
2025-05-06 16:01:28,980 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwPreviewStartTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:01:28,979 - WARNING - 页签 'FestivalShop' 记录 #10 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:01:28,979 - WARNING - 表格 'DragonBoatRankReward' 记录 #1 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,981 - WARNING - {None}
2025-05-06 16:01:28,980 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwPreviewStartTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:01:28,982 - WARNING - 页签 'FestivalShop' 记录 #11 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:01:28,981 - WARNING - {None}
2025-05-06 16:01:28,983 - WARNING - 表格 'DragonBoatRankReward' 记录 #2 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,983 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwStartTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:01:28,982 - WARNING - 页签 'FestivalShop' 记录 #11 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:01:28,984 - WARNING - {None}
2025-05-06 16:01:28,983 - WARNING - 表格 'DragonBoatRankReward' 记录 #2 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,985 - WARNING - 页签 'FestivalShop' 记录 #11 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:01:28,983 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwStartTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:01:28,986 - WARNING - 表格 'DragonBoatRankReward' 记录 #3 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,984 - WARNING - {None}
2025-05-06 16:01:28,987 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwEndTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:01:28,985 - WARNING - 页签 'FestivalShop' 记录 #11 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:01:28,987 - WARNING - {None}
2025-05-06 16:01:28,986 - WARNING - 表格 'DragonBoatRankReward' 记录 #3 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,988 - WARNING - 页签 'FestivalShop' 记录 #12 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:01:28,987 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwEndTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:01:28,989 - WARNING - 表格 'DragonBoatRankReward' 记录 #4 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,987 - WARNING - {None}
2025-05-06 16:01:28,990 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwRewardEndTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:01:28,988 - WARNING - 页签 'FestivalShop' 记录 #12 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:01:28,989 - WARNING - 表格 'DragonBoatRankReward' 记录 #4 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,991 - WARNING - {None}
2025-05-06 16:01:28,990 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwRewardEndTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:01:28,991 - WARNING - 页签 'FestivalShop' 记录 #12 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:01:28,991 - WARNING - {None}
2025-05-06 16:01:28,992 - WARNING - 表格 'DragonBoatRankReward' 记录 #5 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,991 - WARNING - 页签 'FestivalShop' 记录 #12 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:01:28,993 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwPreviewStartTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:01:28,992 - WARNING - 表格 'DragonBoatRankReward' 记录 #5 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,994 - WARNING - {None}
2025-05-06 16:01:28,993 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwPreviewStartTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:01:28,995 - WARNING - 页签 'FestivalShop' 记录 #13 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:01:28,994 - WARNING - {None}
2025-05-06 16:01:28,996 - WARNING - 表格 'DragonBoatRankReward' 记录 #6 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,995 - WARNING - 页签 'FestivalShop' 记录 #13 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:01:28,996 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwStartTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:01:28,996 - WARNING - 表格 'DragonBoatRankReward' 记录 #6 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,997 - WARNING - {None}
2025-05-06 16:01:28,996 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwStartTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:01:28,998 - WARNING - 页签 'FestivalShop' 记录 #13 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:01:28,997 - WARNING - {None}
2025-05-06 16:01:28,999 - WARNING - 表格 'DragonBoatRankReward' 记录 #7 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:28,998 - WARNING - 页签 'FestivalShop' 记录 #13 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:01:29,000 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwEndTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:01:28,999 - WARNING - 表格 'DragonBoatRankReward' 记录 #7 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,000 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,000 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwEndTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:01:29,002 - WARNING - 表格 'DragonBoatRankReward' 记录 #8 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,000 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,002 - WARNING - 表格 'DragonBoatRankReward' 记录 #8 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,002 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwRewardEndTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:01:29,003 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,002 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwRewardEndTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:01:29,004 - WARNING - 表格 'DragonBoatRankReward' 记录 #9 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,003 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,005 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwPreviewStartTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:01:29,004 - WARNING - 表格 'DragonBoatRankReward' 记录 #9 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,006 - WARNING - {None}
2025-05-06 16:01:29,005 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwPreviewStartTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:01:29,007 - WARNING - 表格 'DragonBoatRankReward' 记录 #10 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,006 - WARNING - {None}
2025-05-06 16:01:29,008 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwStartTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:01:29,007 - WARNING - 表格 'DragonBoatRankReward' 记录 #10 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,009 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,008 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwStartTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:01:29,010 - WARNING - 表格 'DragonBoatRankReward' 记录 #11 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,010 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwEndTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:01:29,009 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,010 - WARNING - 表格 'DragonBoatRankReward' 记录 #11 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,011 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,010 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwEndTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:01:29,013 - WARNING - 表格 'DragonBoatRankReward' 记录 #12 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,011 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,013 - WARNING - 表格 'DragonBoatRankReward' 记录 #12 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,014 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwRewardEndTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:01:29,015 - WARNING - {None}
2025-05-06 16:01:29,014 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwRewardEndTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:01:29,019 - WARNING - 表格 'DragonBoatRankReward' 记录 #13 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,015 - WARNING - {None}
2025-05-06 16:01:29,020 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwPreviewStartTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:01:29,019 - WARNING - 表格 'DragonBoatRankReward' 记录 #13 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,021 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,020 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwPreviewStartTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:01:29,022 - WARNING - 表格 'DragonBoatRankReward' 记录 #14 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,021 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,023 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwStartTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:01:29,022 - WARNING - 表格 'DragonBoatRankReward' 记录 #14 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,024 - WARNING - {None}
2025-05-06 16:01:29,023 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwStartTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:01:29,025 - WARNING - 表格 'DragonBoatRankReward' 记录 #15 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,024 - WARNING - {None}
2025-05-06 16:01:29,026 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwEndTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:01:29,025 - WARNING - 表格 'DragonBoatRankReward' 记录 #15 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,028 - WARNING - 页签 'DragonBoatGrid' 记录 #8 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,026 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwEndTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:01:29,029 - WARNING - 表格 'DragonBoatRankReward' 记录 #16 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,028 - WARNING - 页签 'DragonBoatGrid' 记录 #8 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,030 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwRewardEndTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:01:29,029 - WARNING - 表格 'DragonBoatRankReward' 记录 #16 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,031 - INFO - 页签 FestivalShop 处理完成
2025-05-06 16:01:29,031 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,030 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwRewardEndTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:01:29,032 - WARNING - 表格 'DragonBoatRankReward' 记录 #17 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,031 - INFO - 页签 FestivalShop 处理完成
2025-05-06 16:01:29,032 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwPreviewStartTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:01:29,031 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,032 - WARNING - 表格 'DragonBoatRankReward' 记录 #17 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,034 - WARNING - {None}
2025-05-06 16:01:29,032 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwPreviewStartTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:01:29,035 - WARNING - 表格 'DragonBoatRankReward' 记录 #18 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,034 - WARNING - {None}
2025-05-06 16:01:29,036 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwStartTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:01:29,035 - WARNING - 表格 'DragonBoatRankReward' 记录 #18 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:01:29,037 - WARNING - {None}
2025-05-06 16:01:29,036 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwStartTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:01:29,037 - WARNING - {None}
2025-05-06 16:01:29,038 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwEndTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:01:29,039 - WARNING - {None}
2025-05-06 16:01:29,038 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwEndTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:01:29,039 - WARNING - {None}
2025-05-06 16:01:29,040 - WARNING - 表格 'FestivalActSignIn' 记录 #1 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,040 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwRewardEndTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:01:29,041 - WARNING - {None}
2025-05-06 16:01:29,040 - WARNING - 表格 'FestivalActSignIn' 记录 #1 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,041 - INFO - 页签 DragonBoatRankReward 处理完成
2025-05-06 16:01:29,040 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwRewardEndTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:01:29,042 - WARNING - 表格 'FestivalActSignIn' 记录 #3 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,041 - WARNING - {None}
2025-05-06 16:01:29,043 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwPreviewStartTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:01:29,041 - INFO - 页签 DragonBoatRankReward 处理完成
2025-05-06 16:01:29,043 - WARNING - {None}
2025-05-06 16:01:29,042 - WARNING - 表格 'FestivalActSignIn' 记录 #3 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,043 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwPreviewStartTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:01:29,043 - WARNING - {None}
2025-05-06 16:01:29,045 - WARNING - 表格 'FestivalActSignIn' 记录 #5 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,046 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwStartTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:01:29,045 - WARNING - 表格 'FestivalActSignIn' 记录 #5 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,047 - WARNING - {None}
2025-05-06 16:01:29,046 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwStartTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:01:29,048 - WARNING - 表格 'FestivalActSignIn' 记录 #7 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,047 - WARNING - {None}
2025-05-06 16:01:29,048 - WARNING - 表格 'FestivalActSignIn' 记录 #7 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,049 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwEndTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:01:29,049 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwEndTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:01:29,051 - WARNING - {None}
2025-05-06 16:01:29,052 - WARNING - 表格 'FestivalActSignIn' 记录 #9 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,052 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwRewardEndTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:01:29,051 - WARNING - {None}
2025-05-06 16:01:29,052 - WARNING - 表格 'FestivalActSignIn' 记录 #9 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,053 - WARNING - {None}
2025-05-06 16:01:29,052 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwRewardEndTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:01:29,054 - WARNING - 表格 'FestivalActSignIn' 记录 #11 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,053 - WARNING - {None}
2025-05-06 16:01:29,055 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwPreviewStartTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:01:29,054 - WARNING - 表格 'FestivalActSignIn' 记录 #11 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,056 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,055 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwPreviewStartTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:01:29,056 - WARNING - 表格 'FestivalActSignIn' 记录 #13 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,056 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,057 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwStartTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:01:29,056 - WARNING - 表格 'FestivalActSignIn' 记录 #13 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,060 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,057 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwStartTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:01:29,061 - WARNING - 表格 'FestivalActSignIn' 记录 #15 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,060 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,062 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwEndTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:01:29,061 - WARNING - 表格 'FestivalActSignIn' 记录 #15 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,062 - INFO - 页签 FestivalQuest 处理完成
2025-05-06 16:01:29,062 - WARNING - {None}
2025-05-06 16:01:29,062 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwEndTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:01:29,063 - WARNING - 表格 'FestivalActSignIn' 记录 #17 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,062 - INFO - 页签 FestivalQuest 处理完成
2025-05-06 16:01:29,062 - WARNING - {None}
2025-05-06 16:01:29,064 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwRewardEndTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:01:29,063 - WARNING - 表格 'FestivalActSignIn' 记录 #17 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,066 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 I，因为该列缺少类型或字段名
2025-05-06 16:01:29,064 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwRewardEndTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:01:29,066 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,066 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 I，因为该列缺少类型或字段名
2025-05-06 16:01:29,067 - WARNING - 表格 'FestivalActSignIn' 记录 #19 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,066 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,068 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwPreviewStartTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:01:29,067 - WARNING - 表格 'FestivalActSignIn' 记录 #19 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,069 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 J，因为该列缺少类型或字段名
2025-05-06 16:01:29,068 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwPreviewStartTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:01:29,069 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,069 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 J，因为该列缺少类型或字段名
2025-05-06 16:01:29,069 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,070 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwStartTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:01:29,071 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 K，因为该列缺少类型或字段名
2025-05-06 16:01:29,070 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwStartTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:01:29,071 - WARNING - {None}
2025-05-06 16:01:29,071 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 K，因为该列缺少类型或字段名
2025-05-06 16:01:29,072 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwEndTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:01:29,071 - WARNING - {None}
2025-05-06 16:01:29,073 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #6 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,072 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwEndTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:01:29,074 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,073 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #6 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,075 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwRewardEndTime' 字段的值 '2025-05-15 12:34:56' 不是有效的数值
2025-05-06 16:01:29,074 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,076 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #6 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,075 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwRewardEndTime' 字段的值 '2025-05-15 12:34:56' 不是有效的数值
2025-05-06 16:01:29,076 - WARNING - {None}
2025-05-06 16:01:29,076 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #6 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,077 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwPreviewStartTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:01:29,078 - WARNING - 表格 'FestivalActSignIn' 记录 #1 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,076 - WARNING - {None}
2025-05-06 16:01:29,079 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #7 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,077 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwPreviewStartTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:01:29,078 - WARNING - 表格 'FestivalActSignIn' 记录 #1 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,080 - WARNING - 页签 'DragonBoatGrid' 记录 #9 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,079 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #7 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,081 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwStartTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:01:29,080 - WARNING - 页签 'DragonBoatGrid' 记录 #9 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,081 - WARNING - 表格 'FestivalActSignIn' 记录 #3 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,081 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwStartTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:01:29,082 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #7 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,081 - WARNING - 表格 'FestivalActSignIn' 记录 #3 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,083 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,082 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #7 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,084 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwEndTime' 字段的值 '2025-05-15 12:34:56' 不是有效的数值
2025-05-06 16:01:29,085 - WARNING - 表格 'FestivalActSignIn' 记录 #5 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,083 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,085 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #8 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,084 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwEndTime' 字段的值 '2025-05-15 12:34:56' 不是有效的数值
2025-05-06 16:01:29,086 - WARNING - {None}
2025-05-06 16:01:29,085 - WARNING - 表格 'FestivalActSignIn' 记录 #5 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,087 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwRewardEndTime' 字段的值 '2025-05-16 12:34:56' 不是有效的数值
2025-05-06 16:01:29,085 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #8 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,088 - WARNING - 表格 'FestivalActSignIn' 记录 #7 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,086 - WARNING - {None}
2025-05-06 16:01:29,089 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #8 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,087 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwRewardEndTime' 字段的值 '2025-05-16 12:34:56' 不是有效的数值
2025-05-06 16:01:29,089 - WARNING - {None}
2025-05-06 16:01:29,088 - WARNING - 表格 'FestivalActSignIn' 记录 #7 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,090 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwPreviewStartTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:01:29,089 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #8 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,089 - WARNING - {None}
2025-05-06 16:01:29,091 - WARNING - 表格 'FestivalActSignIn' 记录 #9 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,090 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwPreviewStartTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:01:29,091 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #9 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,091 - WARNING - 表格 'FestivalActSignIn' 记录 #9 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,091 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #9 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,092 - WARNING - {None}
2025-05-06 16:01:29,093 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwStartTime' 字段的值 '2025-05-15 12:34:56' 不是有效的数值
2025-05-06 16:01:29,093 - WARNING - 表格 'FestivalActSignIn' 记录 #11 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,092 - WARNING - {None}
2025-05-06 16:01:29,094 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #9 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,093 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwStartTime' 字段的值 '2025-05-15 12:34:56' 不是有效的数值
2025-05-06 16:01:29,095 - WARNING - {None}
2025-05-06 16:01:29,093 - WARNING - 表格 'FestivalActSignIn' 记录 #11 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,096 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwEndTime' 字段的值 '2025-05-16 12:34:56' 不是有效的数值
2025-05-06 16:01:29,094 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #9 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,097 - WARNING - 表格 'FestivalActSignIn' 记录 #13 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,095 - WARNING - {None}
2025-05-06 16:01:29,097 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #10 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,096 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwEndTime' 字段的值 '2025-05-16 12:34:56' 不是有效的数值
2025-05-06 16:01:29,098 - WARNING - {None}
2025-05-06 16:01:29,097 - WARNING - 表格 'FestivalActSignIn' 记录 #13 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,099 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwRewardEndTime' 字段的值 '2025-05-17 12:34:56' 不是有效的数值
2025-05-06 16:01:29,097 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #10 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,100 - WARNING - 表格 'FestivalActSignIn' 记录 #15 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,098 - WARNING - {None}
2025-05-06 16:01:29,100 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #10 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,099 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwRewardEndTime' 字段的值 '2025-05-17 12:34:56' 不是有效的数值
2025-05-06 16:01:29,101 - WARNING - {None}
2025-05-06 16:01:29,100 - WARNING - 表格 'FestivalActSignIn' 记录 #15 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,100 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #10 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,102 - WARNING - 表格 'FestivalActSignIn' 记录 #17 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,101 - WARNING - {None}
2025-05-06 16:01:29,103 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #11 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,102 - WARNING - 表格 'FestivalActSignIn' 记录 #17 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,104 - WARNING - {None}
2025-05-06 16:01:29,103 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #11 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,105 - WARNING - 表格 'FestivalActSignIn' 记录 #19 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,104 - WARNING - {None}
2025-05-06 16:01:29,105 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #11 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,105 - WARNING - 表格 'FestivalActSignIn' 记录 #19 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:01:29,106 - WARNING - {None}
2025-05-06 16:01:29,105 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #11 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,106 - WARNING - {None}
2025-05-06 16:01:29,109 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #12 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,109 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,109 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #12 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,109 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,110 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #12 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,111 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,110 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #12 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,111 - INFO - 页签 FestivalActSignIn 处理完成
2025-05-06 16:01:29,111 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,112 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #13 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,111 - INFO - 页签 FestivalActSignIn 处理完成
2025-05-06 16:01:29,113 - WARNING - {None}
2025-05-06 16:01:29,112 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #13 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,113 - WARNING - {None}
2025-05-06 16:01:29,114 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #13 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,115 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,114 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #13 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,115 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,116 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #14 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,116 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,116 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #14 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,116 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,119 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #14 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,120 - WARNING - {None}
2025-05-06 16:01:29,119 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #14 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,120 - WARNING - {None}
2025-05-06 16:01:29,120 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #15 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,121 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,120 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #15 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,121 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,122 - INFO - 页签 FestivalActController 处理完成
2025-05-06 16:01:29,122 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #15 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,122 - INFO - 页签 FestivalActController 处理完成
2025-05-06 16:01:29,123 - WARNING - {None}
2025-05-06 16:01:29,122 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #15 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,123 - WARNING - {None}
2025-05-06 16:01:29,124 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #16 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,124 - WARNING - 页签 'DragonBoatGrid' 记录 #10 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,124 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #16 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,124 - WARNING - 页签 'DragonBoatGrid' 记录 #10 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,125 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #16 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,126 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,125 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #16 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,126 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,126 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #17 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,126 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #17 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,127 - WARNING - {None}
2025-05-06 16:01:29,128 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #17 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,127 - WARNING - {None}
2025-05-06 16:01:29,128 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #17 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,129 - WARNING - {None}
2025-05-06 16:01:29,129 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #18 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,129 - WARNING - {None}
2025-05-06 16:01:29,129 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #18 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,130 - WARNING - {None}
2025-05-06 16:01:29,131 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #18 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,130 - WARNING - {None}
2025-05-06 16:01:29,131 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #18 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,131 - WARNING - {None}
2025-05-06 16:01:29,131 - WARNING - {None}
2025-05-06 16:01:29,132 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #19 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,132 - WARNING - {None}
2025-05-06 16:01:29,132 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #19 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,132 - WARNING - {None}
2025-05-06 16:01:29,133 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #19 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,134 - WARNING - {None}
2025-05-06 16:01:29,133 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #19 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,134 - WARNING - {None}
2025-05-06 16:01:29,135 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #20 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,135 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,135 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #20 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,135 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,136 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #20 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,137 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,136 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #20 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,137 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,138 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #21 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,138 - WARNING - {None}
2025-05-06 16:01:29,138 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #21 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,138 - WARNING - {None}
2025-05-06 16:01:29,139 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #21 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,140 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,139 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #21 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,140 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,141 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #22 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,141 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,141 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #22 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,141 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,142 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #22 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,143 - WARNING - {None}
2025-05-06 16:01:29,142 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #22 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,143 - WARNING - {None}
2025-05-06 16:01:29,144 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #23 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,144 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,144 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #23 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,144 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,145 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #23 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,146 - WARNING - {None}
2025-05-06 16:01:29,145 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #23 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,146 - WARNING - {None}
2025-05-06 16:01:29,147 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #24 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,147 - WARNING - 页签 'DragonBoatGrid' 记录 #11 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,147 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #24 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,147 - WARNING - 页签 'DragonBoatGrid' 记录 #11 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,148 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #24 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,149 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,148 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #24 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,149 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,150 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #25 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,151 - WARNING - {None}
2025-05-06 16:01:29,150 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #25 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,151 - WARNING - {None}
2025-05-06 16:01:29,151 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #25 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,152 - WARNING - {None}
2025-05-06 16:01:29,151 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #25 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,152 - WARNING - {None}
2025-05-06 16:01:29,153 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #26 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,153 - WARNING - {None}
2025-05-06 16:01:29,153 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #26 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,153 - WARNING - {None}
2025-05-06 16:01:29,154 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #26 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,154 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #26 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,155 - WARNING - {None}
2025-05-06 16:01:29,155 - WARNING - {None}
2025-05-06 16:01:29,155 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #27 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,156 - WARNING - {None}
2025-05-06 16:01:29,155 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #27 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,156 - WARNING - {None}
2025-05-06 16:01:29,157 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #27 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,157 - WARNING - {None}
2025-05-06 16:01:29,157 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #27 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,157 - WARNING - {None}
2025-05-06 16:01:29,158 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #28 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,159 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,158 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #28 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,159 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,160 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #28 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,161 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,160 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #28 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,161 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,161 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #29 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,162 - WARNING - {None}
2025-05-06 16:01:29,161 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #29 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,162 - WARNING - {None}
2025-05-06 16:01:29,163 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #29 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,163 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,163 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #29 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,163 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,164 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #30 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,164 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #30 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,165 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,165 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,166 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #30 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,166 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #30 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,166 - WARNING - {None}
2025-05-06 16:01:29,166 - WARNING - {None}
2025-05-06 16:01:29,167 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #31 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,168 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,167 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #31 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,168 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,168 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #31 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,169 - WARNING - {None}
2025-05-06 16:01:29,168 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #31 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,169 - WARNING - {None}
2025-05-06 16:01:29,170 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #32 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,170 - WARNING - 页签 'DragonBoatGrid' 记录 #12 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,170 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #32 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,170 - WARNING - 页签 'DragonBoatGrid' 记录 #12 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,171 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #32 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,171 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #32 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,172 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,173 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #33 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,172 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,173 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #33 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,174 - WARNING - {None}
2025-05-06 16:01:29,174 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #33 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,174 - WARNING - {None}
2025-05-06 16:01:29,174 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #33 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,176 - WARNING - {None}
2025-05-06 16:01:29,176 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #34 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,176 - WARNING - {None}
2025-05-06 16:01:29,176 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #34 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,177 - WARNING - {None}
2025-05-06 16:01:29,178 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #34 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,177 - WARNING - {None}
2025-05-06 16:01:29,178 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #34 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,178 - WARNING - {None}
2025-05-06 16:01:29,179 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #35 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,178 - WARNING - {None}
2025-05-06 16:01:29,179 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #35 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,180 - WARNING - {None}
2025-05-06 16:01:29,180 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #35 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,180 - WARNING - {None}
2025-05-06 16:01:29,180 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #35 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,181 - WARNING - {None}
2025-05-06 16:01:29,182 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #36 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,181 - WARNING - {None}
2025-05-06 16:01:29,182 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #36 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,182 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,182 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,183 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #36 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,183 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #36 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,183 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,183 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,184 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #37 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,185 - WARNING - {None}
2025-05-06 16:01:29,184 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #37 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,185 - WARNING - {None}
2025-05-06 16:01:29,186 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #37 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,186 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,186 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #37 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,186 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,187 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #38 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,188 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,187 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #38 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,188 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,189 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #38 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,189 - WARNING - {None}
2025-05-06 16:01:29,189 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #38 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,189 - WARNING - {None}
2025-05-06 16:01:29,190 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #39 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,191 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,190 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #39 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,191 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,191 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #39 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,192 - WARNING - {None}
2025-05-06 16:01:29,191 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #39 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,192 - WARNING - {None}
2025-05-06 16:01:29,193 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #40 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,194 - WARNING - 页签 'DragonBoatGrid' 记录 #13 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,193 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #40 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,194 - WARNING - 页签 'DragonBoatGrid' 记录 #13 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,195 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #40 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,195 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,195 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #40 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,195 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,196 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #41 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,197 - WARNING - {None}
2025-05-06 16:01:29,196 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #41 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,197 - WARNING - {None}
2025-05-06 16:01:29,198 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #41 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,198 - WARNING - {None}
2025-05-06 16:01:29,198 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #41 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,198 - WARNING - {None}
2025-05-06 16:01:29,199 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #42 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,200 - WARNING - {None}
2025-05-06 16:01:29,199 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #42 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,200 - WARNING - {None}
2025-05-06 16:01:29,200 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #42 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,201 - WARNING - {None}
2025-05-06 16:01:29,200 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #42 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,201 - WARNING - {None}
2025-05-06 16:01:29,202 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #43 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,203 - WARNING - {None}
2025-05-06 16:01:29,202 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #43 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,203 - WARNING - {None}
2025-05-06 16:01:29,204 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #43 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,204 - WARNING - {None}
2025-05-06 16:01:29,204 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #43 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,204 - WARNING - {None}
2025-05-06 16:01:29,205 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #44 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,205 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #44 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,206 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,206 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,207 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #44 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,208 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,207 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #44 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,208 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,209 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #45 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,209 - WARNING - {None}
2025-05-06 16:01:29,209 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #45 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,209 - WARNING - {None}
2025-05-06 16:01:29,210 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #45 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,210 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,210 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #45 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,210 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,211 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #46 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,212 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,211 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #46 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,212 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,213 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #46 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,214 - WARNING - {None}
2025-05-06 16:01:29,213 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #46 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,214 - WARNING - {None}
2025-05-06 16:01:29,214 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #47 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,215 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,214 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #47 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,215 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,216 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #47 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,216 - WARNING - {None}
2025-05-06 16:01:29,216 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #47 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,216 - WARNING - {None}
2025-05-06 16:01:29,217 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #48 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,218 - WARNING - 页签 'DragonBoatGrid' 记录 #14 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,217 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #48 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,218 - WARNING - 页签 'DragonBoatGrid' 记录 #14 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,219 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #48 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,219 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,219 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #48 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,219 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,220 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #49 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,220 - WARNING - {None}
2025-05-06 16:01:29,220 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #49 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,220 - WARNING - {None}
2025-05-06 16:01:29,221 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #49 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,222 - WARNING - {None}
2025-05-06 16:01:29,221 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #49 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,222 - WARNING - {None}
2025-05-06 16:01:29,223 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #50 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,223 - WARNING - {None}
2025-05-06 16:01:29,223 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #50 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,223 - WARNING - {None}
2025-05-06 16:01:29,224 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #50 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,225 - WARNING - {None}
2025-05-06 16:01:29,224 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #50 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,225 - WARNING - {None}
2025-05-06 16:01:29,225 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #51 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,226 - WARNING - {None}
2025-05-06 16:01:29,225 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #51 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,226 - WARNING - {None}
2025-05-06 16:01:29,227 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #51 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,227 - WARNING - {None}
2025-05-06 16:01:29,227 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #51 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,227 - WARNING - {None}
2025-05-06 16:01:29,228 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #52 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,229 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,228 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #52 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,229 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,229 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #52 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,230 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,229 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #52 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,230 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,231 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #53 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,231 - WARNING - {None}
2025-05-06 16:01:29,231 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #53 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,231 - WARNING - {None}
2025-05-06 16:01:29,232 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #53 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,233 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,232 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #53 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,233 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,234 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #54 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,234 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,234 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #54 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,234 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,235 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #54 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,235 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #54 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,236 - WARNING - {None}
2025-05-06 16:01:29,237 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #55 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,237 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
Reward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,237 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,238 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #55 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,239 - WARNING - {None}
2025-05-06 16:01:29,238 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #55 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,239 - WARNING - {None}
2025-05-06 16:01:29,240 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #56 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,241 - WARNING - 页签 'DragonBoatGrid' 记录 #15 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,240 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #56 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,241 - WARNING - 页签 'DragonBoatGrid' 记录 #15 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,241 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #56 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,242 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,241 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #56 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,242 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,243 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #57 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,243 - WARNING - {None}
2025-05-06 16:01:29,243 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #57 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,243 - WARNING - {None}
2025-05-06 16:01:29,244 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #57 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,245 - WARNING - {None}
2025-05-06 16:01:29,244 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #57 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,245 - WARNING - {None}
2025-05-06 16:01:29,246 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #58 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,246 - WARNING - {None}
2025-05-06 16:01:29,246 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #58 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,246 - WARNING - {None}
2025-05-06 16:01:29,247 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #58 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,248 - WARNING - {None}
2025-05-06 16:01:29,247 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #58 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,248 - WARNING - {None}
2025-05-06 16:01:29,249 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #59 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,249 - WARNING - {None}
2025-05-06 16:01:29,249 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #59 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,249 - WARNING - {None}
2025-05-06 16:01:29,250 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #59 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,251 - WARNING - {None}
2025-05-06 16:01:29,250 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #59 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,251 - WARNING - {None}
2025-05-06 16:01:29,252 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #60 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,252 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,252 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #60 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,252 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,253 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #60 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,254 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,253 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #60 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,254 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,255 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #61 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,256 - WARNING - {None}
2025-05-06 16:01:29,255 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #61 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,256 - WARNING - {None}
2025-05-06 16:01:29,256 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #61 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,257 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,256 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #61 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:01:29,257 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,258 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,258 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,259 - WARNING - {None}
2025-05-06 16:01:29,259 - WARNING - {None}
2025-05-06 16:01:29,259 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,259 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,260 - WARNING - {None}
2025-05-06 16:01:29,260 - WARNING - {None}
2025-05-06 16:01:29,261 - WARNING - 页签 'DragonBoatGrid' 记录 #16 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,261 - WARNING - 页签 'DragonBoatGrid' 记录 #16 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,264 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,264 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,264 - WARNING - {None}
2025-05-06 16:01:29,264 - WARNING - {None}
2025-05-06 16:01:29,265 - WARNING - {None}
2025-05-06 16:01:29,265 - WARNING - {None}
2025-05-06 16:01:29,266 - WARNING - {None}
2025-05-06 16:01:29,266 - WARNING - {None}
2025-05-06 16:01:29,267 - WARNING - {None}
2025-05-06 16:01:29,267 - WARNING - {None}
2025-05-06 16:01:29,267 - WARNING - {None}
2025-05-06 16:01:29,267 - WARNING - {None}
2025-05-06 16:01:29,268 - WARNING - {None}
2025-05-06 16:01:29,268 - WARNING - {None}
2025-05-06 16:01:29,269 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,269 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,270 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,270 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,274 - WARNING - {None}
2025-05-06 16:01:29,274 - WARNING - {None}
2025-05-06 16:01:29,275 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,275 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,276 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,276 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,277 - WARNING - {None}
2025-05-06 16:01:29,277 - WARNING - {None}
2025-05-06 16:01:29,277 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,278 - INFO - 页签 FestivalActWarOrderLevel 处理完成
2025-05-06 16:01:29,277 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,278 - INFO - 页签 FestivalActWarOrderLevel 处理完成
2025-05-06 16:01:29,279 - WARNING - {None}
2025-05-06 16:01:29,279 - WARNING - {None}
2025-05-06 16:01:29,279 - WARNING - 页签 'DragonBoatGrid' 记录 #17 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,279 - WARNING - 页签 'DragonBoatGrid' 记录 #17 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,280 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,280 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,281 - WARNING - {None}
2025-05-06 16:01:29,281 - WARNING - {None}
2025-05-06 16:01:29,281 - WARNING - {None}
2025-05-06 16:01:29,281 - WARNING - {None}
2025-05-06 16:01:29,282 - WARNING - {None}
2025-05-06 16:01:29,282 - WARNING - {None}
2025-05-06 16:01:29,282 - WARNING - {None}
2025-05-06 16:01:29,282 - WARNING - {None}
2025-05-06 16:01:29,283 - WARNING - {None}
2025-05-06 16:01:29,283 - WARNING - {None}
2025-05-06 16:01:29,284 - WARNING - {None}
2025-05-06 16:01:29,284 - WARNING - {None}
2025-05-06 16:01:29,284 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,284 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,285 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,285 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,286 - WARNING - {None}
2025-05-06 16:01:29,286 - WARNING - {None}
2025-05-06 16:01:29,286 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,286 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,287 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,287 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,288 - WARNING - {None}
2025-05-06 16:01:29,288 - WARNING - {None}
2025-05-06 16:01:29,288 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,288 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,289 - WARNING - {None}
2025-05-06 16:01:29,289 - WARNING - {None}
2025-05-06 16:01:29,290 - WARNING - 页签 'DragonBoatGrid' 记录 #18 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,290 - WARNING - 页签 'DragonBoatGrid' 记录 #18 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,290 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,290 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,291 - WARNING - {None}
2025-05-06 16:01:29,291 - WARNING - {None}
2025-05-06 16:01:29,291 - WARNING - {None}
2025-05-06 16:01:29,291 - WARNING - {None}
2025-05-06 16:01:29,292 - WARNING - {None}
2025-05-06 16:01:29,292 - WARNING - {None}
2025-05-06 16:01:29,292 - WARNING - {None}
2025-05-06 16:01:29,292 - WARNING - {None}
2025-05-06 16:01:29,293 - WARNING - {None}
2025-05-06 16:01:29,293 - WARNING - {None}
2025-05-06 16:01:29,294 - WARNING - {None}
2025-05-06 16:01:29,294 - WARNING - {None}
2025-05-06 16:01:29,294 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,294 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,295 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,295 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,296 - WARNING - {None}
2025-05-06 16:01:29,296 - WARNING - {None}
2025-05-06 16:01:29,296 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,296 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,297 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,297 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,298 - WARNING - {None}
2025-05-06 16:01:29,298 - WARNING - {None}
2025-05-06 16:01:29,298 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,298 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,299 - WARNING - {None}
2025-05-06 16:01:29,299 - WARNING - {None}
2025-05-06 16:01:29,300 - WARNING - 页签 'DragonBoatGrid' 记录 #19 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,300 - WARNING - 页签 'DragonBoatGrid' 记录 #19 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,300 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,300 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,301 - WARNING - {None}
2025-05-06 16:01:29,301 - WARNING - {None}
2025-05-06 16:01:29,302 - WARNING - {None}
2025-05-06 16:01:29,302 - WARNING - {None}
2025-05-06 16:01:29,302 - WARNING - {None}
2025-05-06 16:01:29,302 - WARNING - {None}
2025-05-06 16:01:29,303 - WARNING - {None}
2025-05-06 16:01:29,303 - WARNING - {None}
2025-05-06 16:01:29,303 - WARNING - {None}
2025-05-06 16:01:29,303 - WARNING - {None}
2025-05-06 16:01:29,304 - WARNING - {None}
2025-05-06 16:01:29,304 - WARNING - {None}
2025-05-06 16:01:29,305 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,305 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,306 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,306 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,306 - WARNING - {None}
2025-05-06 16:01:29,306 - WARNING - {None}
2025-05-06 16:01:29,307 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,307 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,308 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,308 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,309 - WARNING - {None}
2025-05-06 16:01:29,309 - WARNING - {None}
2025-05-06 16:01:29,309 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,309 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,310 - WARNING - {None}
2025-05-06 16:01:29,310 - WARNING - {None}
2025-05-06 16:01:29,311 - WARNING - 页签 'DragonBoatGrid' 记录 #20 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,311 - WARNING - 页签 'DragonBoatGrid' 记录 #20 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,312 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,312 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,312 - WARNING - {None}
2025-05-06 16:01:29,312 - WARNING - {None}
2025-05-06 16:01:29,313 - WARNING - {None}
2025-05-06 16:01:29,313 - WARNING - {None}
2025-05-06 16:01:29,314 - WARNING - {None}
2025-05-06 16:01:29,314 - WARNING - {None}
2025-05-06 16:01:29,314 - WARNING - {None}
2025-05-06 16:01:29,314 - WARNING - {None}
2025-05-06 16:01:29,315 - WARNING - {None}
2025-05-06 16:01:29,315 - WARNING - {None}
2025-05-06 16:01:29,316 - WARNING - {None}
2025-05-06 16:01:29,316 - WARNING - {None}
2025-05-06 16:01:29,316 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,316 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,317 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,317 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,317 - WARNING - {None}
2025-05-06 16:01:29,317 - WARNING - {None}
2025-05-06 16:01:29,318 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,318 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,319 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,319 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,320 - WARNING - {None}
2025-05-06 16:01:29,320 - WARNING - {None}
2025-05-06 16:01:29,320 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,320 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,321 - WARNING - {None}
2025-05-06 16:01:29,321 - WARNING - {None}
2025-05-06 16:01:29,322 - WARNING - 页签 'DragonBoatGrid' 记录 #21 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,322 - WARNING - 页签 'DragonBoatGrid' 记录 #21 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,322 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,322 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,323 - WARNING - {None}
2025-05-06 16:01:29,323 - WARNING - {None}
2025-05-06 16:01:29,324 - WARNING - {None}
2025-05-06 16:01:29,324 - WARNING - {None}
2025-05-06 16:01:29,324 - WARNING - {None}
2025-05-06 16:01:29,324 - WARNING - {None}
2025-05-06 16:01:29,325 - WARNING - {None}
2025-05-06 16:01:29,325 - WARNING - {None}
2025-05-06 16:01:29,326 - WARNING - {None}
2025-05-06 16:01:29,326 - WARNING - {None}
2025-05-06 16:01:29,326 - WARNING - {None}
2025-05-06 16:01:29,326 - WARNING - {None}
2025-05-06 16:01:29,327 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,327 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,328 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,328 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,328 - WARNING - {None}
2025-05-06 16:01:29,328 - WARNING - {None}
2025-05-06 16:01:29,329 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,329 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,330 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,330 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,330 - WARNING - {None}
2025-05-06 16:01:29,330 - WARNING - {None}
2025-05-06 16:01:29,331 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,331 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,331 - WARNING - {None}
2025-05-06 16:01:29,331 - WARNING - {None}
2025-05-06 16:01:29,332 - WARNING - 页签 'DragonBoatGrid' 记录 #22 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,332 - WARNING - 页签 'DragonBoatGrid' 记录 #22 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,333 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,333 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,333 - WARNING - {None}
2025-05-06 16:01:29,333 - WARNING - {None}
2025-05-06 16:01:29,334 - WARNING - {None}
2025-05-06 16:01:29,334 - WARNING - {None}
2025-05-06 16:01:29,335 - WARNING - {None}
2025-05-06 16:01:29,335 - WARNING - {None}
2025-05-06 16:01:29,335 - WARNING - {None}
2025-05-06 16:01:29,335 - WARNING - {None}
2025-05-06 16:01:29,336 - WARNING - {None}
2025-05-06 16:01:29,336 - WARNING - {None}
2025-05-06 16:01:29,337 - WARNING - {None}
2025-05-06 16:01:29,337 - WARNING - {None}
2025-05-06 16:01:29,337 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,337 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,338 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,338 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,339 - WARNING - {None}
2025-05-06 16:01:29,339 - WARNING - {None}
2025-05-06 16:01:29,339 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,339 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,340 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,340 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,340 - WARNING - {None}
2025-05-06 16:01:29,340 - WARNING - {None}
2025-05-06 16:01:29,341 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,341 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,342 - WARNING - {None}
2025-05-06 16:01:29,342 - WARNING - {None}
2025-05-06 16:01:29,342 - WARNING - 页签 'DragonBoatGrid' 记录 #23 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,342 - WARNING - 页签 'DragonBoatGrid' 记录 #23 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,343 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,343 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,344 - WARNING - {None}
2025-05-06 16:01:29,344 - WARNING - {None}
2025-05-06 16:01:29,345 - WARNING - {None}
2025-05-06 16:01:29,345 - WARNING - {None}
2025-05-06 16:01:29,345 - WARNING - {None}
2025-05-06 16:01:29,345 - WARNING - {None}
2025-05-06 16:01:29,346 - WARNING - {None}
2025-05-06 16:01:29,346 - WARNING - {None}
2025-05-06 16:01:29,346 - WARNING - {None}
2025-05-06 16:01:29,346 - WARNING - {None}
2025-05-06 16:01:29,347 - WARNING - {None}
2025-05-06 16:01:29,347 - WARNING - {None}
2025-05-06 16:01:29,348 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,348 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,348 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,348 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,349 - WARNING - {None}
2025-05-06 16:01:29,349 - WARNING - {None}
2025-05-06 16:01:29,350 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,350 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,351 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,351 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,352 - WARNING - {None}
2025-05-06 16:01:29,352 - WARNING - {None}
2025-05-06 16:01:29,353 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,353 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,354 - WARNING - {None}
2025-05-06 16:01:29,354 - WARNING - {None}
2025-05-06 16:01:29,354 - WARNING - 页签 'DragonBoatGrid' 记录 #24 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,354 - WARNING - 页签 'DragonBoatGrid' 记录 #24 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,355 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,355 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,356 - WARNING - {None}
2025-05-06 16:01:29,356 - WARNING - {None}
2025-05-06 16:01:29,357 - WARNING - {None}
2025-05-06 16:01:29,357 - WARNING - {None}
2025-05-06 16:01:29,357 - WARNING - {None}
2025-05-06 16:01:29,357 - WARNING - {None}
2025-05-06 16:01:29,358 - WARNING - {None}
2025-05-06 16:01:29,358 - WARNING - {None}
2025-05-06 16:01:29,358 - WARNING - {None}
2025-05-06 16:01:29,358 - WARNING - {None}
2025-05-06 16:01:29,359 - WARNING - {None}
2025-05-06 16:01:29,359 - WARNING - {None}
2025-05-06 16:01:29,360 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,360 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,360 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,360 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,361 - WARNING - {None}
2025-05-06 16:01:29,361 - WARNING - {None}
2025-05-06 16:01:29,362 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,362 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,362 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,362 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,363 - WARNING - {None}
2025-05-06 16:01:29,363 - WARNING - {None}
2025-05-06 16:01:29,364 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,364 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,365 - WARNING - {None}
2025-05-06 16:01:29,365 - WARNING - {None}
2025-05-06 16:01:29,366 - WARNING - 页签 'DragonBoatGrid' 记录 #25 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,366 - WARNING - 页签 'DragonBoatGrid' 记录 #25 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,366 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,366 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,367 - WARNING - {None}
2025-05-06 16:01:29,367 - WARNING - {None}
2025-05-06 16:01:29,367 - WARNING - {None}
2025-05-06 16:01:29,367 - WARNING - {None}
2025-05-06 16:01:29,368 - WARNING - {None}
2025-05-06 16:01:29,368 - WARNING - {None}
2025-05-06 16:01:29,369 - WARNING - {None}
2025-05-06 16:01:29,369 - WARNING - {None}
2025-05-06 16:01:29,369 - WARNING - {None}
2025-05-06 16:01:29,369 - WARNING - {None}
2025-05-06 16:01:29,370 - WARNING - {None}
2025-05-06 16:01:29,370 - WARNING - {None}
2025-05-06 16:01:29,371 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,371 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,371 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,371 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,372 - WARNING - {None}
2025-05-06 16:01:29,372 - WARNING - {None}
2025-05-06 16:01:29,372 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,372 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,373 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,373 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,373 - WARNING - {None}
2025-05-06 16:01:29,373 - WARNING - {None}
2025-05-06 16:01:29,374 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,374 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,375 - WARNING - {None}
2025-05-06 16:01:29,375 - WARNING - {None}
2025-05-06 16:01:29,376 - WARNING - 页签 'DragonBoatGrid' 记录 #26 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,376 - WARNING - 页签 'DragonBoatGrid' 记录 #26 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,376 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,376 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,377 - WARNING - {None}
2025-05-06 16:01:29,377 - WARNING - {None}
2025-05-06 16:01:29,378 - WARNING - {None}
2025-05-06 16:01:29,378 - WARNING - {None}
2025-05-06 16:01:29,379 - WARNING - {None}
2025-05-06 16:01:29,379 - WARNING - {None}
2025-05-06 16:01:29,379 - WARNING - {None}
2025-05-06 16:01:29,379 - WARNING - {None}
2025-05-06 16:01:29,380 - WARNING - {None}
2025-05-06 16:01:29,380 - WARNING - {None}
2025-05-06 16:01:29,381 - WARNING - {None}
2025-05-06 16:01:29,381 - WARNING - {None}
2025-05-06 16:01:29,381 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,381 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,382 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,382 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,382 - WARNING - {None}
2025-05-06 16:01:29,382 - WARNING - {None}
2025-05-06 16:01:29,383 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,383 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,384 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,384 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,385 - WARNING - {None}
2025-05-06 16:01:29,385 - WARNING - {None}
2025-05-06 16:01:29,385 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,385 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,386 - WARNING - {None}
2025-05-06 16:01:29,386 - WARNING - {None}
2025-05-06 16:01:29,386 - WARNING - 页签 'DragonBoatGrid' 记录 #27 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,386 - WARNING - 页签 'DragonBoatGrid' 记录 #27 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,387 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,387 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,388 - WARNING - {None}
2025-05-06 16:01:29,388 - WARNING - {None}
2025-05-06 16:01:29,388 - WARNING - {None}
2025-05-06 16:01:29,388 - WARNING - {None}
2025-05-06 16:01:29,389 - WARNING - {None}
2025-05-06 16:01:29,389 - WARNING - {None}
2025-05-06 16:01:29,389 - WARNING - {None}
2025-05-06 16:01:29,389 - WARNING - {None}
2025-05-06 16:01:29,390 - WARNING - {None}
2025-05-06 16:01:29,390 - WARNING - {None}
2025-05-06 16:01:29,390 - WARNING - {None}
2025-05-06 16:01:29,390 - WARNING - {None}
2025-05-06 16:01:29,391 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,391 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,392 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,392 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,392 - WARNING - {None}
2025-05-06 16:01:29,392 - WARNING - {None}
2025-05-06 16:01:29,393 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,393 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,394 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,394 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,394 - WARNING - {None}
2025-05-06 16:01:29,394 - WARNING - {None}
2025-05-06 16:01:29,395 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,395 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,396 - WARNING - {None}
2025-05-06 16:01:29,396 - WARNING - {None}
2025-05-06 16:01:29,396 - WARNING - 页签 'DragonBoatGrid' 记录 #28 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,396 - WARNING - 页签 'DragonBoatGrid' 记录 #28 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,397 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,397 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,398 - WARNING - {None}
2025-05-06 16:01:29,398 - WARNING - {None}
2025-05-06 16:01:29,399 - WARNING - {None}
2025-05-06 16:01:29,399 - WARNING - {None}
2025-05-06 16:01:29,399 - WARNING - {None}
2025-05-06 16:01:29,399 - WARNING - {None}
2025-05-06 16:01:29,400 - WARNING - {None}
2025-05-06 16:01:29,400 - WARNING - {None}
2025-05-06 16:01:29,400 - WARNING - {None}
2025-05-06 16:01:29,400 - WARNING - {None}
2025-05-06 16:01:29,401 - WARNING - {None}
2025-05-06 16:01:29,401 - WARNING - {None}
2025-05-06 16:01:29,402 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,402 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,402 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,402 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,403 - WARNING - {None}
2025-05-06 16:01:29,403 - WARNING - {None}
2025-05-06 16:01:29,403 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,403 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,404 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,404 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,405 - WARNING - {None}
2025-05-06 16:01:29,405 - WARNING - {None}
2025-05-06 16:01:29,406 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,406 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,406 - WARNING - {None}
2025-05-06 16:01:29,406 - WARNING - {None}
2025-05-06 16:01:29,407 - WARNING - 页签 'DragonBoatGrid' 记录 #29 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,407 - WARNING - 页签 'DragonBoatGrid' 记录 #29 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,408 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,408 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,408 - WARNING - {None}
2025-05-06 16:01:29,408 - WARNING - {None}
2025-05-06 16:01:29,409 - WARNING - {None}
2025-05-06 16:01:29,409 - WARNING - {None}
2025-05-06 16:01:29,410 - WARNING - {None}
2025-05-06 16:01:29,410 - WARNING - {None}
2025-05-06 16:01:29,410 - WARNING - {None}
2025-05-06 16:01:29,410 - WARNING - {None}
2025-05-06 16:01:29,411 - WARNING - {None}
2025-05-06 16:01:29,411 - WARNING - {None}
2025-05-06 16:01:29,411 - WARNING - {None}
2025-05-06 16:01:29,411 - WARNING - {None}
2025-05-06 16:01:29,412 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,412 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,413 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,413 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,413 - WARNING - {None}
2025-05-06 16:01:29,413 - WARNING - {None}
2025-05-06 16:01:29,414 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,414 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,415 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,415 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,415 - WARNING - {None}
2025-05-06 16:01:29,415 - WARNING - {None}
2025-05-06 16:01:29,416 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,416 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,417 - WARNING - {None}
2025-05-06 16:01:29,417 - WARNING - {None}
2025-05-06 16:01:29,417 - WARNING - 页签 'DragonBoatGrid' 记录 #30 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,417 - WARNING - 页签 'DragonBoatGrid' 记录 #30 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,418 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,418 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,419 - WARNING - {None}
2025-05-06 16:01:29,419 - WARNING - {None}
2025-05-06 16:01:29,420 - WARNING - {None}
2025-05-06 16:01:29,420 - WARNING - {None}
2025-05-06 16:01:29,420 - WARNING - {None}
2025-05-06 16:01:29,420 - WARNING - {None}
2025-05-06 16:01:29,421 - WARNING - {None}
2025-05-06 16:01:29,421 - WARNING - {None}
2025-05-06 16:01:29,422 - WARNING - {None}
2025-05-06 16:01:29,422 - WARNING - {None}
2025-05-06 16:01:29,422 - WARNING - {None}
2025-05-06 16:01:29,422 - WARNING - {None}
2025-05-06 16:01:29,423 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,423 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,424 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,424 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,425 - WARNING - {None}
2025-05-06 16:01:29,425 - WARNING - {None}
2025-05-06 16:01:29,425 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,425 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,426 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,426 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,427 - WARNING - {None}
2025-05-06 16:01:29,427 - WARNING - {None}
2025-05-06 16:01:29,427 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,427 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,428 - WARNING - {None}
2025-05-06 16:01:29,428 - WARNING - {None}
2025-05-06 16:01:29,428 - WARNING - 页签 'DragonBoatGrid' 记录 #31 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,428 - WARNING - 页签 'DragonBoatGrid' 记录 #31 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,429 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,429 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,430 - WARNING - {None}
2025-05-06 16:01:29,430 - WARNING - {None}
2025-05-06 16:01:29,430 - WARNING - {None}
2025-05-06 16:01:29,430 - WARNING - {None}
2025-05-06 16:01:29,431 - WARNING - {None}
2025-05-06 16:01:29,431 - WARNING - {None}
2025-05-06 16:01:29,431 - WARNING - {None}
2025-05-06 16:01:29,431 - WARNING - {None}
2025-05-06 16:01:29,432 - WARNING - {None}
2025-05-06 16:01:29,432 - WARNING - {None}
2025-05-06 16:01:29,433 - WARNING - {None}
2025-05-06 16:01:29,433 - WARNING - {None}
2025-05-06 16:01:29,433 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,433 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,434 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,434 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,435 - WARNING - {None}
2025-05-06 16:01:29,435 - WARNING - {None}
2025-05-06 16:01:29,435 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,435 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,436 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,436 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,437 - WARNING - {None}
2025-05-06 16:01:29,437 - WARNING - {None}
2025-05-06 16:01:29,437 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,437 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,438 - WARNING - {None}
2025-05-06 16:01:29,438 - WARNING - {None}
2025-05-06 16:01:29,439 - WARNING - 页签 'DragonBoatGrid' 记录 #32 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,439 - WARNING - 页签 'DragonBoatGrid' 记录 #32 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,439 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,439 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,440 - WARNING - {None}
2025-05-06 16:01:29,440 - WARNING - {None}
2025-05-06 16:01:29,441 - WARNING - {None}
2025-05-06 16:01:29,441 - WARNING - {None}
2025-05-06 16:01:29,441 - WARNING - {None}
2025-05-06 16:01:29,441 - WARNING - {None}
2025-05-06 16:01:29,442 - WARNING - {None}
2025-05-06 16:01:29,442 - WARNING - {None}
2025-05-06 16:01:29,443 - WARNING - {None}
2025-05-06 16:01:29,443 - WARNING - {None}
2025-05-06 16:01:29,443 - WARNING - {None}
2025-05-06 16:01:29,443 - WARNING - {None}
2025-05-06 16:01:29,444 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,444 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,445 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,445 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,445 - WARNING - {None}
2025-05-06 16:01:29,445 - WARNING - {None}
2025-05-06 16:01:29,446 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,446 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,446 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,446 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,447 - WARNING - {None}
2025-05-06 16:01:29,447 - WARNING - {None}
2025-05-06 16:01:29,448 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,448 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,448 - WARNING - {None}
2025-05-06 16:01:29,448 - WARNING - {None}
2025-05-06 16:01:29,449 - WARNING - 页签 'DragonBoatGrid' 记录 #33 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,449 - WARNING - 页签 'DragonBoatGrid' 记录 #33 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,450 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,450 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,450 - WARNING - {None}
2025-05-06 16:01:29,450 - WARNING - {None}
2025-05-06 16:01:29,451 - WARNING - {None}
2025-05-06 16:01:29,451 - WARNING - {None}
2025-05-06 16:01:29,452 - WARNING - {None}
2025-05-06 16:01:29,452 - WARNING - {None}
2025-05-06 16:01:29,452 - WARNING - {None}
2025-05-06 16:01:29,452 - WARNING - {None}
2025-05-06 16:01:29,453 - WARNING - {None}
2025-05-06 16:01:29,453 - WARNING - {None}
2025-05-06 16:01:29,454 - WARNING - {None}
2025-05-06 16:01:29,454 - WARNING - {None}
2025-05-06 16:01:29,455 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,455 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,455 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,455 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,456 - WARNING - {None}
2025-05-06 16:01:29,456 - WARNING - {None}
2025-05-06 16:01:29,457 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,457 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,458 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,458 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,459 - WARNING - {None}
2025-05-06 16:01:29,459 - WARNING - {None}
2025-05-06 16:01:29,459 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,459 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,460 - WARNING - {None}
2025-05-06 16:01:29,460 - WARNING - {None}
2025-05-06 16:01:29,461 - WARNING - 页签 'DragonBoatGrid' 记录 #34 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,461 - WARNING - 页签 'DragonBoatGrid' 记录 #34 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,461 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,461 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,462 - WARNING - {None}
2025-05-06 16:01:29,462 - WARNING - {None}
2025-05-06 16:01:29,462 - WARNING - {None}
2025-05-06 16:01:29,462 - WARNING - {None}
2025-05-06 16:01:29,463 - WARNING - {None}
2025-05-06 16:01:29,463 - WARNING - {None}
2025-05-06 16:01:29,464 - WARNING - {None}
2025-05-06 16:01:29,464 - WARNING - {None}
2025-05-06 16:01:29,464 - WARNING - {None}
2025-05-06 16:01:29,464 - WARNING - {None}
2025-05-06 16:01:29,465 - WARNING - {None}
2025-05-06 16:01:29,465 - WARNING - {None}
2025-05-06 16:01:29,466 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,466 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,466 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,466 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,467 - WARNING - {None}
2025-05-06 16:01:29,467 - WARNING - {None}
2025-05-06 16:01:29,467 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,467 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,468 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,468 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,469 - WARNING - {None}
2025-05-06 16:01:29,469 - WARNING - {None}
2025-05-06 16:01:29,470 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,470 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,470 - WARNING - {None}
2025-05-06 16:01:29,470 - WARNING - {None}
2025-05-06 16:01:29,471 - WARNING - 页签 'DragonBoatGrid' 记录 #35 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,471 - WARNING - 页签 'DragonBoatGrid' 记录 #35 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,472 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,472 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,472 - WARNING - {None}
2025-05-06 16:01:29,472 - WARNING - {None}
2025-05-06 16:01:29,473 - WARNING - {None}
2025-05-06 16:01:29,473 - WARNING - {None}
2025-05-06 16:01:29,474 - WARNING - {None}
2025-05-06 16:01:29,474 - WARNING - {None}
2025-05-06 16:01:29,474 - WARNING - {None}
2025-05-06 16:01:29,474 - WARNING - {None}
2025-05-06 16:01:29,475 - WARNING - {None}
2025-05-06 16:01:29,475 - WARNING - {None}
2025-05-06 16:01:29,476 - WARNING - {None}
2025-05-06 16:01:29,476 - WARNING - {None}
2025-05-06 16:01:29,476 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,476 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,477 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,477 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,478 - WARNING - {None}
2025-05-06 16:01:29,478 - WARNING - {None}
2025-05-06 16:01:29,479 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,479 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,479 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,479 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,480 - WARNING - {None}
2025-05-06 16:01:29,480 - WARNING - {None}
2025-05-06 16:01:29,481 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,481 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,481 - WARNING - {None}
2025-05-06 16:01:29,481 - WARNING - {None}
2025-05-06 16:01:29,482 - WARNING - 页签 'DragonBoatGrid' 记录 #36 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,482 - WARNING - 页签 'DragonBoatGrid' 记录 #36 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,482 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,482 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,483 - WARNING - {None}
2025-05-06 16:01:29,483 - WARNING - {None}
2025-05-06 16:01:29,484 - WARNING - {None}
2025-05-06 16:01:29,484 - WARNING - {None}
2025-05-06 16:01:29,485 - WARNING - {None}
2025-05-06 16:01:29,485 - WARNING - {None}
2025-05-06 16:01:29,485 - WARNING - {None}
2025-05-06 16:01:29,485 - WARNING - {None}
2025-05-06 16:01:29,486 - WARNING - {None}
2025-05-06 16:01:29,486 - WARNING - {None}
2025-05-06 16:01:29,487 - WARNING - {None}
2025-05-06 16:01:29,487 - WARNING - {None}
2025-05-06 16:01:29,487 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,487 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,488 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,488 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,489 - WARNING - {None}
2025-05-06 16:01:29,489 - WARNING - {None}
2025-05-06 16:01:29,489 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,489 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,490 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,490 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,491 - WARNING - {None}
2025-05-06 16:01:29,491 - WARNING - {None}
2025-05-06 16:01:29,491 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,491 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,492 - WARNING - {None}
2025-05-06 16:01:29,492 - WARNING - {None}
2025-05-06 16:01:29,492 - WARNING - 页签 'DragonBoatGrid' 记录 #37 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,492 - WARNING - 页签 'DragonBoatGrid' 记录 #37 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,493 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,493 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,494 - WARNING - {None}
2025-05-06 16:01:29,494 - WARNING - {None}
2025-05-06 16:01:29,495 - WARNING - {None}
2025-05-06 16:01:29,495 - WARNING - {None}
2025-05-06 16:01:29,495 - WARNING - {None}
2025-05-06 16:01:29,495 - WARNING - {None}
2025-05-06 16:01:29,496 - WARNING - {None}
2025-05-06 16:01:29,496 - WARNING - {None}
2025-05-06 16:01:29,497 - WARNING - {None}
2025-05-06 16:01:29,497 - WARNING - {None}
2025-05-06 16:01:29,497 - WARNING - {None}
2025-05-06 16:01:29,497 - WARNING - {None}
2025-05-06 16:01:29,498 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,498 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,499 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,499 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,499 - WARNING - {None}
2025-05-06 16:01:29,499 - WARNING - {None}
2025-05-06 16:01:29,500 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,500 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,501 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,501 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,501 - WARNING - {None}
2025-05-06 16:01:29,501 - WARNING - {None}
2025-05-06 16:01:29,502 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,502 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,502 - WARNING - {None}
2025-05-06 16:01:29,502 - WARNING - {None}
2025-05-06 16:01:29,503 - WARNING - 页签 'DragonBoatGrid' 记录 #38 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,503 - WARNING - 页签 'DragonBoatGrid' 记录 #38 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,504 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,504 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,505 - WARNING - {None}
2025-05-06 16:01:29,505 - WARNING - {None}
2025-05-06 16:01:29,505 - WARNING - {None}
2025-05-06 16:01:29,505 - WARNING - {None}
2025-05-06 16:01:29,506 - WARNING - {None}
2025-05-06 16:01:29,506 - WARNING - {None}
2025-05-06 16:01:29,507 - WARNING - {None}
2025-05-06 16:01:29,507 - WARNING - {None}
2025-05-06 16:01:29,507 - WARNING - {None}
2025-05-06 16:01:29,507 - WARNING - {None}
2025-05-06 16:01:29,508 - WARNING - {None}
2025-05-06 16:01:29,508 - WARNING - {None}
2025-05-06 16:01:29,509 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,509 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,509 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,509 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,510 - WARNING - {None}
2025-05-06 16:01:29,510 - WARNING - {None}
2025-05-06 16:01:29,510 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,510 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,511 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,511 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,512 - WARNING - {None}
2025-05-06 16:01:29,512 - WARNING - {None}
2025-05-06 16:01:29,512 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,512 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,513 - WARNING - {None}
2025-05-06 16:01:29,513 - WARNING - {None}
2025-05-06 16:01:29,514 - WARNING - 页签 'DragonBoatGrid' 记录 #39 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,514 - WARNING - 页签 'DragonBoatGrid' 记录 #39 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,514 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,514 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,515 - WARNING - {None}
2025-05-06 16:01:29,515 - WARNING - {None}
2025-05-06 16:01:29,516 - WARNING - {None}
2025-05-06 16:01:29,516 - WARNING - {None}
2025-05-06 16:01:29,517 - WARNING - {None}
2025-05-06 16:01:29,517 - WARNING - {None}
2025-05-06 16:01:29,517 - WARNING - {None}
2025-05-06 16:01:29,517 - WARNING - {None}
2025-05-06 16:01:29,518 - WARNING - {None}
2025-05-06 16:01:29,518 - WARNING - {None}
2025-05-06 16:01:29,519 - WARNING - {None}
2025-05-06 16:01:29,519 - WARNING - {None}
2025-05-06 16:01:29,520 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,520 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,521 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,521 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,521 - WARNING - {None}
2025-05-06 16:01:29,521 - WARNING - {None}
2025-05-06 16:01:29,522 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,522 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,523 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,523 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,523 - WARNING - {None}
2025-05-06 16:01:29,523 - WARNING - {None}
2025-05-06 16:01:29,524 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,524 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,525 - WARNING - {None}
2025-05-06 16:01:29,525 - WARNING - {None}
2025-05-06 16:01:29,525 - WARNING - 页签 'DragonBoatGrid' 记录 #40 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,525 - WARNING - 页签 'DragonBoatGrid' 记录 #40 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,526 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,526 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,527 - WARNING - {None}
2025-05-06 16:01:29,527 - WARNING - {None}
2025-05-06 16:01:29,527 - WARNING - {None}
2025-05-06 16:01:29,527 - WARNING - {None}
2025-05-06 16:01:29,528 - WARNING - {None}
2025-05-06 16:01:29,528 - WARNING - {None}
2025-05-06 16:01:29,529 - WARNING - {None}
2025-05-06 16:01:29,529 - WARNING - {None}
2025-05-06 16:01:29,529 - WARNING - {None}
2025-05-06 16:01:29,529 - WARNING - {None}
2025-05-06 16:01:29,530 - WARNING - {None}
2025-05-06 16:01:29,530 - WARNING - {None}
2025-05-06 16:01:29,530 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,530 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,531 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,531 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,531 - WARNING - {None}
2025-05-06 16:01:29,531 - WARNING - {None}
2025-05-06 16:01:29,532 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,532 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,533 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,533 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,533 - WARNING - {None}
2025-05-06 16:01:29,533 - WARNING - {None}
2025-05-06 16:01:29,534 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,534 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,535 - WARNING - {None}
2025-05-06 16:01:29,535 - WARNING - {None}
2025-05-06 16:01:29,535 - WARNING - 页签 'DragonBoatGrid' 记录 #41 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,535 - WARNING - 页签 'DragonBoatGrid' 记录 #41 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,536 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,536 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,537 - WARNING - {None}
2025-05-06 16:01:29,537 - WARNING - {None}
2025-05-06 16:01:29,538 - WARNING - {None}
2025-05-06 16:01:29,538 - WARNING - {None}
2025-05-06 16:01:29,539 - WARNING - {None}
2025-05-06 16:01:29,539 - WARNING - {None}
2025-05-06 16:01:29,539 - WARNING - {None}
2025-05-06 16:01:29,539 - WARNING - {None}
2025-05-06 16:01:29,540 - WARNING - {None}
2025-05-06 16:01:29,540 - WARNING - {None}
2025-05-06 16:01:29,540 - WARNING - {None}
2025-05-06 16:01:29,540 - WARNING - {None}
2025-05-06 16:01:29,541 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,541 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,542 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,542 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,542 - WARNING - {None}
2025-05-06 16:01:29,542 - WARNING - {None}
2025-05-06 16:01:29,543 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,543 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,543 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,543 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,544 - WARNING - {None}
2025-05-06 16:01:29,544 - WARNING - {None}
2025-05-06 16:01:29,545 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,545 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,545 - WARNING - {None}
2025-05-06 16:01:29,545 - WARNING - {None}
2025-05-06 16:01:29,546 - WARNING - 页签 'DragonBoatGrid' 记录 #42 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,546 - WARNING - 页签 'DragonBoatGrid' 记录 #42 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,547 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,547 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,547 - WARNING - {None}
2025-05-06 16:01:29,547 - WARNING - {None}
2025-05-06 16:01:29,548 - WARNING - {None}
2025-05-06 16:01:29,548 - WARNING - {None}
2025-05-06 16:01:29,549 - WARNING - {None}
2025-05-06 16:01:29,549 - WARNING - {None}
2025-05-06 16:01:29,550 - WARNING - {None}
2025-05-06 16:01:29,550 - WARNING - {None}
2025-05-06 16:01:29,550 - WARNING - {None}
2025-05-06 16:01:29,550 - WARNING - {None}
2025-05-06 16:01:29,551 - WARNING - {None}
2025-05-06 16:01:29,551 - WARNING - {None}
2025-05-06 16:01:29,552 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,552 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,553 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,553 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,554 - WARNING - {None}
2025-05-06 16:01:29,554 - WARNING - {None}
2025-05-06 16:01:29,555 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,555 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,555 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,555 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,556 - WARNING - {None}
2025-05-06 16:01:29,556 - WARNING - {None}
2025-05-06 16:01:29,557 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,557 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,558 - WARNING - {None}
2025-05-06 16:01:29,558 - WARNING - {None}
2025-05-06 16:01:29,558 - WARNING - 页签 'DragonBoatGrid' 记录 #43 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,558 - WARNING - 页签 'DragonBoatGrid' 记录 #43 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,559 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,559 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,560 - WARNING - {None}
2025-05-06 16:01:29,560 - WARNING - {None}
2025-05-06 16:01:29,560 - WARNING - {None}
2025-05-06 16:01:29,560 - WARNING - {None}
2025-05-06 16:01:29,561 - WARNING - {None}
2025-05-06 16:01:29,561 - WARNING - {None}
2025-05-06 16:01:29,562 - WARNING - {None}
2025-05-06 16:01:29,562 - WARNING - {None}
2025-05-06 16:01:29,562 - WARNING - {None}
2025-05-06 16:01:29,562 - WARNING - {None}
2025-05-06 16:01:29,563 - WARNING - {None}
2025-05-06 16:01:29,563 - WARNING - {None}
2025-05-06 16:01:29,563 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,563 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,564 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,564 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,565 - WARNING - {None}
2025-05-06 16:01:29,565 - WARNING - {None}
2025-05-06 16:01:29,566 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,566 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,566 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,566 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,567 - WARNING - {None}
2025-05-06 16:01:29,567 - WARNING - {None}
2025-05-06 16:01:29,568 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,568 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,568 - WARNING - {None}
2025-05-06 16:01:29,568 - WARNING - {None}
2025-05-06 16:01:29,569 - WARNING - 页签 'DragonBoatGrid' 记录 #44 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,569 - WARNING - 页签 'DragonBoatGrid' 记录 #44 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,570 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,570 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,570 - WARNING - {None}
2025-05-06 16:01:29,570 - WARNING - {None}
2025-05-06 16:01:29,571 - WARNING - {None}
2025-05-06 16:01:29,571 - WARNING - {None}
2025-05-06 16:01:29,571 - WARNING - {None}
2025-05-06 16:01:29,571 - WARNING - {None}
2025-05-06 16:01:29,572 - WARNING - {None}
2025-05-06 16:01:29,572 - WARNING - {None}
2025-05-06 16:01:29,572 - WARNING - {None}
2025-05-06 16:01:29,572 - WARNING - {None}
2025-05-06 16:01:29,573 - WARNING - {None}
2025-05-06 16:01:29,573 - WARNING - {None}
2025-05-06 16:01:29,574 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,574 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,574 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,574 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,575 - WARNING - {None}
2025-05-06 16:01:29,575 - WARNING - {None}
2025-05-06 16:01:29,576 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,576 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,576 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,576 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,577 - WARNING - {None}
2025-05-06 16:01:29,577 - WARNING - {None}
2025-05-06 16:01:29,578 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,578 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,578 - WARNING - {None}
2025-05-06 16:01:29,578 - WARNING - {None}
2025-05-06 16:01:29,579 - WARNING - 页签 'DragonBoatGrid' 记录 #45 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,579 - WARNING - 页签 'DragonBoatGrid' 记录 #45 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:01:29,580 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,580 - WARNING - {<re.Match object; span=(0, 8), match='uint[10]'>}
2025-05-06 16:01:29,604 - INFO - 页签 DragonBoatGrid 处理完成
2025-05-06 16:01:29,604 - INFO - 页签 DragonBoatGrid 处理完成
2025-05-06 16:01:29,734 - INFO - 导出完成! 成功: 1, 跳过: 0, 失败: 0
2025-05-06 16:01:29,734 - INFO - 导出完成! 成功: 1, 跳过: 0, 失败: 0
