配置导表工具 v1.1 更新说明
===========================

1. 新增功能: 自定义输出路径
   - 在主界面增加了"输出文件夹路径"选项
   - 如果不指定则默认使用配置路径
   - 可以通过浏览按钮选择自定义的输出路径
   - 输出路径设置会被保存，下次启动时自动加载

2. 界面优化: 添加功能按钮
   - 配置路径和输出路径后新增"打开"按钮，可以直接打开对应文件夹
   - 在文件列表区域添加了"刷新"按钮，可以快速重新扫描当前配置路径下的Excel文件
   - 在日志区域底部添加了"清除日志"按钮，用户可以随时清除日志面板内容
   - 这些按钮使界面操作更加便捷，提升用户体验

3. 修复问题: 数组类型空值显示
   - 修复了数组类型字段为空值时，在可视化文本文件中显示为"NULL"的问题
   - 现在数组类型的空值会显示为空数组"[]"

使用说明:
1. 选择配置文件夹路径（包含Excel文件的目录）
2. 可选：设置自定义输出路径（不指定则使用配置文件夹路径）
3. 如需快速打开配置或输出路径，点击相应的"打开"按钮
4. 如需更新Excel文件列表，点击"刷新"按钮
5. 选择要导出的Excel文件，点击"导出选中"或"导出全部"
6. 导出的文件将保存到指定的输出路径中
7. 可以随时点击"清除日志"按钮清空日志区域

2024年5月17日 