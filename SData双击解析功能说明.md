# 📊 SData文件双击解析功能说明

## 🎯 功能概述

从v2.0版本开始，双击SData文件的行为已从"打开文件所在目录"改为"解析并显示文件内容"，提供更直观的数据查看体验。

## ✨ 主要变更

### 🔄 行为变更对比

| 操作 | 旧版本行为 | 新版本行为 |
|------|------------|------------|
| 双击SData文件 | 打开文件所在目录 | 解析并显示文件内容 |
| 右键菜单 | 包含"打开文件位置" | 保持不变，仍可打开文件位置 |

### 🚀 新功能特性

1. **直接内容查看**
   - 双击即可查看SData文件的结构化内容
   - 无需手动导航到文件位置

2. **智能解析**
   - 自动查找对应的结构体定义文件
   - 根据结构体定义解析二进制数据

3. **表格化显示**
   - 数据以表格形式清晰展示
   - 支持列排序和调整

4. **搜索和过滤**
   - 内置搜索功能，快速定位数据
   - 支持按字段过滤

## 🔧 使用方法

### 基本操作

1. **解析SData文件**
   ```
   步骤1: 切换到"SData"页签
   步骤2: 选择"服务端"或"客户端"子页签
   步骤3: 双击要查看的SData文件
   步骤4: 系统自动打开解析窗口
   ```

2. **查看解析结果**
   ```
   - 窗口顶部显示文件路径和记录数量
   - 主区域以表格形式显示数据
   - 表头包含字段名和注释信息
   ```

3. **搜索数据**
   ```
   - 使用搜索框输入关键词
   - 选择要搜索的字段
   - 点击"搜索"按钮或按回车键
   ```

### 高级功能

1. **导出数据**
   - 支持导出为CSV格式
   - 可选择导出全部或筛选后的数据

2. **列操作**
   - 点击列头进行排序
   - 拖拽列边界调整宽度
   - 右键列头查看更多选项

3. **数据导航**
   - 使用滚动条浏览大量数据
   - 支持键盘方向键导航

## 🔍 解析原理

### 文件识别

SData解析器会自动执行以下步骤：

1. **验证文件格式**
   ```
   检查文件扩展名是否为.SData
   验证文件是否存在且可读
   ```

2. **查找结构体定义**
   ```
   在同目录下查找对应的GameData*.txt文件
   解析结构体定义获取字段信息
   ```

3. **解析二进制数据**
   ```
   读取文件头获取记录数量
   根据结构体定义解析每条记录
   转换为可读的数据格式
   ```

### 支持的数据类型

| 类型 | 说明 | 示例 |
|------|------|------|
| int | 32位整数 | 12345 |
| float | 32位浮点数 | 3.14159 |
| string | 字符串 | "Hello World" |
| bool | 布尔值 | true/false |

## ⚠️ 注意事项

### 解析要求

1. **结构体定义文件**
   - 必须存在对应的GameData*.txt文件
   - 结构体定义格式必须正确
   - 字段类型必须与二进制数据匹配

2. **文件完整性**
   - SData文件不能损坏
   - 文件大小必须与结构体定义匹配

### 常见问题

1. **解析失败**
   ```
   原因: 找不到结构体定义文件
   解决: 确保GameData*.txt文件存在于同一目录
   ```

2. **数据显示异常**
   ```
   原因: 结构体定义与实际数据不匹配
   解决: 检查结构体定义文件的正确性
   ```

3. **性能问题**
   ```
   原因: 文件过大或记录数量过多
   解决: 使用搜索和过滤功能减少显示数据量
   ```

## 🔄 兼容性说明

### 保留的功能

- **右键菜单**：仍可通过右键菜单打开文件所在目录
- **键盘快捷键**：回车键仍可解析选中的SData文件
- **批量操作**：多选和批量复制功能保持不变

### 迁移指南

如果您习惯了旧版本的双击打开目录功能：

1. **使用右键菜单**
   ```
   右键点击SData文件 → 选择"打开文件位置"
   ```

2. **使用工具栏按钮**
   ```
   选中SData文件 → 点击"打开"按钮
   ```

3. **使用快捷键**
   ```
   选中SData文件 → 按Ctrl+O打开目录
   ```

## 🚀 未来增强

计划中的功能改进：

- **数据编辑**：支持直接编辑SData内容
- **比较功能**：对比不同版本的SData文件
- **统计分析**：提供数据统计和分析功能
- **自定义视图**：支持自定义数据显示格式

## 📞 技术支持

如果在使用过程中遇到问题：

1. **检查日志**：查看程序日志获取详细错误信息
2. **验证文件**：确认SData文件和结构体定义文件完整
3. **重启程序**：尝试重启应用程序
4. **联系支持**：向开发团队报告问题

---

**享受更便捷的SData文件查看体验！** 📊✨
