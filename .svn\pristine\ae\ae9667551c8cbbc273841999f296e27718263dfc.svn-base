2025-05-29 17:49:34,191 - INFO - 应用程序基础目录 (用于设置文件): C:\MyFileAudit\导表工具
2025-05-29 17:49:34,191 - INFO - 设置文件路径: C:\MyFileAudit\导表工具\app_settings.json
2025-05-29 17:49:34,371 - INFO - 从 C:\MyFileAudit\导表工具\app_settings.json 加载设置成功。主题: light, 配置路径: C:\MyFileAudit\导表工具\Config
2025-05-29 17:49:34,371 - INFO - 从 C:\MyFileAudit\导表工具\app_settings.json 加载设置成功。主题: light, 配置路径: C:\MyFileAudit\导表工具\Config
2025-05-29 17:49:34,379 - INFO - 正在扫描路径: C:\MyFileAudit\导表工具\Config
2025-05-29 17:49:34,379 - INFO - 正在扫描路径: C:\MyFileAudit\导表工具\Config
2025-05-29 17:49:34,380 - INFO - 找到 2 个Excel配置文件
2025-05-29 17:49:34,380 - INFO - 找到 2 个Excel配置文件
2025-05-29 17:49:34,387 - INFO - 找到 26 个SData文件 (服务端: 13, 客户端: 13)
2025-05-29 17:49:34,387 - INFO - 找到 26 个SData文件 (服务端: 13, 客户端: 13)
2025-05-29 17:49:34,389 - INFO - 找到 13 个服务端加载函数文件和 13 个客户端加载函数文件
2025-05-29 17:49:34,389 - INFO - 找到 13 个服务端加载函数文件和 13 个客户端加载函数文件
2025-05-29 17:49:37,422 - INFO - 开始导出全部 2 个文件...
2025-05-29 17:49:37,422 - INFO - 开始导出全部 2 个文件...
2025-05-29 17:49:37,436 - INFO - 开始处理: test1.xlsx
2025-05-29 17:49:37,436 - INFO - 开始处理: test1.xlsx
2025-05-29 17:49:37,479 - INFO - 页签 Sheet1 处理完成
2025-05-29 17:49:37,479 - INFO - 页签 Sheet1 处理完成
2025-05-29 17:49:37,489 - INFO - 开始处理: 节日活动配置(适配端午).xlsx
2025-05-29 17:49:37,489 - INFO - 开始处理: 节日活动配置(适配端午).xlsx
2025-05-29 17:49:37,528 - ERROR - 页签 'DragonBoatEvent' 记录 #12 行 'dwCurrency' 字段的第 1 个元素 '[201, 1]' 不是有效的数值，将使用默认值0
2025-05-29 17:49:37,528 - ERROR - 页签 'DragonBoatEvent' 记录 #12 行 'dwCurrency' 字段的第 1 个元素 '[201, 1]' 不是有效的数值，将使用默认值0
2025-05-29 17:49:37,530 - ERROR - 页签 'DragonBoatEvent' 记录 #15 字段 'dwReward' (int[4][2]) 要求二维数组格式 (例如 '[[1,2],[3,4]]')，但提供的值 '[1001,100]' 解析后结构不正确 (不是列表的列表)。
2025-05-29 17:49:37,530 - ERROR - 页签 'DragonBoatEvent' 记录 #15 字段 'dwReward' (int[4][2]) 要求二维数组格式 (例如 '[[1,2],[3,4]]')，但提供的值 '[1001,100]' 解析后结构不正确 (不是列表的列表)。
2025-05-29 17:49:37,556 - INFO - 页签 FestivalQuestActive 处理完成
2025-05-29 17:49:37,556 - INFO - 页签 FestivalQuestActive 处理完成
2025-05-29 17:49:37,559 - INFO - 页签 DragonBoatGrid 处理完成
2025-05-29 17:49:37,561 - INFO - 页签 CurrencySystem 处理完成
2025-05-29 17:49:37,559 - INFO - 页签 DragonBoatGrid 处理完成
2025-05-29 17:49:37,561 - INFO - 页签 CurrencySystem 处理完成
2025-05-29 17:49:37,563 - WARNING - 页签 'FestivalAct' 忽略 G 列 ('备注
策划自用')
2025-05-29 17:49:37,563 - WARNING - 页签 'FestivalAct' 忽略 G 列 ('备注
策划自用')
2025-05-29 17:49:37,564 - WARNING - 页签 'FestivalActController' 忽略 M 列 ('类型备注(策划自用)')
2025-05-29 17:49:37,564 - INFO - 页签 DragonBoatBoard 处理完成
2025-05-29 17:49:37,564 - WARNING - 页签 'FestivalActController' 忽略 M 列 ('类型备注(策划自用)')
2025-05-29 17:49:37,564 - INFO - 页签 DragonBoatBoard 处理完成
2025-05-29 17:49:37,568 - WARNING - 页签 'FestivalShop' 忽略 N 列 ('备注')
2025-05-29 17:49:37,568 - WARNING - 页签 'FestivalShop' 忽略 N 列 ('备注')
2025-05-29 17:49:37,573 - INFO - 页签 DragonBoatRankReward 处理完成
2025-05-29 17:49:37,573 - INFO - 页签 DragonBoatRankReward 处理完成
2025-05-29 17:49:37,582 - INFO - 页签 FestivalAct 处理完成
2025-05-29 17:49:37,582 - INFO - 页签 FestivalAct 处理完成
2025-05-29 17:49:37,589 - INFO - 页签 FestivalActSignIn 处理完成
2025-05-29 17:49:37,589 - INFO - 页签 FestivalActSignIn 处理完成
2025-05-29 17:49:37,590 - INFO - 页签 FestivalShop 处理完成
2025-05-29 17:49:37,590 - INFO - 页签 FestivalActController 处理完成
2025-05-29 17:49:37,590 - INFO - 页签 FestivalShop 处理完成
2025-05-29 17:49:37,590 - INFO - 页签 FestivalActController 处理完成
2025-05-29 17:49:37,599 - INFO - 页签 FestivalQuest 处理完成
2025-05-29 17:49:37,599 - INFO - 页签 FestivalQuest 处理完成
2025-05-29 17:49:37,603 - INFO - 页签 FestivalActWarOrderLevel 处理完成
2025-05-29 17:49:37,603 - INFO - 页签 FestivalActWarOrderLevel 处理完成
2025-05-29 17:49:37,623 - INFO - 导出完成! 成功: 1, 跳过: 0, 失败: 1
2025-05-29 17:49:37,623 - INFO - 导出完成! 成功: 1, 跳过: 0, 失败: 1
2025-05-29 17:49:37,635 - INFO - 找到 26 个SData文件 (服务端: 13, 客户端: 13)
2025-05-29 17:49:37,635 - INFO - 找到 26 个SData文件 (服务端: 13, 客户端: 13)
2025-05-29 17:49:37,637 - INFO - 找到 13 个服务端加载函数文件和 13 个客户端加载函数文件
2025-05-29 17:49:37,637 - INFO - 找到 13 个服务端加载函数文件和 13 个客户端加载函数文件
2025-05-29 17:50:44,802 - INFO - 正在打开Excel文件: 节日活动配置(适配端午).xlsx
2025-05-29 17:50:44,802 - INFO - 正在打开Excel文件: 节日活动配置(适配端午).xlsx
2025-05-29 17:51:06,796 - INFO - 开始导出全部 2 个文件...
2025-05-29 17:51:06,796 - INFO - 开始导出全部 2 个文件...
2025-05-29 17:51:06,798 - INFO - 开始处理: test1.xlsx
2025-05-29 17:51:06,798 - INFO - 开始处理: test1.xlsx
2025-05-29 17:51:06,837 - INFO - 页签 Sheet1 处理完成
2025-05-29 17:51:06,837 - INFO - 页签 Sheet1 处理完成
2025-05-29 17:51:06,892 - INFO - 开始处理: 节日活动配置(适配端午).xlsx
2025-05-29 17:51:06,892 - INFO - 开始处理: 节日活动配置(适配端午).xlsx
2025-05-29 17:51:06,941 - INFO - 页签 FestivalQuestActive 处理完成
2025-05-29 17:51:06,941 - INFO - 页签 FestivalQuestActive 处理完成
2025-05-29 17:51:06,942 - INFO - 页签 CurrencySystem 处理完成
2025-05-29 17:51:06,944 - INFO - 页签 DragonBoatEvent 处理完成
2025-05-29 17:51:06,946 - INFO - 页签 DragonBoatGrid 处理完成
2025-05-29 17:51:06,942 - INFO - 页签 CurrencySystem 处理完成
2025-05-29 17:51:06,944 - INFO - 页签 DragonBoatEvent 处理完成
2025-05-29 17:51:06,946 - INFO - 页签 DragonBoatGrid 处理完成
2025-05-29 17:51:06,948 - WARNING - 页签 'FestivalAct' 忽略 G 列 ('备注
策划自用')
2025-05-29 17:51:06,949 - WARNING - 页签 'FestivalActController' 忽略 M 列 ('类型备注(策划自用)')
2025-05-29 17:51:06,948 - WARNING - 页签 'FestivalAct' 忽略 G 列 ('备注
策划自用')
2025-05-29 17:51:06,949 - WARNING - 页签 'FestivalActController' 忽略 M 列 ('类型备注(策划自用)')
2025-05-29 17:51:06,955 - INFO - 页签 DragonBoatBoard 处理完成
2025-05-29 17:51:06,955 - INFO - 页签 DragonBoatBoard 处理完成
2025-05-29 17:51:06,956 - WARNING - 页签 'FestivalShop' 忽略 N 列 ('备注')
2025-05-29 17:51:06,957 - INFO - 页签 DragonBoatRankReward 处理完成
2025-05-29 17:51:06,956 - WARNING - 页签 'FestivalShop' 忽略 N 列 ('备注')
2025-05-29 17:51:06,957 - INFO - 页签 DragonBoatRankReward 处理完成
2025-05-29 17:51:06,961 - INFO - 页签 FestivalAct 处理完成
2025-05-29 17:51:06,961 - INFO - 页签 FestivalAct 处理完成
2025-05-29 17:51:06,963 - INFO - 页签 FestivalActController 处理完成
2025-05-29 17:51:06,963 - INFO - 页签 FestivalActController 处理完成
2025-05-29 17:51:06,973 - INFO - 页签 FestivalShop 处理完成
2025-05-29 17:51:06,973 - INFO - 页签 FestivalShop 处理完成
2025-05-29 17:51:06,974 - INFO - 页签 FestivalActSignIn 处理完成
2025-05-29 17:51:06,974 - INFO - 页签 FestivalActSignIn 处理完成
2025-05-29 17:51:06,979 - INFO - 页签 FestivalActWarOrderLevel 处理完成
2025-05-29 17:51:06,979 - INFO - 页签 FestivalQuest 处理完成
2025-05-29 17:51:06,979 - INFO - 页签 FestivalActWarOrderLevel 处理完成
2025-05-29 17:51:06,979 - INFO - 页签 FestivalQuest 处理完成
2025-05-29 17:51:06,986 - INFO - 导出完成! 成功: 2, 跳过: 0, 失败: 0
2025-05-29 17:51:06,986 - INFO - 导出完成! 成功: 2, 跳过: 0, 失败: 0
2025-05-29 17:51:07,000 - INFO - 找到 26 个SData文件 (服务端: 13, 客户端: 13)
2025-05-29 17:51:07,000 - INFO - 找到 26 个SData文件 (服务端: 13, 客户端: 13)
2025-05-29 17:51:07,002 - INFO - 找到 13 个服务端加载函数文件和 13 个客户端加载函数文件
2025-05-29 17:51:07,002 - INFO - 找到 13 个服务端加载函数文件和 13 个客户端加载函数文件
