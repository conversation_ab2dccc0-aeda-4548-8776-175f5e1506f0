数据条数: 40
dwGridId (uint):1
dwBoardId (uint):1005
dwRow (uint):1
dwCol (uint):1
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):1
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 4000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 4000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):2
dwBoardId (uint):1005
dwRow (uint):1
dwCol (uint):2
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):2
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 2000, 5000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 2000, 5000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):3
dwBoardId (uint):1005
dwRow (uint):1
dwCol (uint):3
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):3
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[4000, 3000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[4000, 3000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):4
dwBoardId (uint):1005
dwRow (uint):1
dwCol (uint):4
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):4
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 5000, 2000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 5000, 2000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):5
dwBoardId (uint):1005
dwRow (uint):1
dwCol (uint):5
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):5
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 4000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 4000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):6
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):6
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 2000, 5000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 2000, 5000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):7
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):7
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[4000, 3000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[4000, 3000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):8
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):8
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 5000, 2000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 5000, 2000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):9
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):9
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 4000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 4000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):10
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):10
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 2000, 5000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 2000, 5000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):11
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):11
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[4000, 3000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[4000, 3000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):12
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):12
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 5000, 2000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 5000, 2000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):13
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):13
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 4000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 4000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):14
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):14
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 2000, 5000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 2000, 5000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):15
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):15
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[4000, 3000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[4000, 3000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):16
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):16
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 5000, 2000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 5000, 2000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):17
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):17
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 4000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 4000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):18
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):18
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 2000, 5000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 2000, 5000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):19
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):19
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[4000, 3000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[4000, 3000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):20
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):20
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 5000, 2000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 5000, 2000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):21
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):21
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 4000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 4000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):22
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):22
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 2000, 5000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 2000, 5000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):23
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):23
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[4000, 3000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[4000, 3000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):24
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):24
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 5000, 2000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 5000, 2000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):25
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):25
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 4000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 4000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):26
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):26
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 2000, 5000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 2000, 5000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):27
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):27
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[4000, 3000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[4000, 3000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):28
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):28
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 5000, 2000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 5000, 2000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):29
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):29
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 4000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 4000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):30
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):30
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 2000, 5000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 2000, 5000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):31
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):31
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[4000, 3000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[4000, 3000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):32
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):32
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 5000, 2000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 5000, 2000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):33
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):33
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 4000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 4000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):34
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):34
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 2000, 5000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 2000, 5000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):35
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):35
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[4000, 3000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[4000, 3000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):36
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):36
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 5000, 2000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 5000, 2000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):37
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):37
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 4000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 4000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):38
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):1
dwNextId (uint):0
dwRefreshId (uint):38
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 2000, 5000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 2000, 5000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):39
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):39
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[4000, 3000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[4000, 3000, 3000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

dwGridId (uint):40
dwBoardId (uint):1005
dwRow (uint):0
dwCol (uint):0
byGridType (byte):2
dwNextId (uint):0
dwRefreshId (uint):40
dwEmptyEventRate (uint):3000
dwBackEventRate (uint):3000
dwBackEventIds (uint[10]):[10001, 10002, 10003, 0, 0, 0, 0, 0, 0, 0]
dwBackEventWeights (uint[10]):[3000, 5000, 2000, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventRate (uint):3000
dwForwardEventIds (uint[10]):[20001, 20002, 20003, 0, 0, 0, 0, 0, 0, 0]
dwForwardEventWeights (uint[10]):[3000, 5000, 2000, 0, 0, 0, 0, 0, 0, 0]
dwSpecialEventRate (uint):1000
dwSpecialEventWeights (uint[10]):[40001, 40002, 40003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventIds (uint[10]):[30001, 30002, 30003, 0, 0, 0, 0, 0, 0, 0]
dwRewardEventWeights (uint[10]):[1000, 2000, 3000, 0, 0, 0, 0, 0, 0, 0]

