#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import struct
import re
import ast
import json
from utils.sdata_parser import SDataParser
from core.type_converter import TypeConverter

class DebugTools:
    """调试工具类，用于辅助排查生成文件问题"""
    
    @staticmethod
    def compare_binary_and_text(sdata_path, txt_path):
        """比较二进制文件和文本文件的内容
        
        Args:
            sdata_path: SData二进制文件路径
            txt_path: 文本文件路径
            
        Returns:
            str: 比较结果文本
        """
        # 确保文件存在
        if not os.path.exists(sdata_path):
            return f"二进制文件不存在: {sdata_path}"
        if not os.path.exists(txt_path):
            return f"文本文件不存在: {txt_path}"
        
        # 解析二进制文件
        parser = SDataParser()
        binary_content = parser.parse_sdata_file(sdata_path)
        
        # 读取文本文件
        with open(txt_path, 'r', encoding='utf-8') as f:
            text_content = f.read()
        
        # 返回比较结果
        result = [
            "=== 二进制文件内容 ===",
            binary_content,
            "",
            "=== 文本文件内容 ===",
            text_content
        ]
        
        return '\n'.join(result)
    
    @staticmethod
    def generate_debug_binary_file(file_path, field_data):
        """生成简单的调试用二进制文件
        
        Args:
            file_path: 输出文件路径
            field_data: 字段数据，形如 [{"name": "字段名", "type": "类型", "value": 值}, ...]
            
        Returns:
            bool: 是否成功
        """
        try:
            converter = TypeConverter()
            
            with open(file_path, 'wb') as f:
                # 写入记录数 (1)
                f.write(struct.pack('<I', 1))
                
                # 写入每个字段
                for field in field_data:
                    binary_data = converter.convert_to_binary(field["value"], field["type"])
                    f.write(binary_data)
                    
            # 创建一个辅助的文本文件记录写入的内容
            with open(file_path + '.txt', 'w', encoding='utf-8') as f:
                f.write(f"调试文件: {file_path}\n\n")
                f.write(f"记录数: 1\n\n")
                
                for field in field_data:
                    name = field["name"]
                    type_str = field["type"]
                    value = field["value"]
                    
                    f.write(f"字段: {name} ({type_str})\n")
                    f.write(f"值: {value}\n")
                    
                    binary_data = converter.convert_to_binary(value, type_str)
                    hex_data = binary_data.hex()
                    formatted_hex = ' '.join([hex_data[i:i+2] for i in range(0, len(hex_data), 2)])
                    f.write(f"二进制表示: {formatted_hex}\n\n")
            
            return True
        except Exception as e:
            print(f"生成调试文件失败: {str(e)}")
            return False

# 使用示例
if __name__ == "__main__":
    # 测试生成一个简单的调试文件，包含常见的问题字段
    test_fields = [
        {"name": "dwId", "type": "uint", "value": 1003001},
        {"name": "charTets", "type": "char[32]", "value": "撒大大"},
        {"name": "charTets2", "type": "char[3][32]", "value": ["每日登录", "完成副本", "击杀BOSS"]}
    ]
    
    DebugTools.generate_debug_binary_file("test_debug.sdata", test_fields)
    print("调试文件生成完成，请查看 test_debug.sdata 和 test_debug.sdata.txt") 