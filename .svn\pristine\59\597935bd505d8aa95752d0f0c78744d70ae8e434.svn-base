2025-05-06 16:03:27,904 - INFO - 正在扫描路径: C:/MyFileAudit/导表工具/config
2025-05-06 16:03:27,904 - INFO - 正在扫描路径: C:/MyFileAudit/导表工具/config
2025-05-06 16:03:27,905 - INFO - 找到 1 个Excel配置文件
2025-05-06 16:03:27,905 - INFO - 找到 1 个Excel配置文件
2025-05-06 16:03:29,981 - INFO - 开始导出全部 1 个文件...
2025-05-06 16:03:29,981 - INFO - 开始导出全部 1 个文件...
2025-05-06 16:03:29,992 - INFO - 开始处理: DragonBoatFestival.xlsx
2025-05-06 16:03:29,992 - INFO - 开始处理: DragonBoatFestival.xlsx
2025-05-06 16:03:30,041 - WARNING - 页签 'DragonBoatGrid' 忽略列 S，因为该列缺少类型或字段名
2025-05-06 16:03:30,041 - WARNING - 页签 'DragonBoatGrid' 忽略列 S，因为该列缺少类型或字段名
2025-05-06 16:03:30,043 - WARNING - 页签 'DragonBoatGrid' 忽略列 T，因为该列缺少类型或字段名
2025-05-06 16:03:30,043 - WARNING - 页签 'DragonBoatGrid' 忽略列 T，因为该列缺少类型或字段名
2025-05-06 16:03:30,045 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,045 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,047 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,047 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,054 - WARNING - 表格 'DragonBoatEvent' 记录 #7 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,054 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,054 - WARNING - 表格 'DragonBoatEvent' 记录 #7 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,054 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,056 - WARNING - 表格 'DragonBoatEvent' 记录 #8 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,057 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,056 - WARNING - 表格 'DragonBoatEvent' 记录 #8 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,057 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,059 - WARNING - 表格 'DragonBoatEvent' 记录 #9 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,060 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,059 - WARNING - 表格 'DragonBoatEvent' 记录 #9 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,060 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,064 - WARNING - 页签 'DragonBoatGrid' 记录 #6 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,064 - WARNING - 页签 'DragonBoatGrid' 记录 #6 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,066 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,066 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,067 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,067 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,068 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,068 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,069 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,073 - INFO - 页签 CurrencySystem 处理完成
2025-05-06 16:03:30,069 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,075 - INFO - 页签 FestivalQuestActive 处理完成
2025-05-06 16:03:30,073 - INFO - 页签 CurrencySystem 处理完成
2025-05-06 16:03:30,077 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,075 - INFO - 页签 FestivalQuestActive 处理完成
2025-05-06 16:03:30,077 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,080 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,080 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,082 - WARNING - 页签 'DragonBoatGrid' 记录 #7 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,082 - WARNING - 表格 'DragonBoatEvent' 记录 #7 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,082 - WARNING - 页签 'DragonBoatGrid' 记录 #7 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,082 - WARNING - 表格 'DragonBoatEvent' 记录 #7 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,083 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,084 - WARNING - 表格 'DragonBoatEvent' 记录 #8 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,083 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,084 - WARNING - 表格 'DragonBoatEvent' 记录 #8 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,085 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,086 - WARNING - 表格 'DragonBoatEvent' 记录 #9 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,085 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,087 - WARNING - 表格 'DragonBoatRankReward' 记录 #1 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,086 - WARNING - 表格 'DragonBoatEvent' 记录 #9 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,088 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,087 - WARNING - 表格 'DragonBoatRankReward' 记录 #1 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,088 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,089 - WARNING - 表格 'DragonBoatRankReward' 记录 #2 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,090 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,089 - WARNING - 表格 'DragonBoatRankReward' 记录 #2 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,090 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,092 - INFO - 页签 DragonBoatEvent 处理完成
2025-05-06 16:03:30,092 - WARNING - 表格 'DragonBoatRankReward' 记录 #3 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,092 - INFO - 页签 DragonBoatEvent 处理完成
2025-05-06 16:03:30,093 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,092 - WARNING - 表格 'DragonBoatRankReward' 记录 #3 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,093 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,094 - WARNING - 页签 'FestivalAct' 忽略列 G (说明: '备注
策划自用')，因为该列缺少类型或字段名
2025-05-06 16:03:30,095 - INFO - 页签 DragonBoatBoard 处理完成
2025-05-06 16:03:30,095 - WARNING - 表格 'DragonBoatRankReward' 记录 #4 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,094 - WARNING - 页签 'FestivalAct' 忽略列 G (说明: '备注
策划自用')，因为该列缺少类型或字段名
2025-05-06 16:03:30,096 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,095 - INFO - 页签 DragonBoatBoard 处理完成
2025-05-06 16:03:30,095 - WARNING - 表格 'DragonBoatRankReward' 记录 #4 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,096 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,098 - WARNING - 页签 'FestivalActController' 忽略列 M (说明: '类型备注(策划自用)')，因为该列缺少类型或字段名
2025-05-06 16:03:30,098 - WARNING - 表格 'DragonBoatRankReward' 记录 #5 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,098 - WARNING - 页签 'FestivalActController' 忽略列 M (说明: '类型备注(策划自用)')，因为该列缺少类型或字段名
2025-05-06 16:03:30,098 - WARNING - 表格 'DragonBoatRankReward' 记录 #5 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,099 - WARNING - 页签 'DragonBoatGrid' 记录 #8 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,099 - WARNING - 页签 'DragonBoatGrid' 记录 #8 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,100 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwPreviewStartTime' 字段的值 '2025-05-01 12:34:56' 不是有效的数值
2025-05-06 16:03:30,100 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwPreviewStartTime' 字段的值 '2025-05-01 12:34:56' 不是有效的数值
2025-05-06 16:03:30,101 - WARNING - 表格 'DragonBoatRankReward' 记录 #6 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,102 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,101 - WARNING - 表格 'DragonBoatRankReward' 记录 #6 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,103 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwStartTime' 字段的值 '2025-05-02 12:34:56' 不是有效的数值
2025-05-06 16:03:30,102 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,104 - WARNING - 表格 'DragonBoatRankReward' 记录 #7 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,103 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwStartTime' 字段的值 '2025-05-02 12:34:56' 不是有效的数值
2025-05-06 16:03:30,104 - WARNING - 表格 'DragonBoatRankReward' 记录 #7 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,105 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,105 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,106 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwEndTime' 字段的值 '2025-05-03 12:34:56' 不是有效的数值
2025-05-06 16:03:30,106 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwEndTime' 字段的值 '2025-05-03 12:34:56' 不是有效的数值
2025-05-06 16:03:30,108 - WARNING - 表格 'DragonBoatRankReward' 记录 #8 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,108 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,108 - WARNING - 表格 'DragonBoatRankReward' 记录 #8 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,109 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwRewardEndTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:03:30,108 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,110 - INFO - 页签 FestivalAct 处理完成
2025-05-06 16:03:30,110 - WARNING - 表格 'DragonBoatRankReward' 记录 #9 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,109 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwRewardEndTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:03:30,111 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,110 - INFO - 页签 FestivalAct 处理完成
2025-05-06 16:03:30,112 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwPreviewStartTime' 字段的值 '2025-05-02 12:34:56' 不是有效的数值
2025-05-06 16:03:30,110 - WARNING - 表格 'DragonBoatRankReward' 记录 #9 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,113 - WARNING - 页签 'FestivalShop' 忽略列 N (说明: '备注')，因为该列缺少类型或字段名
2025-05-06 16:03:30,111 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,114 - WARNING - 表格 'DragonBoatRankReward' 记录 #10 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,112 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwPreviewStartTime' 字段的值 '2025-05-02 12:34:56' 不是有效的数值
2025-05-06 16:03:30,115 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,113 - WARNING - 页签 'FestivalShop' 忽略列 N (说明: '备注')，因为该列缺少类型或字段名
2025-05-06 16:03:30,115 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwStartTime' 字段的值 '2025-05-03 12:34:56' 不是有效的数值
2025-05-06 16:03:30,114 - WARNING - 表格 'DragonBoatRankReward' 记录 #10 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,116 - WARNING - 页签 'FestivalShop' 记录 #6 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:03:30,115 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,117 - WARNING - 表格 'DragonBoatRankReward' 记录 #11 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,115 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwStartTime' 字段的值 '2025-05-03 12:34:56' 不是有效的数值
2025-05-06 16:03:30,118 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,116 - WARNING - 页签 'FestivalShop' 记录 #6 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:03:30,119 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwEndTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:03:30,117 - WARNING - 表格 'DragonBoatRankReward' 记录 #11 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,120 - WARNING - 页签 'FestivalShop' 记录 #6 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:03:30,118 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,120 - WARNING - 表格 'DragonBoatRankReward' 记录 #12 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,119 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwEndTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:03:30,121 - WARNING - 页签 'DragonBoatGrid' 记录 #9 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,120 - WARNING - 页签 'FestivalShop' 记录 #6 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:03:30,122 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwRewardEndTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:03:30,120 - WARNING - 表格 'DragonBoatRankReward' 记录 #12 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,123 - WARNING - 页签 'FestivalShop' 记录 #7 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:03:30,121 - WARNING - 页签 'DragonBoatGrid' 记录 #9 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,123 - WARNING - 表格 'DragonBoatRankReward' 记录 #13 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,122 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwRewardEndTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:03:30,124 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,125 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwPreviewStartTime' 字段的值 '2025-05-03 12:34:56' 不是有效的数值
2025-05-06 16:03:30,123 - WARNING - 页签 'FestivalShop' 记录 #7 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:03:30,123 - WARNING - 表格 'DragonBoatRankReward' 记录 #13 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,124 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,125 - WARNING - 页签 'FestivalShop' 记录 #7 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:03:30,126 - WARNING - 表格 'DragonBoatRankReward' 记录 #14 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,125 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwPreviewStartTime' 字段的值 '2025-05-03 12:34:56' 不是有效的数值
2025-05-06 16:03:30,127 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,125 - WARNING - 页签 'FestivalShop' 记录 #7 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:03:30,126 - WARNING - 表格 'DragonBoatRankReward' 记录 #14 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,128 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwStartTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:03:30,127 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,129 - WARNING - 页签 'FestivalShop' 记录 #8 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:03:30,128 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwStartTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:03:30,130 - WARNING - 表格 'DragonBoatRankReward' 记录 #15 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,129 - WARNING - 页签 'FestivalShop' 记录 #8 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:03:30,130 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,130 - WARNING - 表格 'DragonBoatRankReward' 记录 #15 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,131 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwEndTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:03:30,132 - WARNING - 页签 'FestivalShop' 记录 #8 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:03:30,130 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,132 - WARNING - 表格 'DragonBoatRankReward' 记录 #16 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,131 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwEndTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:03:30,134 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,132 - WARNING - 页签 'FestivalShop' 记录 #8 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:03:30,134 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwRewardEndTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:03:30,132 - WARNING - 表格 'DragonBoatRankReward' 记录 #16 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,135 - WARNING - 页签 'FestivalShop' 记录 #9 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:03:30,134 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,136 - WARNING - 表格 'DragonBoatRankReward' 记录 #17 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,134 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwRewardEndTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:03:30,137 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,135 - WARNING - 页签 'FestivalShop' 记录 #9 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:03:30,136 - WARNING - 表格 'DragonBoatRankReward' 记录 #17 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,138 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwPreviewStartTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:03:30,139 - WARNING - 页签 'FestivalShop' 记录 #9 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:03:30,137 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,138 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwPreviewStartTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:03:30,141 - WARNING - 表格 'DragonBoatRankReward' 记录 #18 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,139 - WARNING - 页签 'FestivalShop' 记录 #9 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:03:30,142 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,141 - WARNING - 表格 'DragonBoatRankReward' 记录 #18 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,143 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwStartTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:03:30,142 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,143 - WARNING - 页签 'FestivalShop' 记录 #10 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:03:30,143 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwStartTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:03:30,143 - WARNING - 页签 'FestivalShop' 记录 #10 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:03:30,144 - WARNING - 页签 'DragonBoatGrid' 记录 #10 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,145 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwEndTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:03:30,144 - WARNING - 页签 'DragonBoatGrid' 记录 #10 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,146 - WARNING - 页签 'FestivalShop' 记录 #10 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:03:30,145 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwEndTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:03:30,147 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,146 - WARNING - 页签 'FestivalShop' 记录 #10 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:03:30,148 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwRewardEndTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:03:30,147 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,150 - WARNING - 页签 'FestivalShop' 记录 #11 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:03:30,148 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwRewardEndTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:03:30,151 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,150 - WARNING - 页签 'FestivalShop' 记录 #11 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:03:30,152 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwPreviewStartTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:03:30,153 - WARNING - 表格 'DragonBoatRankReward' 记录 #1 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,151 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,154 - WARNING - 页签 'FestivalShop' 记录 #11 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:03:30,152 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwPreviewStartTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:03:30,154 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,153 - WARNING - 表格 'DragonBoatRankReward' 记录 #1 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,154 - WARNING - 页签 'FestivalShop' 记录 #11 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:03:30,155 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwStartTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:03:30,156 - WARNING - 表格 'DragonBoatRankReward' 记录 #2 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,154 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,157 - WARNING - 页签 'FestivalShop' 记录 #12 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:03:30,155 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwStartTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:03:30,158 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,156 - WARNING - 表格 'DragonBoatRankReward' 记录 #2 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,159 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwEndTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:03:30,157 - WARNING - 页签 'FestivalShop' 记录 #12 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:03:30,159 - WARNING - 表格 'DragonBoatRankReward' 记录 #3 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,158 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,160 - WARNING - 页签 'FestivalShop' 记录 #12 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:03:30,159 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwEndTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:03:30,161 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,159 - WARNING - 表格 'DragonBoatRankReward' 记录 #3 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,162 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwRewardEndTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:03:30,160 - WARNING - 页签 'FestivalShop' 记录 #12 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:03:30,162 - WARNING - 表格 'DragonBoatRankReward' 记录 #4 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,161 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,163 - WARNING - 页签 'FestivalShop' 记录 #13 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:03:30,162 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwRewardEndTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:03:30,164 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,162 - WARNING - 表格 'DragonBoatRankReward' 记录 #4 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,165 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwPreviewStartTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:03:30,163 - WARNING - 页签 'FestivalShop' 记录 #13 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:03:30,166 - WARNING - 表格 'DragonBoatRankReward' 记录 #5 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,164 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,167 - WARNING - 页签 'FestivalShop' 记录 #13 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:03:30,165 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwPreviewStartTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:03:30,168 - WARNING - 页签 'DragonBoatGrid' 记录 #11 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,166 - WARNING - 表格 'DragonBoatRankReward' 记录 #5 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,168 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwStartTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:03:30,167 - WARNING - 页签 'FestivalShop' 记录 #13 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:03:30,169 - WARNING - 表格 'DragonBoatRankReward' 记录 #6 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,168 - WARNING - 页签 'DragonBoatGrid' 记录 #11 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,168 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwStartTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:03:30,170 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,169 - WARNING - 表格 'DragonBoatRankReward' 记录 #6 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,171 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwEndTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:03:30,170 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,172 - WARNING - 表格 'DragonBoatRankReward' 记录 #7 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,171 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwEndTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:03:30,173 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,172 - WARNING - 表格 'DragonBoatRankReward' 记录 #7 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,174 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwRewardEndTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:03:30,173 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,174 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwRewardEndTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:03:30,175 - WARNING - 表格 'DragonBoatRankReward' 记录 #8 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,176 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,177 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwPreviewStartTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:03:30,175 - WARNING - 表格 'DragonBoatRankReward' 记录 #8 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,176 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,178 - WARNING - 表格 'DragonBoatRankReward' 记录 #9 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,177 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwPreviewStartTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:03:30,179 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,178 - WARNING - 表格 'DragonBoatRankReward' 记录 #9 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,181 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwStartTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:03:30,179 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,182 - WARNING - 表格 'DragonBoatRankReward' 记录 #10 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,181 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwStartTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:03:30,183 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,182 - WARNING - 表格 'DragonBoatRankReward' 记录 #10 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,187 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwEndTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:03:30,183 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,187 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwEndTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:03:30,188 - WARNING - 表格 'DragonBoatRankReward' 记录 #11 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,189 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,188 - WARNING - 表格 'DragonBoatRankReward' 记录 #11 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,190 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwRewardEndTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:03:30,189 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,191 - WARNING - 表格 'DragonBoatRankReward' 记录 #12 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,190 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwRewardEndTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:03:30,192 - WARNING - 页签 'DragonBoatGrid' 记录 #12 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,191 - WARNING - 表格 'DragonBoatRankReward' 记录 #12 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,192 - WARNING - 页签 'DragonBoatGrid' 记录 #12 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,193 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwPreviewStartTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:03:30,195 - WARNING - 表格 'DragonBoatRankReward' 记录 #13 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,193 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwPreviewStartTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:03:30,196 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,195 - WARNING - 表格 'DragonBoatRankReward' 记录 #13 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,197 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwStartTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:03:30,196 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,197 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwStartTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:03:30,199 - WARNING - 表格 'DragonBoatRankReward' 记录 #14 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,199 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,199 - WARNING - 表格 'DragonBoatRankReward' 记录 #14 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,200 - INFO - 页签 FestivalShop 处理完成
2025-05-06 16:03:30,200 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwEndTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:03:30,199 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,201 - WARNING - 表格 'DragonBoatRankReward' 记录 #15 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,200 - INFO - 页签 FestivalShop 处理完成
2025-05-06 16:03:30,202 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,200 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwEndTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:03:30,201 - WARNING - 表格 'DragonBoatRankReward' 记录 #15 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,203 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwRewardEndTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:03:30,202 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,204 - WARNING - 表格 'DragonBoatRankReward' 记录 #16 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,203 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwRewardEndTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:03:30,205 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,204 - WARNING - 表格 'DragonBoatRankReward' 记录 #16 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,206 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwPreviewStartTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:03:30,205 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,207 - WARNING - 表格 'FestivalActSignIn' 记录 #1 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,206 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwPreviewStartTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:03:30,208 - WARNING - 表格 'DragonBoatRankReward' 记录 #17 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,209 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,207 - WARNING - 表格 'FestivalActSignIn' 记录 #1 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,209 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwStartTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:03:30,208 - WARNING - 表格 'DragonBoatRankReward' 记录 #17 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,210 - WARNING - 表格 'FestivalActSignIn' 记录 #3 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,209 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,212 - WARNING - 表格 'DragonBoatRankReward' 记录 #18 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,209 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwStartTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:03:30,210 - WARNING - 表格 'FestivalActSignIn' 记录 #3 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,213 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,212 - WARNING - 表格 'DragonBoatRankReward' 记录 #18 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:03:30,214 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwEndTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:03:30,213 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,214 - WARNING - 表格 'FestivalActSignIn' 记录 #5 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,214 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwEndTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:03:30,215 - WARNING - 页签 'DragonBoatGrid' 记录 #13 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,214 - WARNING - 表格 'FestivalActSignIn' 记录 #5 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,216 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwRewardEndTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:03:30,215 - WARNING - 页签 'DragonBoatGrid' 记录 #13 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,217 - WARNING - 表格 'FestivalActSignIn' 记录 #7 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,216 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwRewardEndTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:03:30,217 - WARNING - 表格 'FestivalActSignIn' 记录 #7 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,218 - INFO - 页签 DragonBoatRankReward 处理完成
2025-05-06 16:03:30,219 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,218 - INFO - 页签 DragonBoatRankReward 处理完成
2025-05-06 16:03:30,219 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwPreviewStartTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:03:30,220 - WARNING - 表格 'FestivalActSignIn' 记录 #9 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,219 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,219 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwPreviewStartTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:03:30,223 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,220 - WARNING - 表格 'FestivalActSignIn' 记录 #9 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,223 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwStartTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:03:30,223 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,224 - WARNING - 表格 'FestivalActSignIn' 记录 #11 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,225 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,223 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwStartTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:03:30,224 - WARNING - 表格 'FestivalActSignIn' 记录 #11 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,227 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwEndTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:03:30,228 - WARNING - 表格 'FestivalActSignIn' 记录 #13 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,225 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,227 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwEndTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:03:30,228 - WARNING - 表格 'FestivalActSignIn' 记录 #13 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,229 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,230 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwRewardEndTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:03:30,229 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,231 - WARNING - 表格 'FestivalActSignIn' 记录 #15 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,230 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwRewardEndTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:03:30,232 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,231 - WARNING - 表格 'FestivalActSignIn' 记录 #15 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,232 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwPreviewStartTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:03:30,232 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,233 - WARNING - 表格 'FestivalActSignIn' 记录 #17 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,232 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwPreviewStartTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:03:30,234 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,233 - WARNING - 表格 'FestivalActSignIn' 记录 #17 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,237 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwStartTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:03:30,234 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,237 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwStartTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:03:30,239 - WARNING - 表格 'FestivalActSignIn' 记录 #19 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,239 - WARNING - 页签 'DragonBoatGrid' 记录 #14 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,239 - WARNING - 表格 'FestivalActSignIn' 记录 #19 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,240 - INFO - 页签 FestivalQuest 处理完成
2025-05-06 16:03:30,241 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwEndTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:03:30,239 - WARNING - 页签 'DragonBoatGrid' 记录 #14 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,240 - INFO - 页签 FestivalQuest 处理完成
2025-05-06 16:03:30,241 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwEndTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:03:30,242 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,243 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 I，因为该列缺少类型或字段名
2025-05-06 16:03:30,242 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,244 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwRewardEndTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:03:30,243 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 I，因为该列缺少类型或字段名
2025-05-06 16:03:30,245 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,244 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwRewardEndTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:03:30,246 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 J，因为该列缺少类型或字段名
2025-05-06 16:03:30,245 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,247 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwPreviewStartTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:03:30,246 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 J，因为该列缺少类型或字段名
2025-05-06 16:03:30,248 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,249 - WARNING - 表格 'FestivalActSignIn' 记录 #1 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,247 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwPreviewStartTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:03:30,250 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 K，因为该列缺少类型或字段名
2025-05-06 16:03:30,248 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,249 - WARNING - 表格 'FestivalActSignIn' 记录 #1 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,251 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwStartTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:03:30,252 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,250 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 K，因为该列缺少类型或字段名
2025-05-06 16:03:30,253 - WARNING - 表格 'FestivalActSignIn' 记录 #3 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,251 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwStartTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:03:30,254 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #6 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,252 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,255 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwEndTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:03:30,253 - WARNING - 表格 'FestivalActSignIn' 记录 #3 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,254 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #6 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,256 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,255 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwEndTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:03:30,257 - WARNING - 表格 'FestivalActSignIn' 记录 #5 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,256 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,258 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #6 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,259 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwRewardEndTime' 字段的值 '2025-05-15 12:34:56' 不是有效的数值
2025-05-06 16:03:30,257 - WARNING - 表格 'FestivalActSignIn' 记录 #5 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,259 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,258 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #6 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,260 - WARNING - 表格 'FestivalActSignIn' 记录 #7 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,259 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwRewardEndTime' 字段的值 '2025-05-15 12:34:56' 不是有效的数值
2025-05-06 16:03:30,261 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #7 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,259 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,262 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwPreviewStartTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:03:30,260 - WARNING - 表格 'FestivalActSignIn' 记录 #7 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,261 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #7 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,263 - WARNING - 页签 'DragonBoatGrid' 记录 #15 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,262 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwPreviewStartTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:03:30,264 - WARNING - 表格 'FestivalActSignIn' 记录 #9 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,263 - WARNING - 页签 'DragonBoatGrid' 记录 #15 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,264 - WARNING - 表格 'FestivalActSignIn' 记录 #9 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,265 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #7 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,266 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwStartTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:03:30,266 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,265 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #7 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,267 - WARNING - 表格 'FestivalActSignIn' 记录 #11 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,266 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwStartTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:03:30,268 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #8 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,266 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,269 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwEndTime' 字段的值 '2025-05-15 12:34:56' 不是有效的数值
2025-05-06 16:03:30,267 - WARNING - 表格 'FestivalActSignIn' 记录 #11 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,269 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,268 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #8 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,270 - WARNING - 表格 'FestivalActSignIn' 记录 #13 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,269 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwEndTime' 字段的值 '2025-05-15 12:34:56' 不是有效的数值
2025-05-06 16:03:30,271 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #8 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,269 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,272 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwRewardEndTime' 字段的值 '2025-05-16 12:34:56' 不是有效的数值
2025-05-06 16:03:30,270 - WARNING - 表格 'FestivalActSignIn' 记录 #13 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,272 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,271 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #8 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,272 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwRewardEndTime' 字段的值 '2025-05-16 12:34:56' 不是有效的数值
2025-05-06 16:03:30,273 - WARNING - 表格 'FestivalActSignIn' 记录 #15 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,272 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,274 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #9 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,273 - WARNING - 表格 'FestivalActSignIn' 记录 #15 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,275 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwPreviewStartTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:03:30,274 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #9 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,276 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,275 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwPreviewStartTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:03:30,276 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,277 - WARNING - 表格 'FestivalActSignIn' 记录 #17 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,277 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #9 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,277 - WARNING - 表格 'FestivalActSignIn' 记录 #17 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,278 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwStartTime' 字段的值 '2025-05-15 12:34:56' 不是有效的数值
2025-05-06 16:03:30,277 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #9 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,279 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,278 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwStartTime' 字段的值 '2025-05-15 12:34:56' 不是有效的数值
2025-05-06 16:03:30,280 - WARNING - 表格 'FestivalActSignIn' 记录 #19 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,279 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,281 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #10 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,280 - WARNING - 表格 'FestivalActSignIn' 记录 #19 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:03:30,281 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwEndTime' 字段的值 '2025-05-16 12:34:56' 不是有效的数值
2025-05-06 16:03:30,281 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #10 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,282 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,281 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwEndTime' 字段的值 '2025-05-16 12:34:56' 不是有效的数值
2025-05-06 16:03:30,282 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,283 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #10 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,284 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwRewardEndTime' 字段的值 '2025-05-17 12:34:56' 不是有效的数值
2025-05-06 16:03:30,283 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #10 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,285 - WARNING - 页签 'DragonBoatGrid' 记录 #16 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,284 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwRewardEndTime' 字段的值 '2025-05-17 12:34:56' 不是有效的数值
2025-05-06 16:03:30,285 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #11 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,285 - WARNING - 页签 'DragonBoatGrid' 记录 #16 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,285 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #11 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,287 - INFO - 页签 FestivalActSignIn 处理完成
2025-05-06 16:03:30,287 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,287 - INFO - 页签 FestivalActSignIn 处理完成
2025-05-06 16:03:30,288 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #11 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,287 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,288 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #11 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,289 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,290 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #12 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,289 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,290 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #12 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,291 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,291 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #12 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,291 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,291 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #12 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,293 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,294 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #13 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,293 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,294 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #13 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,295 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,295 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #13 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,295 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,295 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #13 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,297 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,298 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #14 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,297 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,298 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #14 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,298 - WARNING - 页签 'DragonBoatGrid' 记录 #17 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,298 - WARNING - 页签 'DragonBoatGrid' 记录 #17 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,299 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #14 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,300 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,299 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #14 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,300 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,303 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #15 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,303 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #15 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,304 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,304 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,304 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #15 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,304 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #15 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,305 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,305 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,306 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #16 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,306 - INFO - 页签 FestivalActController 处理完成
2025-05-06 16:03:30,306 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #16 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,307 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,306 - INFO - 页签 FestivalActController 处理完成
2025-05-06 16:03:30,308 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #16 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,307 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,308 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #16 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,309 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,309 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #17 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,309 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,309 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #17 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,310 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,310 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,311 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #17 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,312 - WARNING - 页签 'DragonBoatGrid' 记录 #18 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,311 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #17 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,312 - WARNING - 页签 'DragonBoatGrid' 记录 #18 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,313 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #18 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,313 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,313 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #18 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,313 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,314 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #18 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,315 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,314 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #18 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,315 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,316 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #19 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,316 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,316 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #19 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,316 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,317 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #19 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,318 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,317 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #19 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,318 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,319 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #20 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,319 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,319 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #20 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,319 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,320 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #20 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,321 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,320 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #20 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,321 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,322 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #21 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,322 - WARNING - 页签 'DragonBoatGrid' 记录 #19 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,322 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #21 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,322 - WARNING - 页签 'DragonBoatGrid' 记录 #19 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,323 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #21 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,324 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,323 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #21 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,324 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,325 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #22 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,325 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,325 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #22 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,325 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,326 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #22 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,327 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,326 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #22 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,327 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,328 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #23 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,328 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,328 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #23 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,328 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,329 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #23 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,330 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,329 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #23 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,330 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,331 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #24 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,331 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #24 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
0003]
2025-05-06 16:03:30,331 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,332 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #24 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,333 - WARNING - 页签 'DragonBoatGrid' 记录 #20 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,332 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #24 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,333 - WARNING - 页签 'DragonBoatGrid' 记录 #20 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,334 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #25 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,335 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,334 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #25 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,335 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,336 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #25 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,337 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,336 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #25 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,337 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,338 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #26 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,338 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,338 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #26 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,338 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,339 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #26 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,340 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,339 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #26 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,340 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,340 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #27 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,341 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,340 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #27 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,341 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,342 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #27 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,343 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,342 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #27 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,343 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,343 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #28 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,344 - WARNING - 页签 'DragonBoatGrid' 记录 #21 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,343 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #28 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,344 - WARNING - 页签 'DragonBoatGrid' 记录 #21 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,345 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #28 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,346 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,345 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #28 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,346 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,347 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #29 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,348 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,347 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #29 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,348 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,348 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #29 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,349 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,348 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #29 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,349 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,350 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #30 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,351 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,350 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #30 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,351 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,352 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #30 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,353 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,352 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #30 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,353 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,353 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #31 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,354 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,353 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #31 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,354 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,355 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #31 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,355 - WARNING - 页签 'DragonBoatGrid' 记录 #22 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,355 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #31 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,355 - WARNING - 页签 'DragonBoatGrid' 记录 #22 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,356 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #32 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,357 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,356 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #32 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,357 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,358 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #32 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,359 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,358 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #32 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,359 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,360 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #33 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,360 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,360 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #33 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,360 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,361 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #33 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,362 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,361 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #33 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,362 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,363 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #34 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,364 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,363 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #34 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,364 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,364 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #34 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,364 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #34 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,365 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,366 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #35 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,365 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,366 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #35 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,367 - WARNING - 页签 'DragonBoatGrid' 记录 #23 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,368 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #35 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,367 - WARNING - 页签 'DragonBoatGrid' 记录 #23 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,368 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #35 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,368 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,369 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #36 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,368 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,369 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #36 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,370 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,370 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,371 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #36 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,371 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #36 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,371 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,371 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,372 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #37 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,372 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #37 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,373 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,373 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,374 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #37 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,374 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #37 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,375 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,375 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,375 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #38 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,375 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #38 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,376 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,376 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,377 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #38 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,378 - WARNING - 页签 'DragonBoatGrid' 记录 #24 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,377 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #38 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,378 - WARNING - 页签 'DragonBoatGrid' 记录 #24 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,378 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #39 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,379 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,378 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #39 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,379 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,380 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #39 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,381 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,380 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #39 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,381 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,382 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #40 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,382 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,382 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #40 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,382 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,383 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #40 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,384 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,383 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #40 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,384 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,385 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #41 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,386 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,385 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #41 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,386 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,387 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #41 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,387 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,387 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #41 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,387 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,388 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #42 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,389 - WARNING - 页签 'DragonBoatGrid' 记录 #25 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,388 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #42 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,389 - WARNING - 页签 'DragonBoatGrid' 记录 #25 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,390 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #42 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,390 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,390 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #42 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,390 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,391 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #43 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,392 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,391 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #43 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,392 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,392 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #43 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,393 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,392 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #43 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,393 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,394 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #44 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,395 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,394 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #44 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,395 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,395 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #44 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,396 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,395 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #44 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,396 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,397 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #45 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,398 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,397 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #45 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,398 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,399 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #45 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,400 - WARNING - 页签 'DragonBoatGrid' 记录 #26 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,399 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #45 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,400 - WARNING - 页签 'DragonBoatGrid' 记录 #26 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,400 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #46 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,401 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,400 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #46 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,401 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,402 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #46 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,403 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,402 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #46 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,403 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,403 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #47 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,404 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,403 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #47 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,404 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,405 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #47 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,406 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,405 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #47 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,406 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,406 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #48 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,407 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,406 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #48 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,407 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,408 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #48 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,409 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,408 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #48 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,409 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,410 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #49 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,410 - WARNING - 页签 'DragonBoatGrid' 记录 #27 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,410 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #49 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,410 - WARNING - 页签 'DragonBoatGrid' 记录 #27 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,411 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #49 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,412 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,411 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #49 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,412 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,413 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #50 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,413 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,413 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #50 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,413 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,414 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #50 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,415 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,414 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #50 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,415 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,415 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #51 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,416 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,415 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #51 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,416 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,417 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #51 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,417 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,417 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #51 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,417 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,418 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #52 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,419 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,418 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #52 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,419 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,420 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #52 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,420 - WARNING - 页签 'DragonBoatGrid' 记录 #28 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,420 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #52 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,420 - WARNING - 页签 'DragonBoatGrid' 记录 #28 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,421 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #53 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,422 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,421 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #53 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,422 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,423 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #53 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,423 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,423 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #53 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,423 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,424 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #54 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,425 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,424 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #54 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,425 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,426 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #54 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,426 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,426 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #54 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,426 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,427 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #55 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,428 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,427 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #55 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,428 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,429 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #55 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,429 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,429 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #55 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,429 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,430 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #56 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,431 - WARNING - 页签 'DragonBoatGrid' 记录 #29 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,430 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #56 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,431 - WARNING - 页签 'DragonBoatGrid' 记录 #29 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,432 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #56 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,433 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,432 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #56 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,433 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,434 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #57 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,434 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,434 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #57 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,434 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,435 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #57 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,436 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,435 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #57 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,436 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,437 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #58 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,438 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,437 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #58 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,438 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,438 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #58 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,439 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,438 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #58 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,439 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,440 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #59 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,441 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,440 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #59 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,441 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,442 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #59 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,442 - WARNING - 页签 'DragonBoatGrid' 记录 #30 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,442 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #59 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,442 - WARNING - 页签 'DragonBoatGrid' 记录 #30 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,443 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #60 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,444 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,443 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #60 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,444 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,445 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #60 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,446 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,445 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #60 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,446 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,447 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #61 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,447 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,447 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #61 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,447 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,448 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #61 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,449 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,448 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #61 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:03:30,449 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,450 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,450 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,451 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,451 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,452 - WARNING - 页签 'DragonBoatGrid' 记录 #31 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,452 - WARNING - 页签 'DragonBoatGrid' 记录 #31 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,453 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,453 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,457 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,457 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,458 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,458 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,459 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,459 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,460 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,460 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,460 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,460 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,461 - WARNING - 页签 'DragonBoatGrid' 记录 #32 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,461 - WARNING - 页签 'DragonBoatGrid' 记录 #32 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,462 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,462 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,463 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,463 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,466 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,466 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,467 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,467 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,468 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,468 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,469 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,469 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,469 - WARNING - 页签 'DragonBoatGrid' 记录 #33 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,469 - WARNING - 页签 'DragonBoatGrid' 记录 #33 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,470 - INFO - 页签 FestivalActWarOrderLevel 处理完成
2025-05-06 16:03:30,471 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,470 - INFO - 页签 FestivalActWarOrderLevel 处理完成
2025-05-06 16:03:30,471 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,472 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,472 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,472 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,472 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,473 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,473 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,474 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,474 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,474 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,474 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,475 - WARNING - 页签 'DragonBoatGrid' 记录 #34 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,475 - WARNING - 页签 'DragonBoatGrid' 记录 #34 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,476 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,476 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,477 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,477 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,477 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,477 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,478 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,478 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,479 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,479 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,479 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,479 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,480 - WARNING - 页签 'DragonBoatGrid' 记录 #35 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,480 - WARNING - 页签 'DragonBoatGrid' 记录 #35 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,481 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,481 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,481 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,481 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,482 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,482 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,483 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,483 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,484 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,484 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,484 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,484 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,485 - WARNING - 页签 'DragonBoatGrid' 记录 #36 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,485 - WARNING - 页签 'DragonBoatGrid' 记录 #36 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,486 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,486 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,486 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,486 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,487 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,487 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,488 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,488 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,489 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,489 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,489 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,489 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,490 - WARNING - 页签 'DragonBoatGrid' 记录 #37 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,490 - WARNING - 页签 'DragonBoatGrid' 记录 #37 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,491 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,491 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,491 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,491 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,492 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,492 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,493 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,493 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,493 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,493 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,494 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,494 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,495 - WARNING - 页签 'DragonBoatGrid' 记录 #38 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,495 - WARNING - 页签 'DragonBoatGrid' 记录 #38 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,495 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,495 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,496 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,496 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,497 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,497 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,498 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,498 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,498 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,498 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,499 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,499 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,500 - WARNING - 页签 'DragonBoatGrid' 记录 #39 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,500 - WARNING - 页签 'DragonBoatGrid' 记录 #39 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,501 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,501 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,501 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,501 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,502 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,502 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,503 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,503 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,503 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,503 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,504 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,504 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,505 - WARNING - 页签 'DragonBoatGrid' 记录 #40 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,505 - WARNING - 页签 'DragonBoatGrid' 记录 #40 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,506 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,506 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,506 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,506 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,507 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,507 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,508 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,508 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,509 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,509 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,509 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,509 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,510 - WARNING - 页签 'DragonBoatGrid' 记录 #41 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,510 - WARNING - 页签 'DragonBoatGrid' 记录 #41 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,511 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,511 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,511 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,511 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,512 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,512 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,513 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,513 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,513 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,513 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:03:30,514 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,514 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,515 - WARNING - 页签 'DragonBoatGrid' 记录 #42 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,515 - WARNING - 页签 'DragonBoatGrid' 记录 #42 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,516 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,516 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,516 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,516 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,517 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,517 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,518 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,518 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,518 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,518 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:03:30,519 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,519 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,520 - WARNING - 页签 'DragonBoatGrid' 记录 #43 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,520 - WARNING - 页签 'DragonBoatGrid' 记录 #43 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,521 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,521 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,521 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,521 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,522 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,522 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,523 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,523 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,523 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,523 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:03:30,524 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,524 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,525 - WARNING - 页签 'DragonBoatGrid' 记录 #44 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,525 - WARNING - 页签 'DragonBoatGrid' 记录 #44 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,525 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,525 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,526 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,526 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:03:30,527 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,527 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,528 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,528 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:03:30,528 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,528 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:03:30,529 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,529 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:03:30,530 - WARNING - 页签 'DragonBoatGrid' 记录 #45 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,530 - WARNING - 页签 'DragonBoatGrid' 记录 #45 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:03:30,531 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,531 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:03:30,556 - INFO - 页签 DragonBoatGrid 处理完成
2025-05-06 16:03:30,556 - INFO - 页签 DragonBoatGrid 处理完成
2025-05-06 16:03:30,672 - INFO - 导出完成! 成功: 1, 跳过: 0, 失败: 0
2025-05-06 16:03:30,672 - INFO - 导出完成! 成功: 1, 跳过: 0, 失败: 0
2025-05-06 16:07:48,603 - INFO - 正在扫描路径: C:/MyFileAudit/导表工具/config
2025-05-06 16:07:48,603 - INFO - 正在扫描路径: C:/MyFileAudit/导表工具/config
2025-05-06 16:07:48,605 - INFO - 找到 2 个Excel配置文件
2025-05-06 16:07:48,605 - INFO - 找到 2 个Excel配置文件
2025-05-06 16:07:52,019 - INFO - 开始导出选中的 1 个文件...
2025-05-06 16:07:52,019 - INFO - 开始导出选中的 1 个文件...
2025-05-06 16:07:52,024 - INFO - 开始处理: test1.xlsx
2025-05-06 16:07:52,024 - INFO - 开始处理: test1.xlsx
2025-05-06 16:07:52,055 - WARNING - 页签 'Sheet1' 记录 #6 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,055 - WARNING - 页签 'Sheet1' 记录 #6 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,056 - WARNING - 页签 'Sheet1' 记录 #7 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,056 - WARNING - 页签 'Sheet1' 记录 #7 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,058 - WARNING - 页签 'Sheet1' 记录 #8 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,058 - WARNING - 页签 'Sheet1' 记录 #8 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,059 - WARNING - 页签 'Sheet1' 记录 #9 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,059 - WARNING - 页签 'Sheet1' 记录 #9 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,062 - WARNING - 页签 'Sheet1' 记录 #10 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,062 - WARNING - 页签 'Sheet1' 记录 #10 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,064 - WARNING - 页签 'Sheet1' 记录 #11 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,064 - WARNING - 页签 'Sheet1' 记录 #11 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,065 - WARNING - 页签 'Sheet1' 记录 #12 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,065 - WARNING - 页签 'Sheet1' 记录 #12 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,066 - WARNING - 页签 'Sheet1' 记录 #13 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,066 - WARNING - 页签 'Sheet1' 记录 #13 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,068 - WARNING - 页签 'Sheet1' 记录 #14 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,068 - WARNING - 页签 'Sheet1' 记录 #14 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,069 - WARNING - 页签 'Sheet1' 记录 #15 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,069 - WARNING - 页签 'Sheet1' 记录 #15 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,070 - WARNING - 页签 'Sheet1' 记录 #16 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,070 - WARNING - 页签 'Sheet1' 记录 #16 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,072 - WARNING - 页签 'Sheet1' 记录 #17 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,072 - WARNING - 页签 'Sheet1' 记录 #17 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,073 - WARNING - 页签 'Sheet1' 记录 #18 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,073 - WARNING - 页签 'Sheet1' 记录 #18 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,074 - WARNING - 页签 'Sheet1' 记录 #19 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,074 - WARNING - 页签 'Sheet1' 记录 #19 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,075 - WARNING - 页签 'Sheet1' 记录 #20 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,075 - WARNING - 页签 'Sheet1' 记录 #20 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,076 - WARNING - 页签 'Sheet1' 记录 #21 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,076 - WARNING - 页签 'Sheet1' 记录 #21 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,077 - WARNING - 页签 'Sheet1' 记录 #22 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,077 - WARNING - 页签 'Sheet1' 记录 #22 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,078 - WARNING - 页签 'Sheet1' 记录 #23 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,078 - WARNING - 页签 'Sheet1' 记录 #23 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,079 - WARNING - 页签 'Sheet1' 记录 #24 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,079 - WARNING - 页签 'Sheet1' 记录 #24 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,081 - WARNING - 页签 'Sheet1' 记录 #25 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,081 - WARNING - 页签 'Sheet1' 记录 #25 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,082 - WARNING - 页签 'Sheet1' 记录 #26 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,082 - WARNING - 页签 'Sheet1' 记录 #26 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,083 - WARNING - 页签 'Sheet1' 记录 #27 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,083 - WARNING - 页签 'Sheet1' 记录 #27 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,084 - WARNING - 页签 'Sheet1' 记录 #28 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,084 - WARNING - 页签 'Sheet1' 记录 #28 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,085 - WARNING - 页签 'Sheet1' 记录 #29 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,085 - WARNING - 页签 'Sheet1' 记录 #29 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,086 - WARNING - 页签 'Sheet1' 记录 #30 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,086 - WARNING - 页签 'Sheet1' 记录 #30 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,087 - WARNING - 页签 'Sheet1' 记录 #31 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,087 - WARNING - 页签 'Sheet1' 记录 #31 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,089 - WARNING - 页签 'Sheet1' 记录 #32 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,089 - WARNING - 页签 'Sheet1' 记录 #32 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,090 - WARNING - 页签 'Sheet1' 记录 #33 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,090 - WARNING - 页签 'Sheet1' 记录 #33 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,092 - WARNING - 页签 'Sheet1' 记录 #34 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,092 - WARNING - 页签 'Sheet1' 记录 #34 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,093 - WARNING - 页签 'Sheet1' 记录 #35 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,093 - WARNING - 页签 'Sheet1' 记录 #35 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,094 - WARNING - 页签 'Sheet1' 记录 #36 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,094 - WARNING - 页签 'Sheet1' 记录 #36 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,096 - WARNING - 页签 'Sheet1' 记录 #37 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,096 - WARNING - 页签 'Sheet1' 记录 #37 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,097 - WARNING - 页签 'Sheet1' 记录 #38 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,097 - WARNING - 页签 'Sheet1' 记录 #38 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,098 - WARNING - 页签 'Sheet1' 记录 #39 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,098 - WARNING - 页签 'Sheet1' 记录 #39 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,099 - WARNING - 页签 'Sheet1' 记录 #40 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,099 - WARNING - 页签 'Sheet1' 记录 #40 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,100 - WARNING - 页签 'Sheet1' 记录 #41 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,100 - WARNING - 页签 'Sheet1' 记录 #41 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,101 - WARNING - 页签 'Sheet1' 记录 #42 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,101 - WARNING - 页签 'Sheet1' 记录 #42 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,102 - WARNING - 页签 'Sheet1' 记录 #43 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,102 - WARNING - 页签 'Sheet1' 记录 #43 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,103 - WARNING - 页签 'Sheet1' 记录 #44 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,103 - WARNING - 页签 'Sheet1' 记录 #44 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,104 - WARNING - 页签 'Sheet1' 记录 #45 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,104 - WARNING - 页签 'Sheet1' 记录 #45 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:07:52,135 - INFO - 页签 Sheet1 处理完成
2025-05-06 16:07:52,135 - INFO - 页签 Sheet1 处理完成
2025-05-06 16:07:52,146 - INFO - 导出完成! 成功: 1, 跳过: 0, 失败: 0
2025-05-06 16:07:52,146 - INFO - 导出完成! 成功: 1, 跳过: 0, 失败: 0
2025-05-06 16:08:04,963 - INFO - TXT数据文件 Sheet1Client.SData.txt 内容:
2025-05-06 16:08:04,963 - INFO - TXT数据文件 Sheet1Client.SData.txt 内容:
2025-05-06 16:08:11,054 - INFO - TXT数据文件 Sheet1Server.SData.txt 内容:
2025-05-06 16:08:11,054 - INFO - TXT数据文件 Sheet1Server.SData.txt 内容:
2025-05-06 16:08:39,865 - INFO - 开始导出选中的 1 个文件...
2025-05-06 16:08:39,865 - INFO - 开始导出选中的 1 个文件...
2025-05-06 16:08:39,868 - INFO - 开始处理: test1.xlsx
2025-05-06 16:08:39,868 - INFO - 开始处理: test1.xlsx
2025-05-06 16:08:39,898 - WARNING - 页签 'Sheet1' 记录 #6 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,898 - WARNING - 页签 'Sheet1' 记录 #6 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,900 - WARNING - 页签 'Sheet1' 记录 #7 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,900 - WARNING - 页签 'Sheet1' 记录 #7 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,901 - WARNING - 页签 'Sheet1' 记录 #8 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,901 - WARNING - 页签 'Sheet1' 记录 #8 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,903 - WARNING - 页签 'Sheet1' 记录 #9 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,903 - WARNING - 页签 'Sheet1' 记录 #9 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,904 - WARNING - 页签 'Sheet1' 记录 #10 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,904 - WARNING - 页签 'Sheet1' 记录 #10 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,905 - WARNING - 页签 'Sheet1' 记录 #11 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,905 - WARNING - 页签 'Sheet1' 记录 #11 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,907 - WARNING - 页签 'Sheet1' 记录 #12 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,907 - WARNING - 页签 'Sheet1' 记录 #12 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,908 - WARNING - 页签 'Sheet1' 记录 #13 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,908 - WARNING - 页签 'Sheet1' 记录 #13 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,910 - WARNING - 页签 'Sheet1' 记录 #14 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,910 - WARNING - 页签 'Sheet1' 记录 #14 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,912 - WARNING - 页签 'Sheet1' 记录 #15 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,912 - WARNING - 页签 'Sheet1' 记录 #15 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,913 - WARNING - 页签 'Sheet1' 记录 #16 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,913 - WARNING - 页签 'Sheet1' 记录 #16 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,915 - WARNING - 页签 'Sheet1' 记录 #17 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,915 - WARNING - 页签 'Sheet1' 记录 #17 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,916 - WARNING - 页签 'Sheet1' 记录 #18 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,916 - WARNING - 页签 'Sheet1' 记录 #18 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,917 - WARNING - 页签 'Sheet1' 记录 #19 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,917 - WARNING - 页签 'Sheet1' 记录 #19 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,918 - WARNING - 页签 'Sheet1' 记录 #20 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,918 - WARNING - 页签 'Sheet1' 记录 #20 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,919 - WARNING - 页签 'Sheet1' 记录 #21 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,919 - WARNING - 页签 'Sheet1' 记录 #21 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,920 - WARNING - 页签 'Sheet1' 记录 #22 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,920 - WARNING - 页签 'Sheet1' 记录 #22 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,921 - WARNING - 页签 'Sheet1' 记录 #23 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,921 - WARNING - 页签 'Sheet1' 记录 #23 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,923 - WARNING - 页签 'Sheet1' 记录 #24 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,923 - WARNING - 页签 'Sheet1' 记录 #24 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,924 - WARNING - 页签 'Sheet1' 记录 #25 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,924 - WARNING - 页签 'Sheet1' 记录 #25 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,926 - WARNING - 页签 'Sheet1' 记录 #26 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,926 - WARNING - 页签 'Sheet1' 记录 #26 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,927 - WARNING - 页签 'Sheet1' 记录 #27 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,927 - WARNING - 页签 'Sheet1' 记录 #27 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,929 - WARNING - 页签 'Sheet1' 记录 #28 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,929 - WARNING - 页签 'Sheet1' 记录 #28 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,930 - WARNING - 页签 'Sheet1' 记录 #29 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,930 - WARNING - 页签 'Sheet1' 记录 #29 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,931 - WARNING - 页签 'Sheet1' 记录 #30 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,931 - WARNING - 页签 'Sheet1' 记录 #30 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,932 - WARNING - 页签 'Sheet1' 记录 #31 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,932 - WARNING - 页签 'Sheet1' 记录 #31 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,933 - WARNING - 页签 'Sheet1' 记录 #32 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,933 - WARNING - 页签 'Sheet1' 记录 #32 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,935 - WARNING - 页签 'Sheet1' 记录 #33 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,935 - WARNING - 页签 'Sheet1' 记录 #33 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,936 - WARNING - 页签 'Sheet1' 记录 #34 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,936 - WARNING - 页签 'Sheet1' 记录 #34 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,937 - WARNING - 页签 'Sheet1' 记录 #35 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,937 - WARNING - 页签 'Sheet1' 记录 #35 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,938 - WARNING - 页签 'Sheet1' 记录 #36 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,938 - WARNING - 页签 'Sheet1' 记录 #36 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,940 - WARNING - 页签 'Sheet1' 记录 #37 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,940 - WARNING - 页签 'Sheet1' 记录 #37 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,941 - WARNING - 页签 'Sheet1' 记录 #38 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,941 - WARNING - 页签 'Sheet1' 记录 #38 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,943 - WARNING - 页签 'Sheet1' 记录 #39 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,943 - WARNING - 页签 'Sheet1' 记录 #39 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,944 - WARNING - 页签 'Sheet1' 记录 #40 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,944 - WARNING - 页签 'Sheet1' 记录 #40 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,945 - WARNING - 页签 'Sheet1' 记录 #41 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,945 - WARNING - 页签 'Sheet1' 记录 #41 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,946 - WARNING - 页签 'Sheet1' 记录 #42 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,946 - WARNING - 页签 'Sheet1' 记录 #42 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,948 - WARNING - 页签 'Sheet1' 记录 #43 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,948 - WARNING - 页签 'Sheet1' 记录 #43 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,949 - WARNING - 页签 'Sheet1' 记录 #44 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,949 - WARNING - 页签 'Sheet1' 记录 #44 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,950 - WARNING - 页签 'Sheet1' 记录 #45 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,950 - WARNING - 页签 'Sheet1' 记录 #45 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:08:39,976 - INFO - 页签 Sheet1 处理完成
2025-05-06 16:08:39,976 - INFO - 页签 Sheet1 处理完成
2025-05-06 16:08:39,985 - INFO - 导出完成! 成功: 1, 跳过: 0, 失败: 0
2025-05-06 16:08:39,985 - INFO - 导出完成! 成功: 1, 跳过: 0, 失败: 0
