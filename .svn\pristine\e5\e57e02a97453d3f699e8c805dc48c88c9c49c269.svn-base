import struct
import binas<PERSON><PERSON>

def write_test_file():
    # 创建测试数据
    test_data = [1001, 1002, 1003]
    
    # 写入二进制文件 - 方式1：使用数组长度（Python标准方式）
    with open('test1.bin', 'wb') as f:
        # 写入数组长度
        f.write(struct.pack('<I', len(test_data)))
        # 写入数组数据
        for value in test_data:
            f.write(struct.pack('<I', value))
    
    # 写入二进制文件 - 方式2：模拟C++结构体方式（固定长度数组）
    with open('test2.bin', 'wb') as f:
        # 直接写入10个uint32值
        for i in range(10):
            if i < len(test_data):
                f.write(struct.pack('<I', test_data[i]))
            else:
                f.write(struct.pack('<I', 0))  # 填充0
    
    # 读取并验证 - 方式1：使用数组长度（Python标准方式）
    print("\n方式1 - 使用数组长度（Python标准方式）：")
    with open('test1.bin', 'rb') as f:
        # 读取数组长度
        length = struct.unpack('<I', f.read(4))[0]
        print(f"数组长度: {length}")
        
        # 读取数组数据
        values = []
        for _ in range(length):
            value = struct.unpack('<I', f.read(4))[0]
            values.append(value)
        print(f"读取的值: {values}")
    
    # 读取并验证 - 方式2：模拟C++结构体方式（固定长度数组）
    print("\n方式2 - 模拟C++结构体方式（固定长度数组）：")
    with open('test2.bin', 'rb') as f:
        # 直接读取10个uint32值
        values = []
        for _ in range(10):
            value = struct.unpack('<I', f.read(4))[0]
            values.append(value)
        print(f"读取的值: {values}")
    
    # 显示二进制文件的内容
    print("\n二进制文件内容 - 方式1（带长度）：")
    with open('test1.bin', 'rb') as f:
        data = f.read()
        hex_data = binascii.hexlify(data).decode('ascii')
        print(f"十六进制: {hex_data}")
        
        # 按4字节分组显示
        print("\n按4字节分组：")
        for i in range(0, len(hex_data), 8):
            chunk = hex_data[i:i+8]
            print(f"字节 {i//8}: {chunk}")
    
    print("\n二进制文件内容 - 方式2（固定长度）：")
    with open('test2.bin', 'rb') as f:
        data = f.read()
        hex_data = binascii.hexlify(data).decode('ascii')
        print(f"十六进制: {hex_data}")
        
        # 按4字节分组显示
        print("\n按4字节分组：")
        for i in range(0, len(hex_data), 8):
            chunk = hex_data[i:i+8]
            print(f"字节 {i//8}: {chunk}")

if __name__ == '__main__':
    write_test_file() 