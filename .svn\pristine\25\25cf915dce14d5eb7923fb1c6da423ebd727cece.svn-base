#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PIL import Image, ImageDraw, ImageFont
import os

def create_excel_icon(output_file, size=(16, 16)):
    """创建Excel图标
    
    Args:
        output_file: 输出文件路径
        size: 图标大小，默认16x16
    """
    # 创建透明背景的图像
    img = Image.new('RGBA', size, (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制Excel图标 - 绿色背景矩形
    width, height = size
    margin = int(width * 0.12)  # 留出一些边距
    
    # 绘制文档底色 - 浅绿色
    draw.rectangle(
        [(margin, margin), (width - margin, height - margin)],
        fill=(33, 115, 70),  # Excel绿色
        outline=None
    )
    
    # 尝试绘制X字母表示Excel
    try:
        # 尝试使用字体
        font_size = int(height * 0.6)
        font = ImageFont.truetype("arial.ttf", font_size)
        text = "X"
        
        # 计算文本位置使其居中
        text_width = font.getlength(text)
        text_height = font_size
        text_x = (width - text_width) // 2
        text_y = (height - text_height) // 2 - 1  # 稍微上移一点
        
        # 绘制文本
        draw.text((text_x, text_y), text, fill=(255, 255, 255), font=font)
    except Exception:
        # 如果无法加载字体，则绘制简单的X
        x_start, y_start = width // 3, height // 3
        x_end, y_end = width - width // 3, height - height // 3
        
        # 绘制X
        draw.line([(x_start, y_start), (x_end, y_end)], fill=(255, 255, 255), width=2)
        draw.line([(x_start, y_end), (x_end, y_start)], fill=(255, 255, 255), width=2)
    
    # 保存图像
    img.save(output_file, 'PNG')
    print(f"Created Excel icon: {output_file}")

if __name__ == "__main__":
    # 创建图标目录
    icons_dir = os.path.join("resources", "icons")
    os.makedirs(icons_dir, exist_ok=True)
    
    # 创建Excel图标
    create_excel_icon(os.path.join(icons_dir, "excel-file.png"))
    
    # 创建稍大的图标用于界面其他地方
    create_excel_icon(os.path.join(icons_dir, "excel-file-24.png"), size=(24, 24))
    
    print("Excel icons created successfully!") 