#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试SData反向推导对话框是否能正确显示为独立窗口
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.sdata_reverse_tab import BatchProcessDialog, EnhancedStructEditDialog

class TestMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试对话框")
        self.setGeometry(100, 100, 400, 300)
        
        # 模拟app_icon
        self.app_icon = None
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 测试批量处理对话框按钮
        batch_btn = QPushButton("测试批量处理对话框")
        batch_btn.clicked.connect(self.test_batch_dialog)
        layout.addWidget(batch_btn)
        
        # 测试单个文件配置对话框按钮
        single_btn = QPushButton("测试单个文件配置对话框")
        single_btn.clicked.connect(self.test_single_dialog)
        layout.addWidget(single_btn)
        
    def test_batch_dialog(self):
        """测试批量处理对话框"""
        test_files = ["test1.SData", "test2.SData"]
        dialog = BatchProcessDialog(test_files, self)
        dialog.exec_()
        
    def test_single_dialog(self):
        """测试单个文件配置对话框"""
        test_file = "test.SData"
        dialog = EnhancedStructEditDialog(test_file, self)
        dialog.exec_()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = TestMainWindow()
    window.show()
    sys.exit(app.exec_())
