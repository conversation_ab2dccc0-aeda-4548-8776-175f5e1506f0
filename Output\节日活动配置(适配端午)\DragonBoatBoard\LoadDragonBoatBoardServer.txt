BOOL CGameData::LoadDragonBoatBoardServerCfg()
{
    std::string DataPath = "data/DragonBoatBoardServer.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        ::g_pClientToLog->PrintFileDirct("CDataFile::LoadDragonBoatBoardCfg fopen error");
        return FALSE;
    }
    
    m_mapDragonBoatBoardCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stDragonBoatBoardServerCfg cfg;
        fread(&cfg, sizeof(stDragonBoatBoardServerCfg), 1, fp);

        m_mapDragonBoatBoardCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}