BOOL CGameData::LoadDragonBoatEventServerCfg()
{
    std::string DataPath = "data/DragonBoatEventServer.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        ::g_pClientToLog->PrintFileDirct("CDataFile::LoadDragonBoatEventCfg fopen error");
        return FALSE;
    }
    
    m_mapDragonBoatEventCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stDragonBoatEventServerCfg cfg;
        fread(&cfg, sizeof(stDragonBoatEventServerCfg), 1, fp);

        m_mapDragonBoatEventCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}