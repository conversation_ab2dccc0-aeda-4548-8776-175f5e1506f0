BOOL CGameData::LoadDragonBoatGridServerCfg()
{
    std::string DataPath = "data/DragonBoatGridServer.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        ::g_pClientToLog->PrintFileDirct("CDataFile::LoadDragonBoatGridCfg fopen error");
        return FALSE;
    }
    
    m_mapDragonBoatGridCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stDragonBoatGridServerCfg cfg;
        fread(&cfg, sizeof(stDragonBoatGridServerCfg), 1, fp);

        m_mapDragonBoatGridCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}