#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import argparse
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.debug_tools import DebugTools
from utils.sdata_parser import SDataParser

def main():
    """比较二进制文件和文本文件的内容"""
    parser = argparse.ArgumentParser(description='比较SData二进制文件和文本文件')
    parser.add_argument('binary_file', help='SData二进制文件路径')
    parser.add_argument('--text_file', help='文本文件路径(如未指定将使用同名.txt文件)')
    parser.add_argument('--parse', action='store_true', help='直接解析二进制文件并显示')
    args = parser.parse_args()
    
    binary_file = args.binary_file
    
    # 检查二进制文件是否存在
    if not os.path.exists(binary_file):
        print(f"错误：二进制文件不存在 - {binary_file}")
        return
    
    # 如果只需要解析二进制文件
    if args.parse:
        parser = SDataParser()
        result = parser.parse_sdata_file(binary_file)
        print(result)
        return
    
    # 确定文本文件路径
    text_file = args.text_file
    if not text_file:
        # 默认使用同名.txt文件
        base_name = os.path.splitext(binary_file)[0]
        if os.path.exists(f"{base_name}.txt"):
            text_file = f"{base_name}.txt"
        elif os.path.exists(f"{binary_file}.txt"):
            text_file = f"{binary_file}.txt"
        else:
            print(f"错误：找不到对应的文本文件，请使用--text_file参数指定")
            return
    
    # 检查文本文件是否存在
    if not os.path.exists(text_file):
        print(f"错误：文本文件不存在 - {text_file}")
        return
    
    # 比较文件内容
    result = DebugTools.compare_binary_and_text(binary_file, text_file)
    print(result)

if __name__ == "__main__":
    main() 