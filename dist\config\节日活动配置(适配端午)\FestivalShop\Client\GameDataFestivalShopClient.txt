struct stFestivalShopClientCfg
{
    DWORD    dwId;                  // id
    DWORD    dwShopId;              // 对应活动id/活动组id
    DWORD    dwItemId;              // 商品id
    WORD     wItemCount;            // 商品单次售卖数量
    BYTE     byBuyType;             // 购买类型
    BYTE     byLimitType;           // 售卖限购限制类型
    DWORD    wLimitCount;           // 限购次数
    DWORD    wPointCost;            // 充值点数价格
    DWORD    dwCurrencyId;          // 活动货币id
    DWORD    dwCurrencyCost;        // 活动道具id单价数量
    DWORD    dwStartTime;           // 上架时间
    DWORD    dwEndTime;             // 下架时间
    DWORD    dwReturnCurrencyId;    // 返还积分数
};
