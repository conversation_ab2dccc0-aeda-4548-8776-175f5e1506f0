BOOL CGameData::LoadDragonBoatRankRewardClientCfg()
{
    std::string DataPath = "data/DragonBoatRankRewardClient.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        GSLogError(":LoadDragonBoatRankRewardCfg Failed!  [%s]", DataPath.c_str());
        return FALSE;
    }
    
    m_mapDragonBoatRankRewardCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stDragonBoatRankRewardClientCfg cfg;
        fread(&cfg, sizeof(stDragonBoatRankRewardClientCfg), 1, fp);

        m_mapDragonBoatRankRewardCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}