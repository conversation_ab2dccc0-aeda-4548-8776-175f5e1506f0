服务端生成说明：
BOOL CGameData::Load${页签名}ServerCfg()
{
    std::string DataPath = "data/${页签名}Server.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        ::g_pClientToLog->PrintFileDirct("CDataFile::${函数名} fopen error");
        return FALSE;
    }
    
    m_map${页签名}ServerCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        st${页签名}ServerCfg cfg;
        fread(&cfg, sizeof(st${页签名}ServerCfg), 1, fp);

        m_map${页签名}ServerCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}

客户端生成说明：
BOOL CGameData::Load${页签名}ClientCfg()
{
    std::string DataPath = "data/${页签名}Client.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        GSLogError(":${函数名} Failed!  [%s]", DataPath.c_str());
        return FALSE;
    }
    
    m_map${页签名}ClientCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        st${页签名}ClientCfg cfg;
        fread(&cfg, sizeof(st${页签名}ClientCfg), 1, fp);

        m_map${页签名}ClientCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}