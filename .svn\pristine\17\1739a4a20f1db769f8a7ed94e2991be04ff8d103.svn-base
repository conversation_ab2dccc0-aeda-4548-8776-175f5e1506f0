2025-05-06 16:09:02,259 - INFO - 正在扫描路径: C:/MyFileAudit/导表工具/config
2025-05-06 16:09:02,259 - INFO - 正在扫描路径: C:/MyFileAudit/导表工具/config
2025-05-06 16:09:02,260 - INFO - 找到 2 个Excel配置文件
2025-05-06 16:09:02,260 - INFO - 找到 2 个Excel配置文件
2025-05-06 16:09:05,165 - INFO - 开始导出选中的 1 个文件...
2025-05-06 16:09:05,165 - INFO - 开始导出选中的 1 个文件...
2025-05-06 16:09:05,179 - INFO - 开始处理: test1.xlsx
2025-05-06 16:09:05,179 - INFO - 开始处理: test1.xlsx
2025-05-06 16:09:05,212 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,212 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,214 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,214 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,215 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,215 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,216 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,216 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,217 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,217 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,218 - WARNING - 页签 'Sheet1' 记录 #6 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,218 - WARNING - 页签 'Sheet1' 记录 #6 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,219 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,219 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,221 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,221 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,221 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,221 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,223 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,223 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,224 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,224 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,225 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,225 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,226 - WARNING - 页签 'Sheet1' 记录 #7 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,226 - WARNING - 页签 'Sheet1' 记录 #7 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,227 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,227 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,228 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,228 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,229 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,229 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,230 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,230 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,231 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,231 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,232 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,232 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,233 - WARNING - 页签 'Sheet1' 记录 #8 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,233 - WARNING - 页签 'Sheet1' 记录 #8 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,235 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,235 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,236 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,236 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,237 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,237 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,238 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,238 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,239 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,239 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,240 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,240 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,240 - WARNING - 页签 'Sheet1' 记录 #9 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,240 - WARNING - 页签 'Sheet1' 记录 #9 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,241 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,241 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,242 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,242 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,243 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,243 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,244 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,244 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,245 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,245 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,246 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,246 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,247 - WARNING - 页签 'Sheet1' 记录 #10 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,247 - WARNING - 页签 'Sheet1' 记录 #10 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,248 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,248 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,249 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,249 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,249 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,249 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,250 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,250 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,251 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,251 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,253 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,253 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,254 - WARNING - 页签 'Sheet1' 记录 #11 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,254 - WARNING - 页签 'Sheet1' 记录 #11 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,255 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,255 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,256 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,256 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,257 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,257 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,258 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,258 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,259 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,259 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,260 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,260 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,261 - WARNING - 页签 'Sheet1' 记录 #12 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,261 - WARNING - 页签 'Sheet1' 记录 #12 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,262 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,262 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,263 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,263 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,264 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,264 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,265 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,265 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,266 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,266 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,267 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,267 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,268 - WARNING - 页签 'Sheet1' 记录 #13 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,268 - WARNING - 页签 'Sheet1' 记录 #13 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,269 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,269 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,270 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,270 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,271 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,271 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,272 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,272 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,273 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,273 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,274 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,274 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,274 - WARNING - 页签 'Sheet1' 记录 #14 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,274 - WARNING - 页签 'Sheet1' 记录 #14 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,275 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,275 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,276 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,276 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,277 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,277 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,279 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,279 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,280 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,280 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,280 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,280 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,281 - WARNING - 页签 'Sheet1' 记录 #15 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,281 - WARNING - 页签 'Sheet1' 记录 #15 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,282 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,282 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,283 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,283 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,285 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,285 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,286 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,286 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,286 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,286 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,287 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,287 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,288 - WARNING - 页签 'Sheet1' 记录 #16 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,288 - WARNING - 页签 'Sheet1' 记录 #16 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,289 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,289 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,290 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,290 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,291 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,291 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,291 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,291 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,292 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,292 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,293 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,293 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,294 - WARNING - 页签 'Sheet1' 记录 #17 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,294 - WARNING - 页签 'Sheet1' 记录 #17 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,295 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,295 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,296 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,296 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,296 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,296 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,297 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,297 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,298 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,298 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,299 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,299 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,300 - WARNING - 页签 'Sheet1' 记录 #18 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,300 - WARNING - 页签 'Sheet1' 记录 #18 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,301 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,301 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,301 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,301 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,302 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,302 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,303 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,303 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,304 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,304 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,305 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,305 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,306 - WARNING - 页签 'Sheet1' 记录 #19 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,306 - WARNING - 页签 'Sheet1' 记录 #19 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,307 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,307 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,307 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,307 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,308 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,308 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,309 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,309 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,309 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,309 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,310 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,310 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,311 - WARNING - 页签 'Sheet1' 记录 #20 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,311 - WARNING - 页签 'Sheet1' 记录 #20 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,312 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,312 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,313 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,313 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,314 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,314 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,314 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,314 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,315 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,315 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,316 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,316 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,317 - WARNING - 页签 'Sheet1' 记录 #21 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,317 - WARNING - 页签 'Sheet1' 记录 #21 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,318 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,318 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,319 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,319 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,319 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,319 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,320 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,320 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,321 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,321 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,322 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,322 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,323 - WARNING - 页签 'Sheet1' 记录 #22 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,323 - WARNING - 页签 'Sheet1' 记录 #22 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,324 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,324 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,324 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,324 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,325 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,325 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,326 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,326 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,327 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,327 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,328 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,328 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,329 - WARNING - 页签 'Sheet1' 记录 #23 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,329 - WARNING - 页签 'Sheet1' 记录 #23 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,330 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,330 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,331 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,331 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,332 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,332 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,333 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,333 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,334 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,334 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,334 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,334 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,335 - WARNING - 页签 'Sheet1' 记录 #24 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,335 - WARNING - 页签 'Sheet1' 记录 #24 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,336 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,336 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,337 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,337 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,338 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,338 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,338 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,338 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,339 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,339 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,339 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,339 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,340 - WARNING - 页签 'Sheet1' 记录 #25 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,340 - WARNING - 页签 'Sheet1' 记录 #25 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,341 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,341 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,342 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,342 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,342 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,342 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,343 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,343 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,344 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,344 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,345 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,345 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,346 - WARNING - 页签 'Sheet1' 记录 #26 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,346 - WARNING - 页签 'Sheet1' 记录 #26 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,346 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,346 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,347 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,347 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,348 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,348 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,349 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,349 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,350 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,350 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,350 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,350 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,351 - WARNING - 页签 'Sheet1' 记录 #27 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,351 - WARNING - 页签 'Sheet1' 记录 #27 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,352 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,352 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,353 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,353 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,353 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,353 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,354 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,354 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,355 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,355 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,356 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,356 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,356 - WARNING - 页签 'Sheet1' 记录 #28 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,356 - WARNING - 页签 'Sheet1' 记录 #28 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,357 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,357 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,358 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,358 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,359 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,359 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,359 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,359 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,360 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,360 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,361 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,361 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,361 - WARNING - 页签 'Sheet1' 记录 #29 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,361 - WARNING - 页签 'Sheet1' 记录 #29 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,362 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,362 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,363 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,363 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,364 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,364 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,364 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,364 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,365 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,365 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,366 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,366 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,366 - WARNING - 页签 'Sheet1' 记录 #30 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,366 - WARNING - 页签 'Sheet1' 记录 #30 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,367 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,367 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,368 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,368 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,368 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,368 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,369 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,369 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,370 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,370 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,371 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,371 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,371 - WARNING - 页签 'Sheet1' 记录 #31 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,371 - WARNING - 页签 'Sheet1' 记录 #31 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,372 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,372 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,373 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,373 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,374 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,374 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,374 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,374 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,375 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,375 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,376 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,376 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,376 - WARNING - 页签 'Sheet1' 记录 #32 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,376 - WARNING - 页签 'Sheet1' 记录 #32 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,377 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,377 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,378 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,378 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,379 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,379 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,379 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,379 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,380 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,380 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,381 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,381 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,381 - WARNING - 页签 'Sheet1' 记录 #33 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,381 - WARNING - 页签 'Sheet1' 记录 #33 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,382 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,382 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,383 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,383 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,384 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,384 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,384 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,384 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,385 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,385 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,386 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,386 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,386 - WARNING - 页签 'Sheet1' 记录 #34 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,386 - WARNING - 页签 'Sheet1' 记录 #34 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,387 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,387 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,388 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,388 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,388 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,388 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,389 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,389 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,389 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,389 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,390 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,390 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,391 - WARNING - 页签 'Sheet1' 记录 #35 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,391 - WARNING - 页签 'Sheet1' 记录 #35 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,391 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,391 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,392 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,392 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,392 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,392 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,393 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,393 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,394 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,394 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,395 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,395 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,396 - WARNING - 页签 'Sheet1' 记录 #36 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,396 - WARNING - 页签 'Sheet1' 记录 #36 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,396 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,396 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,397 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,397 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,397 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,397 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,398 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,398 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,399 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,399 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,399 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,399 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,400 - WARNING - 页签 'Sheet1' 记录 #37 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,400 - WARNING - 页签 'Sheet1' 记录 #37 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,400 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,400 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,401 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,401 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,402 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,402 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,402 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,402 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,403 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,403 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,404 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,404 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,404 - WARNING - 页签 'Sheet1' 记录 #38 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,404 - WARNING - 页签 'Sheet1' 记录 #38 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,405 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,405 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,406 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,406 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,406 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,406 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,407 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,407 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,407 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,407 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,408 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,408 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,409 - WARNING - 页签 'Sheet1' 记录 #39 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,409 - WARNING - 页签 'Sheet1' 记录 #39 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,409 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,409 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,410 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,410 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,411 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,411 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,411 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,411 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,412 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,412 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,413 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,413 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,413 - WARNING - 页签 'Sheet1' 记录 #40 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,413 - WARNING - 页签 'Sheet1' 记录 #40 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,414 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,414 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,415 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,415 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,416 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,416 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,417 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,417 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,417 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,417 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,418 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,418 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,419 - WARNING - 页签 'Sheet1' 记录 #41 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,419 - WARNING - 页签 'Sheet1' 记录 #41 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,419 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,419 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,420 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,420 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,421 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,421 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,421 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,421 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,422 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,422 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:09:05,423 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,423 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,423 - WARNING - 页签 'Sheet1' 记录 #42 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,423 - WARNING - 页签 'Sheet1' 记录 #42 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,424 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,424 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,425 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,425 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,426 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,426 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,427 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,427 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,427 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,427 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:09:05,428 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,428 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,429 - WARNING - 页签 'Sheet1' 记录 #43 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,429 - WARNING - 页签 'Sheet1' 记录 #43 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,429 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,429 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,430 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,430 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,431 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,431 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,431 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,431 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,432 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,432 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:09:05,433 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,433 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,433 - WARNING - 页签 'Sheet1' 记录 #44 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,433 - WARNING - 页签 'Sheet1' 记录 #44 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,434 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,434 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,435 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,435 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:09:05,435 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,435 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,436 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,436 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:09:05,436 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,436 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:09:05,437 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,437 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:09:05,438 - WARNING - 页签 'Sheet1' 记录 #45 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,438 - WARNING - 页签 'Sheet1' 记录 #45 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:09:05,438 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,438 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:09:05,462 - INFO - 页签 Sheet1 处理完成
2025-05-06 16:09:05,462 - INFO - 页签 Sheet1 处理完成
2025-05-06 16:09:05,503 - INFO - 导出完成! 成功: 1, 跳过: 0, 失败: 0
2025-05-06 16:09:05,503 - INFO - 导出完成! 成功: 1, 跳过: 0, 失败: 0
2025-05-06 16:12:00,415 - INFO - 开始导出选中的 1 个文件...
2025-05-06 16:12:00,415 - INFO - 开始导出选中的 1 个文件...
2025-05-06 16:12:00,419 - INFO - 开始处理: test1.xlsx
2025-05-06 16:12:00,419 - INFO - 开始处理: test1.xlsx
2025-05-06 16:12:00,455 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,455 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,457 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,457 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,459 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,459 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,460 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,460 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,461 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,461 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,463 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,463 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,464 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,464 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,466 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,466 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,468 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,468 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,470 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,470 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,471 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,471 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,472 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,472 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,473 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,473 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,475 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,475 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,477 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,477 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,478 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,478 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,479 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,479 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,480 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,480 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,481 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,481 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,483 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,483 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,484 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,484 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,486 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,486 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,487 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,487 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,489 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,489 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,490 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,490 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,491 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,491 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,492 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,492 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,493 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,493 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,494 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,494 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,496 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,496 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,497 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,497 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,498 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,498 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,499 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,499 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,500 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,500 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,501 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,501 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,502 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,502 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,503 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,503 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,504 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,504 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,505 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,505 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,506 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,506 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,507 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,507 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,508 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,508 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,508 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,508 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,509 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,509 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,510 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,510 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,511 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,511 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,512 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,512 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,513 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,513 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,514 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,514 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,515 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,515 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,516 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,516 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,517 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,517 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,518 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,518 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,519 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,519 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,520 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,520 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,521 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,521 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,521 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,521 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,523 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,523 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,524 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,524 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,525 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,525 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,525 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,525 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,526 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,526 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,527 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,527 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,528 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,528 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,529 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,529 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,530 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,530 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,531 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,531 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,532 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,532 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,533 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,533 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,534 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,534 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,535 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,535 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,536 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,536 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,537 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,537 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,538 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,538 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,538 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,538 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,539 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,539 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,540 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,540 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,541 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,541 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,542 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,542 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,542 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,542 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,543 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,543 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,544 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,544 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,545 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,545 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,546 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,546 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,547 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,547 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,548 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,548 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,549 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,549 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,550 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,550 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,550 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,550 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,551 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,551 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,552 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,552 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,553 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,553 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,554 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,554 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,555 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,555 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,556 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,556 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,557 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,557 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,558 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,558 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,559 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,559 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,560 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,560 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,561 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,561 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,561 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,561 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,562 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,562 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,563 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,563 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,564 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,564 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,565 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,565 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,565 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,565 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,567 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,567 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,568 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,568 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,568 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,568 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,569 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,569 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,570 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,570 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,571 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,571 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,572 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,572 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,573 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,573 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,574 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,574 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,574 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,574 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,575 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,575 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,576 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,576 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,577 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,577 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,578 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,578 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,579 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,579 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,580 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,580 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,581 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,581 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,581 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,581 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,583 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,583 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,584 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,584 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,584 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,584 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,585 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,585 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,586 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,586 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,587 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,587 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,588 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,588 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,589 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,589 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,590 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,590 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,591 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,591 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,592 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,592 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,592 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,592 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,593 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,593 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,594 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,594 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,595 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,595 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,596 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,596 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,596 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,596 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,597 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,597 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,598 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,598 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,599 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,599 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,600 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,600 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,601 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,601 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,601 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,601 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,602 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,602 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,603 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,603 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,604 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,604 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,605 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,605 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,606 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,606 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,607 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,607 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,607 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,607 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,608 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,608 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,609 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,609 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,610 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,610 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,611 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,611 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,612 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,612 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,612 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,612 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,613 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,613 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,614 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,614 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,615 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,615 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,616 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,616 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,616 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,616 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,617 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,617 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,618 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,618 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,619 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,619 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,620 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,620 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,620 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,620 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,621 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,621 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,622 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,622 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,623 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,623 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,624 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,624 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,625 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,625 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,626 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,626 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,626 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,626 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,627 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,627 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,628 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,628 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,629 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,629 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,630 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,630 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,631 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,631 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,631 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,631 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,632 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,632 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,633 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,633 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,634 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,634 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,634 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,634 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,635 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,635 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,636 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,636 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,637 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,637 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,638 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,638 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,638 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,638 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,639 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,639 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,640 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,640 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,641 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,641 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,642 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,642 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,643 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,643 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,644 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,644 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,644 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,644 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,645 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,645 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,646 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,646 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,647 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,647 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,648 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,648 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,649 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,649 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,649 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,649 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,650 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,650 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,651 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,651 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,651 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,651 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,652 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,652 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,653 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,653 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,654 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,654 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,654 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,654 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,655 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,655 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,656 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,656 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,657 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,657 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,658 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,658 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,659 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,659 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,660 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,660 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,661 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,661 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,661 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,661 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,662 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,662 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,663 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,663 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,664 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,664 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,664 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,664 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,665 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,665 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,666 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,666 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,667 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,667 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,668 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,668 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,669 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,669 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,669 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,669 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,670 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,670 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,671 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,671 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,672 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,672 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,672 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,672 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,673 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,673 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,674 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,674 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,675 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,675 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,676 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,676 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,677 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,677 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,678 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,678 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,678 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,678 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,679 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,679 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,680 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,680 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,680 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,680 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,681 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,681 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,682 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,682 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,683 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,683 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,684 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,684 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,685 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,685 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,686 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,686 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,687 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,687 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,687 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,687 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,688 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,688 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,689 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,689 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,690 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,690 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,691 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,691 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:12:00,691 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,691 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,693 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,693 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,693 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,693 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,694 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,694 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,695 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,695 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,696 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,696 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,697 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,697 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:12:00,698 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,698 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,698 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,698 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,699 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,699 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,700 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,700 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,701 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,701 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,701 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,701 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,702 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,702 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:12:00,703 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,703 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,704 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,704 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,704 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,704 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,705 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,705 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:12:00,706 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,706 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,707 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,707 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:12:00,708 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,708 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:12:00,708 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,708 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:12:00,709 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,709 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [30001,30002,30003]
2025-05-06 16:12:00,710 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,710 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:12:00,740 - INFO - 页签 Sheet1 处理完成
2025-05-06 16:12:00,740 - INFO - 页签 Sheet1 处理完成
2025-05-06 16:12:00,770 - INFO - 导出完成! 成功: 1, 跳过: 0, 失败: 0
2025-05-06 16:12:00,770 - INFO - 导出完成! 成功: 1, 跳过: 0, 失败: 0
