#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SData反向推导页签组件
用于将SData文件反向推导为Excel表格
支持客户端/服务端结构体差异、自定义Excel文件名、Sheet页选择等功能
"""

import os
import sys
import json
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                           QLabel, QTextEdit, QListWidget, QListWidgetItem,
                           QMessageBox, QFileDialog, QSplitter, QFrame,
                           QProgressBar, QApplication, QTabWidget, QLineEdit,
                           QComboBox, QCheckBox, QGroupBox, QGridLayout,
                           QTableWidget, QTableWidgetItem, QHeaderView,
                           QAbstractItemView, QSpinBox, QInputDialog, QDialog)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QMimeData, QSettings
from PyQt5.QtGui import QDragEnterEvent, QDropEvent, QFont

from utils.sdata_parser import SDataParser
from core.excel_processor import ExcelProcessor


class BatchProcessDialog(QDialog):
    """批量处理配置对话框"""

    batch_process_started = pyqtSignal(dict)  # 批量处理配置

    def __init__(self, file_list, parent=None):
        super().__init__(parent)
        self.file_list = file_list
        self.setWindowTitle("批量处理配置")
        self.setMinimumSize(700, 600)
        self.setModal(True)  # 设置为模态对话框
        # 设置窗口图标
        if parent and hasattr(parent, 'parent') and hasattr(parent.parent, 'app_icon'):
            self.setWindowIcon(parent.parent.app_icon)
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)

        # 添加说明标签
        info_label = QLabel("批量处理SData文件，将其反向推导为Excel表格")
        info_label.setStyleSheet("font-weight: bold; margin-bottom: 10px; color: #2c3e50; font-size: 14px;")
        layout.addWidget(info_label)

        # 添加操作步骤提示
        steps_label = QLabel("操作步骤：1. 确认待处理文件列表 → 2. 选择结构体类型并输入定义 → 3. 配置输出选项 → 4. 开始批量处理")
        steps_label.setStyleSheet("color: #666; font-size: 12px; margin-bottom: 15px; padding: 8px; background-color: #f8f9fa; border-left: 3px solid #007bff; border-radius: 4px;")
        steps_label.setWordWrap(True)
        layout.addWidget(steps_label)

        # 文件列表
        files_group = QGroupBox(f"待处理文件 ({len(self.file_list)} 个)")
        files_layout = QVBoxLayout(files_group)

        self.files_table = QTableWidget()
        self.files_table.setColumnCount(3)
        self.files_table.setHorizontalHeaderLabels(["文件名", "路径", "状态"])
        self.files_table.horizontalHeader().setStretchLastSection(True)
        self.files_table.setSelectionBehavior(QAbstractItemView.SelectRows)

        # 填充文件列表
        self.files_table.setRowCount(len(self.file_list))
        for i, file_path in enumerate(self.file_list):
            self.files_table.setItem(i, 0, QTableWidgetItem(os.path.basename(file_path)))
            self.files_table.setItem(i, 1, QTableWidgetItem(file_path))
            self.files_table.setItem(i, 2, QTableWidgetItem("待处理"))

        files_layout.addWidget(self.files_table)
        layout.addWidget(files_group)

        # 批量配置
        config_group = QGroupBox("批量配置")
        config_layout = QGridLayout(config_group)

        # 结构体类型
        config_layout.addWidget(QLabel("结构体类型:"), 0, 0)
        self.struct_type_combo = QComboBox()
        self.struct_type_combo.addItems(["统一结构", "客户端/服务端分离"])
        self.struct_type_combo.currentTextChanged.connect(self.on_struct_type_changed)
        config_layout.addWidget(self.struct_type_combo, 0, 1)

        # 结构体定义
        self.struct_tab_widget = QTabWidget()

        # 统一结构页签
        self.unified_tab = QWidget()
        unified_layout = QVBoxLayout(self.unified_tab)
        unified_layout.addWidget(QLabel("统一结构体定义:"))
        self.unified_struct_edit = QTextEdit()
        self.unified_struct_edit.setPlaceholderText("在此输入结构体定义...")
        font = QFont("Consolas", 10)
        self.unified_struct_edit.setFont(font)
        unified_layout.addWidget(self.unified_struct_edit)
        self.struct_tab_widget.addTab(self.unified_tab, "统一结构")

        # 客户端结构页签
        self.client_tab = QWidget()
        client_layout = QVBoxLayout(self.client_tab)
        client_layout.addWidget(QLabel("客户端结构体定义:"))
        self.client_struct_edit = QTextEdit()
        self.client_struct_edit.setPlaceholderText("在此输入客户端结构体定义...")
        self.client_struct_edit.setFont(font)
        client_layout.addWidget(self.client_struct_edit)
        self.struct_tab_widget.addTab(self.client_tab, "客户端结构")

        # 服务端结构页签
        self.server_tab = QWidget()
        server_layout = QVBoxLayout(self.server_tab)
        server_layout.addWidget(QLabel("服务端结构体定义:"))
        self.server_struct_edit = QTextEdit()
        self.server_struct_edit.setPlaceholderText("在此输入服务端结构体定义...")
        self.server_struct_edit.setFont(font)
        server_layout.addWidget(self.server_struct_edit)
        self.struct_tab_widget.addTab(self.server_tab, "服务端结构")

        config_layout.addWidget(self.struct_tab_widget, 1, 0, 1, 2)

        # 输出配置
        config_layout.addWidget(QLabel("Excel文件名模式:"), 2, 0)
        self.excel_name_pattern_edit = QLineEdit()
        self.excel_name_pattern_edit.setPlaceholderText("留空使用SData文件名，支持 {filename} 变量")
        config_layout.addWidget(self.excel_name_pattern_edit, 2, 1)

        config_layout.addWidget(QLabel("Sheet页名模式:"), 3, 0)
        self.sheet_name_pattern_edit = QLineEdit()
        self.sheet_name_pattern_edit.setPlaceholderText("留空使用SData文件名，支持 {filename} 变量")
        config_layout.addWidget(self.sheet_name_pattern_edit, 3, 1)

        layout.addWidget(config_group)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 返回按钮
        self.back_btn = QPushButton("← 返回")
        self.back_btn.setToolTip("返回到文件列表，不保存当前配置")
        self.back_btn.clicked.connect(self.reject)  # 使用reject()关闭对话框
        try:
            from PyQt5.QtGui import QIcon
            self.back_btn.setIcon(QIcon.fromTheme("go-previous"))
        except:
            pass
        self.back_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)

        # 验证结构体按钮
        self.validate_btn = QPushButton("验证结构体")
        self.validate_btn.setToolTip("验证当前输入的结构体定义格式")
        self.validate_btn.clicked.connect(self.validate_current_struct)
        try:
            from PyQt5.QtGui import QIcon
            self.validate_btn.setIcon(QIcon.fromTheme("dialog-information"))
        except:
            pass

        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setToolTip("取消批量处理并关闭对话框")
        self.cancel_btn.clicked.connect(self.reject)  # 使用reject()关闭对话框
        try:
            from PyQt5.QtGui import QIcon
            self.cancel_btn.setIcon(QIcon.fromTheme("dialog-cancel"))
        except:
            pass

        # 开始处理按钮
        self.start_btn = QPushButton("开始批量处理 →")
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.start_btn.setToolTip("开始执行批量处理")
        try:
            from PyQt5.QtGui import QIcon
            self.start_btn.setIcon(QIcon.fromTheme("media-playback-start"))
        except:
            pass
        self.start_btn.clicked.connect(self.start_batch_process)

        button_layout.addWidget(self.back_btn)
        button_layout.addWidget(self.validate_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.start_btn)

        layout.addLayout(button_layout)

        # 初始化界面状态
        self.on_struct_type_changed("统一结构")

    def on_struct_type_changed(self, struct_type):
        """结构体类型改变"""
        if struct_type == "统一结构":
            self.struct_tab_widget.setTabEnabled(1, False)  # 禁用客户端页签
            self.struct_tab_widget.setTabEnabled(2, False)  # 禁用服务端页签
            self.struct_tab_widget.setCurrentIndex(0)       # 切换到统一结构页签
        else:
            self.struct_tab_widget.setTabEnabled(1, True)   # 启用客户端页签
            self.struct_tab_widget.setTabEnabled(2, True)   # 启用服务端页签
            self.struct_tab_widget.setCurrentIndex(1)       # 切换到客户端页签

    def start_batch_process(self):
        """开始批量处理"""
        struct_type = self.struct_type_combo.currentText()

        if struct_type == "统一结构":
            struct_content = self.unified_struct_edit.toPlainText().strip()
            if not struct_content:
                QMessageBox.warning(self, "警告", "请输入结构体定义！")
                return
            structs = {"unified": struct_content}
        else:
            client_struct = self.client_struct_edit.toPlainText().strip()
            server_struct = self.server_struct_edit.toPlainText().strip()
            if not client_struct and not server_struct:
                QMessageBox.warning(self, "警告", "请至少输入一个结构体定义！")
                return
            structs = {"client": client_struct, "server": server_struct}

        # 验证结构体定义
        config = {
            "struct_type": struct_type,
            "structs": structs
        }
        validation_result = self._validate_struct_definitions(config)
        if not validation_result["valid"]:
            QMessageBox.warning(self, "结构体验证失败", validation_result["message"])
            return

        batch_config = {
            "files": self.file_list,
            "struct_type": struct_type,
            "structs": structs,
            "excel_name_pattern": self.excel_name_pattern_edit.text().strip(),
            "sheet_name_pattern": self.sheet_name_pattern_edit.text().strip()
        }

        self.batch_process_started.emit(batch_config)
        self.accept()  # 使用accept()关闭对话框

    def _validate_struct_definitions(self, config):
        """验证结构体定义的格式（批量处理版本）"""
        struct_type = config["struct_type"]
        structs = config["structs"]

        if struct_type == "统一结构":
            struct_content = structs.get("unified", "").strip()
            if not struct_content:
                return {"valid": False, "message": "请输入统一结构体定义！"}

            # 验证结构体格式
            validation = self._validate_single_struct(struct_content, "统一结构")
            if not validation["valid"]:
                return validation

        else:  # 客户端/服务端分离
            client_struct = structs.get("client", "").strip()
            server_struct = structs.get("server", "").strip()

            if not client_struct and not server_struct:
                return {"valid": False, "message": "请至少输入一个结构体定义（客户端或服务端）！"}

            # 验证客户端结构体
            if client_struct:
                validation = self._validate_single_struct(client_struct, "客户端结构")
                if not validation["valid"]:
                    return validation

            # 验证服务端结构体
            if server_struct:
                validation = self._validate_single_struct(server_struct, "服务端结构")
                if not validation["valid"]:
                    return validation

        return {"valid": True, "message": "结构体定义验证通过"}

    def _validate_single_struct(self, struct_content, struct_name):
        """验证单个结构体定义（批量处理版本）"""
        import re

        # 检查是否包含字段定义（支持注释）
        field_pattern = r'\s*(\w+)\s+(\w+)(?:\[(\d+)\])?(?:\[(\d+)\])?\s*;\s*(?://\s*(.*))?'
        field_matches = re.findall(field_pattern, struct_content)

        if not field_matches:
            return {
                "valid": False,
                "message": f"{struct_name}中未找到有效的字段定义！\n\n请确保格式如下：\nDWORD dwId;\nBYTE byLevel;\nchar szName[32];\n\n支持的类型：DWORD, int, BYTE, char, float, double"
            }

        # 检查字段类型是否支持
        supported_types = ['DWORD', 'int', 'BYTE', 'char', 'float', 'double', 'short', 'long', 'unsigned']
        unsupported_fields = []

        for match in field_matches:
            field_type = match[0]
            field_name = match[1]

            # 检查类型是否支持
            if field_type not in supported_types:
                unsupported_fields.append(f"{field_type} {field_name}")

        if unsupported_fields:
            return {
                "valid": False,
                "message": f"{struct_name}中包含不支持的字段类型：\n\n" + "\n".join(unsupported_fields) +
                          f"\n\n支持的类型：{', '.join(supported_types)}"
            }

        return {"valid": True, "message": f"{struct_name}验证通过，找到 {len(field_matches)} 个字段"}

    def validate_current_struct(self):
        """验证当前输入的结构体定义（批量处理版本）"""
        struct_type = self.struct_type_combo.currentText()

        if struct_type == "统一结构":
            struct_content = self.unified_struct_edit.toPlainText().strip()
            structs = {"unified": struct_content}
        else:
            client_struct = self.client_struct_edit.toPlainText().strip()
            server_struct = self.server_struct_edit.toPlainText().strip()
            structs = {"client": client_struct, "server": server_struct}

        config = {
            "struct_type": struct_type,
            "structs": structs
        }

        validation_result = self._validate_struct_definitions(config)

        if validation_result["valid"]:
            QMessageBox.information(self, "验证成功", validation_result["message"])
        else:
            QMessageBox.warning(self, "验证失败", validation_result["message"])


class BatchProcessThread(QThread):
    """批量处理线程"""

    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_signal = pyqtSignal(bool, str)  # 成功标志, 消息

    def __init__(self, batch_config, output_path):
        super().__init__()
        self.batch_config = batch_config
        self.output_path = output_path

    def run(self):
        """执行批量处理"""
        try:
            files = self.batch_config["files"]
            total_files = len(files)
            success_count = 0
            failed_files = []

            for i, file_path in enumerate(files):
                self.status_updated.emit(f"正在处理 {os.path.basename(file_path)} ({i+1}/{total_files})")

                try:
                    # 为每个文件创建配置
                    filename = os.path.splitext(os.path.basename(file_path))[0]

                    excel_name = self.batch_config["excel_name_pattern"]
                    if excel_name:
                        excel_name = excel_name.replace("{filename}", filename)
                    else:
                        excel_name = filename

                    sheet_name = self.batch_config["sheet_name_pattern"]
                    if sheet_name:
                        sheet_name = sheet_name.replace("{filename}", filename)
                    else:
                        sheet_name = filename

                    config = {
                        "sdata_path": file_path,
                        "struct_type": self.batch_config["struct_type"],
                        "structs": self.batch_config["structs"],
                        "excel_name": excel_name,
                        "sheet_name": sheet_name,
                        "append_to_existing": False,
                        "existing_excel_path": ""
                    }

                    # 处理单个文件
                    thread = EnhancedReverseProcessThread(config, self.output_path)
                    thread.run()  # 直接调用run方法，同步执行

                    success_count += 1

                except Exception as e:
                    failed_files.append(f"{os.path.basename(file_path)}: {str(e)}")

                # 更新进度
                progress = int((i + 1) * 100 / total_files)
                self.progress_updated.emit(progress)

            # 生成结果消息
            if failed_files:
                message = f"批量处理完成：成功 {success_count} 个，失败 {len(failed_files)} 个\n失败文件：\n" + "\n".join(failed_files)
                self.finished_signal.emit(False, message)
            else:
                message = f"批量处理完成：成功处理 {success_count} 个文件"
                self.finished_signal.emit(True, message)

        except Exception as e:
            self.finished_signal.emit(False, f"批量处理出错: {str(e)}")


class EnhancedStructEditDialog(QDialog):
    """增强版结构体编辑对话框，支持客户端/服务端结构体差异"""

    struct_submitted = pyqtSignal(dict)  # 提交完整的配置信息

    def __init__(self, sdata_path, parent=None):
        super().__init__(parent)
        self.sdata_path = sdata_path
        self.setWindowTitle(f'SData反向推导配置 - {os.path.basename(sdata_path)}')
        self.setMinimumSize(800, 700)
        self.setModal(True)  # 设置为模态对话框
        # 设置窗口图标
        if parent and hasattr(parent, 'parent') and hasattr(parent.parent, 'app_icon'):
            self.setWindowIcon(parent.parent.app_icon)
        self.settings = QSettings()
        self.init_ui()
        self.load_settings()

    def init_ui(self):
        layout = QVBoxLayout(self)

        # 文件信息
        file_info_label = QLabel(f"SData文件: {os.path.basename(self.sdata_path)}")
        file_info_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-bottom: 10px;")
        layout.addWidget(file_info_label)

        # 输出配置组
        output_group = QGroupBox("输出配置")
        output_layout = QGridLayout(output_group)

        # Excel文件名配置
        output_layout.addWidget(QLabel("Excel文件名:"), 0, 0)
        self.excel_name_edit = QLineEdit()
        self.excel_name_edit.setPlaceholderText("留空则使用SData文件名")
        output_layout.addWidget(self.excel_name_edit, 0, 1)

        # Sheet页名配置
        output_layout.addWidget(QLabel("Sheet页名:"), 1, 0)
        self.sheet_name_edit = QLineEdit()
        self.sheet_name_edit.setPlaceholderText("留空则使用SData文件名")
        output_layout.addWidget(self.sheet_name_edit, 1, 1)

        # 是否追加到现有Excel
        self.append_to_existing_cb = QCheckBox("追加到现有Excel文件")
        output_layout.addWidget(self.append_to_existing_cb, 2, 0, 1, 2)

        # 现有Excel文件选择
        self.existing_excel_label = QLabel("现有Excel文件:")
        self.existing_excel_edit = QLineEdit()
        self.existing_excel_edit.setEnabled(False)
        self.browse_excel_btn = QPushButton("浏览...")
        self.browse_excel_btn.setEnabled(False)
        self.browse_excel_btn.clicked.connect(self.browse_existing_excel)

        output_layout.addWidget(self.existing_excel_label, 3, 0)
        excel_layout = QHBoxLayout()
        excel_layout.addWidget(self.existing_excel_edit)
        excel_layout.addWidget(self.browse_excel_btn)
        output_layout.addLayout(excel_layout, 3, 1)

        # 连接复选框事件
        self.append_to_existing_cb.toggled.connect(self.on_append_toggled)

        layout.addWidget(output_group)

        # 结构体配置组
        struct_group = QGroupBox("结构体定义")
        struct_layout = QVBoxLayout(struct_group)

        # 结构体类型选择
        struct_type_layout = QHBoxLayout()
        struct_type_layout.addWidget(QLabel("结构体类型:"))

        self.struct_type_combo = QComboBox()
        self.struct_type_combo.addItems(["统一结构", "客户端/服务端分离"])
        self.struct_type_combo.currentTextChanged.connect(self.on_struct_type_changed)
        struct_type_layout.addWidget(self.struct_type_combo)
        struct_type_layout.addStretch()

        struct_layout.addLayout(struct_type_layout)

        # 结构体编辑区域
        self.struct_tab_widget = QTabWidget()

        # 统一结构页签
        self.unified_tab = QWidget()
        unified_layout = QVBoxLayout(self.unified_tab)

        unified_layout.addWidget(QLabel("结构体定义:"))
        self.unified_struct_edit = QTextEdit()
        self.unified_struct_edit.setPlaceholderText("在此输入结构体定义...")
        font = QFont("Consolas", 10)
        self.unified_struct_edit.setFont(font)
        unified_layout.addWidget(self.unified_struct_edit)

        self.struct_tab_widget.addTab(self.unified_tab, "统一结构")

        # 客户端结构页签
        self.client_tab = QWidget()
        client_layout = QVBoxLayout(self.client_tab)

        client_layout.addWidget(QLabel("客户端结构体定义:"))
        self.client_struct_edit = QTextEdit()
        self.client_struct_edit.setPlaceholderText("在此输入客户端结构体定义...")
        self.client_struct_edit.setFont(font)
        client_layout.addWidget(self.client_struct_edit)

        self.struct_tab_widget.addTab(self.client_tab, "客户端结构")

        # 服务端结构页签
        self.server_tab = QWidget()
        server_layout = QVBoxLayout(self.server_tab)

        server_layout.addWidget(QLabel("服务端结构体定义:"))
        self.server_struct_edit = QTextEdit()
        self.server_struct_edit.setPlaceholderText("在此输入服务端结构体定义...")
        self.server_struct_edit.setFont(font)
        server_layout.addWidget(self.server_struct_edit)

        self.struct_tab_widget.addTab(self.server_tab, "服务端结构")

        struct_layout.addWidget(self.struct_tab_widget)

        # 示例说明
        example_label = QLabel("""
💡 结构体格式说明：
• 可以包含完整的 struct 定义，也可以只包含字段定义
• 支持的类型：DWORD, int, BYTE, char, float, double, short, long
• 支持数组：fieldName[size] 或 fieldName[size1][size2]
• 注释使用 // 开头
• ⚠️ 注意：字段名前缀通常表示类型（dw=DWORD, by=BYTE, sz=char数组）

示例格式：
struct ExchangeShopInfoConfig {
    DWORD dwExchangeID;     // 交换ID
    DWORD dwItemID;         // 物品ID
    BYTE byItemCount;       // 物品数量（注意：by前缀通常是BYTE）
    BYTE IconId;            // 图标ID
    DWORD dwBuyPrice;       // 购买价格
    DWORD dwLimitID;        // 限制ID
    BYTE byLimitType;       // 限制类型
    BYTE byLimitCount;      // 限制数量（注意：by前缀通常是BYTE）
    BYTE byKillLevel;       // 击杀等级
    DWORD dwItemsInGift[5]; // 礼品物品数组
};

或者简化格式（只包含字段）：
DWORD dwExchangeID;
DWORD dwItemID;
BYTE byItemCount;
BYTE IconId;
        """)
        example_label.setStyleSheet("color: #666; font-size: 11px; background-color: #f5f5f5; padding: 10px; border-radius: 5px; border-left: 3px solid #007bff;")
        struct_layout.addWidget(example_label)

        layout.addWidget(struct_group)

        # 按钮区域
        button_layout = QHBoxLayout()

        self.save_template_btn = QPushButton("保存为模板")
        self.save_template_btn.clicked.connect(self.save_template)
        try:
            from PyQt5.QtGui import QIcon
            self.save_template_btn.setIcon(QIcon.fromTheme("document-save"))
        except:
            pass

        self.load_template_btn = QPushButton("加载模板")
        self.load_template_btn.clicked.connect(self.load_template)
        try:
            from PyQt5.QtGui import QIcon
            self.load_template_btn.setIcon(QIcon.fromTheme("document-open"))
        except:
            pass

        # 验证结构体按钮
        self.validate_btn = QPushButton("验证结构体")
        self.validate_btn.clicked.connect(self.validate_current_struct)
        try:
            from PyQt5.QtGui import QIcon
            self.validate_btn.setIcon(QIcon.fromTheme("dialog-information"))
        except:
            pass

        self.submit_btn = QPushButton("开始转换")
        self.submit_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        try:
            from PyQt5.QtGui import QIcon
            self.submit_btn.setIcon(QIcon.fromTheme("media-playback-start"))
        except:
            pass
        self.submit_btn.clicked.connect(self.submit_config)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        try:
            from PyQt5.QtGui import QIcon
            self.cancel_btn.setIcon(QIcon.fromTheme("dialog-cancel"))
        except:
            pass
        self.cancel_btn.clicked.connect(self.reject)  # 使用reject()关闭对话框

        button_layout.addWidget(self.save_template_btn)
        button_layout.addWidget(self.load_template_btn)
        button_layout.addWidget(self.validate_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.submit_btn)

        layout.addLayout(button_layout)

        # 初始化界面状态
        self.on_struct_type_changed("统一结构")

    def on_append_toggled(self, checked):
        """追加到现有Excel选项切换"""
        self.existing_excel_edit.setEnabled(checked)
        self.browse_excel_btn.setEnabled(checked)
        self.existing_excel_label.setEnabled(checked)

    def browse_existing_excel(self):
        """浏览现有Excel文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择Excel文件", "", "Excel文件 (*.xlsx *.xls);;所有文件 (*)"
        )
        if file_path:
            self.existing_excel_edit.setText(file_path)

    def on_struct_type_changed(self, struct_type):
        """结构体类型改变"""
        if struct_type == "统一结构":
            self.struct_tab_widget.setTabEnabled(1, False)  # 禁用客户端页签
            self.struct_tab_widget.setTabEnabled(2, False)  # 禁用服务端页签
            self.struct_tab_widget.setCurrentIndex(0)       # 切换到统一结构页签
        else:
            self.struct_tab_widget.setTabEnabled(1, True)   # 启用客户端页签
            self.struct_tab_widget.setTabEnabled(2, True)   # 启用服务端页签
            self.struct_tab_widget.setCurrentIndex(1)       # 切换到客户端页签

    def save_template(self):
        """保存配置为模板"""
        config = self.get_current_config()
        if not config:
            return

        name, ok = QInputDialog.getText(self, "保存模板", "请输入模板名称:")
        if ok and name.strip():
            templates = self.load_templates()
            templates[name.strip()] = config
            self.save_templates(templates)
            QMessageBox.information(self, "成功", f"模板 '{name.strip()}' 已保存")

    def load_template(self):
        """加载模板"""
        templates = self.load_templates()
        if not templates:
            QMessageBox.information(self, "提示", "没有保存的模板")
            return

        names = list(templates.keys())
        name, ok = QInputDialog.getItem(self, "加载模板", "选择模板:", names, 0, False)
        if ok and name:
            config = templates[name]
            self.apply_config(config)
            QMessageBox.information(self, "成功", f"模板 '{name}' 已加载")

    def get_current_config(self):
        """获取当前配置"""
        struct_type = self.struct_type_combo.currentText()

        if struct_type == "统一结构":
            struct_content = self.unified_struct_edit.toPlainText().strip()
            if not struct_content:
                QMessageBox.warning(self, "警告", "请输入结构体定义！")
                return None
            structs = {"unified": struct_content}
        else:
            client_struct = self.client_struct_edit.toPlainText().strip()
            server_struct = self.server_struct_edit.toPlainText().strip()
            if not client_struct and not server_struct:
                QMessageBox.warning(self, "警告", "请至少输入一个结构体定义！")
                return None
            structs = {"client": client_struct, "server": server_struct}

        return {
            "struct_type": struct_type,
            "structs": structs,
            "excel_name": self.excel_name_edit.text().strip(),
            "sheet_name": self.sheet_name_edit.text().strip(),
            "append_to_existing": self.append_to_existing_cb.isChecked(),
            "existing_excel_path": self.existing_excel_edit.text().strip()
        }

    def apply_config(self, config):
        """应用配置"""
        self.struct_type_combo.setCurrentText(config.get("struct_type", "统一结构"))

        structs = config.get("structs", {})
        if "unified" in structs:
            self.unified_struct_edit.setPlainText(structs["unified"])
        if "client" in structs:
            self.client_struct_edit.setPlainText(structs["client"])
        if "server" in structs:
            self.server_struct_edit.setPlainText(structs["server"])

        self.excel_name_edit.setText(config.get("excel_name", ""))
        self.sheet_name_edit.setText(config.get("sheet_name", ""))
        self.append_to_existing_cb.setChecked(config.get("append_to_existing", False))
        self.existing_excel_edit.setText(config.get("existing_excel_path", ""))

    def load_templates(self):
        """加载模板"""
        templates_file = os.path.join(os.path.dirname(__file__), "..", "config", "reverse_templates.json")
        if os.path.exists(templates_file):
            try:
                with open(templates_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {}

    def save_templates(self, templates):
        """保存模板"""
        templates_file = os.path.join(os.path.dirname(__file__), "..", "config", "reverse_templates.json")
        os.makedirs(os.path.dirname(templates_file), exist_ok=True)
        try:
            with open(templates_file, 'w', encoding='utf-8') as f:
                json.dump(templates, f, ensure_ascii=False, indent=2)
        except Exception as e:
            QMessageBox.warning(self, "错误", f"保存模板失败: {str(e)}")

    def load_settings(self):
        """加载设置"""
        self.excel_name_edit.setText(self.settings.value("reverse/excel_name", ""))
        self.sheet_name_edit.setText(self.settings.value("reverse/sheet_name", ""))
        self.append_to_existing_cb.setChecked(self.settings.value("reverse/append_to_existing", False, type=bool))
        self.existing_excel_edit.setText(self.settings.value("reverse/existing_excel_path", ""))
        self.struct_type_combo.setCurrentText(self.settings.value("reverse/struct_type", "统一结构"))

    def save_settings(self):
        """保存设置"""
        self.settings.setValue("reverse/excel_name", self.excel_name_edit.text())
        self.settings.setValue("reverse/sheet_name", self.sheet_name_edit.text())
        self.settings.setValue("reverse/append_to_existing", self.append_to_existing_cb.isChecked())
        self.settings.setValue("reverse/existing_excel_path", self.existing_excel_edit.text())
        self.settings.setValue("reverse/struct_type", self.struct_type_combo.currentText())

    def submit_config(self):
        """提交配置"""
        config = self.get_current_config()
        if not config:
            return

        # 验证配置
        if config["append_to_existing"] and not config["existing_excel_path"]:
            QMessageBox.warning(self, "警告", "请选择要追加的Excel文件！")
            return

        # 验证结构体定义
        validation_result = self.validate_struct_definitions(config)
        if not validation_result["valid"]:
            QMessageBox.warning(self, "结构体验证失败", validation_result["message"])
            return

        # 保存设置
        self.save_settings()

        # 添加SData文件路径
        config["sdata_path"] = self.sdata_path

        # 发射信号
        self.struct_submitted.emit(config)
        self.accept()  # 使用accept()关闭对话框

    def validate_struct_definitions(self, config):
        """验证结构体定义的格式"""
        struct_type = config["struct_type"]
        structs = config["structs"]

        if struct_type == "统一结构":
            struct_content = structs.get("unified", "").strip()
            if not struct_content:
                return {"valid": False, "message": "请输入统一结构体定义！"}

            # 验证结构体格式
            validation = self._validate_single_struct(struct_content, "统一结构")
            if not validation["valid"]:
                return validation

        else:  # 客户端/服务端分离
            client_struct = structs.get("client", "").strip()
            server_struct = structs.get("server", "").strip()

            if not client_struct and not server_struct:
                return {"valid": False, "message": "请至少输入一个结构体定义（客户端或服务端）！"}

            # 验证客户端结构体
            if client_struct:
                validation = self._validate_single_struct(client_struct, "客户端结构")
                if not validation["valid"]:
                    return validation

            # 验证服务端结构体
            if server_struct:
                validation = self._validate_single_struct(server_struct, "服务端结构")
                if not validation["valid"]:
                    return validation

        return {"valid": True, "message": "结构体定义验证通过"}

    def _validate_single_struct(self, struct_content, struct_name):
        """验证单个结构体定义"""
        import re

        # 检查是否包含字段定义（支持注释）
        field_pattern = r'\s*(\w+)\s+(\w+)(?:\[(\d+)\])?(?:\[(\d+)\])?\s*;\s*(?://\s*(.*))?'
        field_matches = re.findall(field_pattern, struct_content)

        if not field_matches:
            return {
                "valid": False,
                "message": f"{struct_name}中未找到有效的字段定义！\n\n请确保格式如下：\nDWORD dwId;\nBYTE byLevel;\nchar szName[32];\n\n支持的类型：DWORD, int, BYTE, char, float, double"
            }

        # 检查字段类型是否支持
        supported_types = ['DWORD', 'int', 'BYTE', 'char', 'float', 'double', 'short', 'long', 'unsigned']
        unsupported_fields = []

        for match in field_matches:
            field_type = match[0]
            field_name = match[1]

            # 检查类型是否支持
            if field_type not in supported_types:
                unsupported_fields.append(f"{field_type} {field_name}")

        if unsupported_fields:
            return {
                "valid": False,
                "message": f"{struct_name}中包含不支持的字段类型：\n\n" + "\n".join(unsupported_fields) +
                          f"\n\n支持的类型：{', '.join(supported_types)}"
            }

        return {"valid": True, "message": f"{struct_name}验证通过，找到 {len(field_matches)} 个字段"}

    def validate_current_struct(self):
        """验证当前输入的结构体定义"""
        config = self.get_current_config()
        if not config:
            QMessageBox.warning(self, "验证失败", "无法获取当前配置")
            return

        validation_result = self.validate_struct_definitions(config)

        if validation_result["valid"]:
            QMessageBox.information(self, "验证成功", validation_result["message"])
        else:
            QMessageBox.warning(self, "验证失败", validation_result["message"])


class EnhancedReverseProcessThread(QThread):
    """增强版反向处理线程"""

    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_signal = pyqtSignal(bool, str)  # 成功标志, 消息

    def __init__(self, config, output_path):
        super().__init__()
        self.config = config
        self.output_path = output_path
        self.sdata_path = config["sdata_path"]
        
    def run(self):
        """执行增强版反向推导"""
        try:
            self.status_updated.emit("正在解析配置...")
            self.progress_updated.emit(10)

            struct_type = self.config["struct_type"]
            structs = self.config["structs"]

            # 确定要使用的结构体
            if struct_type == "统一结构":
                struct_content = structs.get("unified", "")
                if not struct_content:
                    self.finished_signal.emit(False, "未找到统一结构体定义")
                    return
                results = [("unified", struct_content)]
            else:
                results = []
                if structs.get("client"):
                    results.append(("client", structs["client"]))
                if structs.get("server"):
                    results.append(("server", structs["server"]))
                if not results:
                    self.finished_signal.emit(False, "未找到客户端或服务端结构体定义")
                    return

            self.status_updated.emit("正在解析SData文件...")
            self.progress_updated.emit(30)

            # 解析每种结构体
            all_data = {}
            parser = SDataParser()

            for struct_name, struct_content in results:
                self.status_updated.emit(f"正在解析{struct_name}结构体...")

                # 创建临时结构体文件
                temp_struct_path = os.path.join(os.path.dirname(self.sdata_path), f"temp_struct_{struct_name}.txt")
                with open(temp_struct_path, 'w', encoding='utf-8') as f:
                    f.write(struct_content)

                # 调试信息：打印结构体内容
                self.status_updated.emit(f"结构体内容长度: {len(struct_content)} 字符")
                print(f"调试: 结构体内容前200字符: {struct_content[:200]}")

                # 解析SData文件
                data = parser.parse_sdata_file_structured(self.sdata_path, temp_struct_path, auto_detect_count=True)

                # 清理临时文件
                if os.path.exists(temp_struct_path):
                    os.remove(temp_struct_path)

                if not data['success']:
                    error_msg = f"解析{struct_name}结构体失败: {data['message']}"
                    if "无法找到结构体定义文件" in data['message']:
                        error_msg += "\n\n建议：\n1. 检查结构体定义格式是否正确\n2. 确保结构体名称与SData文件匹配\n3. 验证字段类型定义是否完整"
                    elif "文件格式错误" in data['message']:
                        error_msg += "\n\n建议：\n1. 确认文件是有效的SData格式\n2. 检查文件是否损坏\n3. 验证文件大小是否正常"
                    self.finished_signal.emit(False, error_msg)
                    return

                all_data[struct_name] = data

            self.status_updated.emit("正在生成Excel文件...")
            self.progress_updated.emit(70)

            # 生成Excel文件
            excel_path = self.create_excel_file(all_data)

            self.progress_updated.emit(100)
            self.status_updated.emit("完成")

            # 验证生成的Excel文件
            validation_result = self.validate_excel_file(excel_path, all_data)
            if validation_result["success"]:
                self.finished_signal.emit(True, f"成功生成Excel文件: {excel_path}\n验证通过：{validation_result['message']}")
            else:
                self.finished_signal.emit(True, f"成功生成Excel文件: {excel_path}\n警告：{validation_result['message']}")

        except Exception as e:
            self.finished_signal.emit(False, f"处理过程中出错: {str(e)}")
            
    def create_excel_file(self, all_data):
        """创建Excel文件"""
        import openpyxl
        from openpyxl.styles import Font, PatternFill, Alignment

        # 确定Excel文件路径
        if self.config["append_to_existing"] and self.config["existing_excel_path"]:
            excel_path = self.config["existing_excel_path"]
            try:
                wb = openpyxl.load_workbook(excel_path)
            except:
                wb = openpyxl.Workbook()
        else:
            wb = openpyxl.Workbook()
            # 确定Excel文件名
            if self.config["excel_name"]:
                excel_name = self.config["excel_name"]
            else:
                excel_name = os.path.splitext(os.path.basename(self.sdata_path))[0]

            if not excel_name.endswith('.xlsx'):
                excel_name += '.xlsx'
            excel_path = os.path.join(self.output_path, excel_name)

        # 处理每种结构体的数据
        for struct_name, data in all_data.items():
            # 确定Sheet名称
            if self.config["sheet_name"]:
                sheet_name = self.config["sheet_name"]
                if len(all_data) > 1:
                    sheet_name += f"_{struct_name}"
            else:
                base_name = os.path.splitext(os.path.basename(self.sdata_path))[0]
                if len(all_data) > 1:
                    sheet_name = f"{base_name}_{struct_name}"
                else:
                    sheet_name = base_name

            # 创建或获取工作表
            if sheet_name in wb.sheetnames:
                ws = wb[sheet_name]
                ws.delete_rows(1, ws.max_row)  # 清空现有数据
            else:
                ws = wb.create_sheet(sheet_name)

            # 如果是第一个sheet且原来有默认sheet，删除默认sheet
            if len(wb.sheetnames) > 1 and 'Sheet' in wb.sheetnames:
                wb.remove(wb['Sheet'])

            self._populate_worksheet(ws, data)

        # 保存文件
        wb.save(excel_path)
        return excel_path

    def _get_cell(self, ws, row, col):
        """获取单元格，兼容不同版本的openpyxl"""
        try:
            # 尝试使用新版本的参数名
            return ws.cell(row=row, column=col)
        except TypeError:
            # 如果失败，使用旧版本的参数名
            return ws.cell(row=row, col=col)

    def _populate_worksheet(self, ws, data):
        """填充工作表数据"""
        from openpyxl.styles import Font, PatternFill, Alignment

        # 设置表头（字段说明）
        fields = data['fields']
        for col, field in enumerate(fields, 1):
            cell = self._get_cell(ws, 1, col)
            cell.value = field['comment'] or field['name']
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")

        # 设置类型行
        for col, field in enumerate(fields, 1):
            cell = self._get_cell(ws, 2, col)
            cell.value = field['type']
            cell.font = Font(italic=True)
            cell.fill = PatternFill(start_color="F0F0F0", end_color="F0F0F0", fill_type="solid")

        # 设置字段名行
        for col, field in enumerate(fields, 1):
            cell = self._get_cell(ws, 3, col)
            cell.value = field['name']
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="E0E0E0", end_color="E0E0E0", fill_type="solid")

        # 设置生成类型行（默认为all）
        for col, field in enumerate(fields, 1):
            cell = self._get_cell(ws, 4, col)
            cell.value = "all"
            cell.font = Font(color="666666")

        # 填充数据
        for row_idx, row_data in enumerate(data['rows'], 5):
            for col_idx, field in enumerate(fields, 1):
                field_name = field['name']
                value = row_data.get(field_name, "")

                cell = self._get_cell(ws, row_idx, col_idx)
                cell.value = value

        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

    def validate_excel_file(self, excel_path, all_data):
        """验证生成的Excel文件"""
        try:
            import openpyxl

            # 检查文件是否可以正常打开
            wb = openpyxl.load_workbook(excel_path)

            validation_issues = []

            for sheet_name in wb.sheetnames:
                ws = wb[sheet_name]

                # 检查基本格式
                if ws.max_row < 5:
                    validation_issues.append(f"Sheet '{sheet_name}' 行数不足（需要至少5行）")
                    continue

                # 检查表头结构
                field_comments = [cell.value for cell in ws[1]]
                field_types = [cell.value for cell in ws[2]]
                field_names = [cell.value for cell in ws[3]]
                field_targets = [cell.value for cell in ws[4]]

                # 验证字段完整性
                for i, (comment, type_str, name, target) in enumerate(zip(field_comments, field_types, field_names, field_targets)):
                    if not type_str or not name:
                        if name != "~":  # 忽略标记为~的列
                            validation_issues.append(f"Sheet '{sheet_name}' 第{i+1}列缺少类型或字段名")

                # 检查数据行
                data_rows = 0
                for row in ws.iter_rows(min_row=5, values_only=True):
                    if any(cell is not None for cell in row):
                        data_rows += 1

                if data_rows == 0:
                    validation_issues.append(f"Sheet '{sheet_name}' 没有数据行")

            wb.close()

            if validation_issues:
                return {
                    "success": False,
                    "message": f"发现 {len(validation_issues)} 个问题：\n" + "\n".join(validation_issues)
                }
            else:
                return {
                    "success": True,
                    "message": "Excel文件格式正确，可用于正向生成SData"
                }

        except Exception as e:
            return {
                "success": False,
                "message": f"验证过程出错: {str(e)}"
            }


class SDataReverseTab(QWidget):
    """SData反向推导页签"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(10)
        
        # 标题和说明
        title_label = QLabel("SData反向推导")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 5px;")
        layout.addWidget(title_label)
        
        desc_label = QLabel("将SData文件反向推导为Excel表格，支持客户端/服务端结构体差异、自定义Excel文件名等功能")
        desc_label.setStyleSheet("color: #666; margin-bottom: 10px;")
        layout.addWidget(desc_label)

        # 使用说明
        help_label = QLabel("""
使用说明：
1. 拖拽或添加SData文件到左侧列表
2. 双击文件打开配置对话框，输入结构体定义
3. 可选择统一结构或客户端/服务端分离模式
4. 自定义Excel文件名和Sheet页名
5. 支持追加到现有Excel文件
6. 使用批量处理功能可一次处理多个文件
        """)
        help_label.setStyleSheet("""
            color: #555;
            font-size: 11px;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            border-left: 3px solid #007bff;
            margin-bottom: 15px;
        """)
        layout.addWidget(help_label)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：文件列表区域
        left_frame = QFrame()
        left_frame.setFrameStyle(QFrame.StyledPanel)
        left_layout = QVBoxLayout(left_frame)
        
        # 文件列表标题
        file_list_label = QLabel("拖拽SData文件到此处：")
        file_list_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        left_layout.addWidget(file_list_label)

        # 添加操作提示
        self.operation_hint = QLabel("💡 添加文件后，可以双击文件进行单独配置，或使用下方按钮进行处理")
        self.operation_hint.setStyleSheet("""
            color: #28a745;
            font-size: 11px;
            margin-bottom: 5px;
            padding: 6px;
            background-color: #d4edda;
            border-left: 3px solid #28a745;
            border-radius: 3px;
        """)
        self.operation_hint.setWordWrap(True)
        self.operation_hint.setVisible(False)  # 初始隐藏
        left_layout.addWidget(self.operation_hint)
        
        # 文件列表
        self.file_list = QListWidget()
        self.file_list.setAcceptDrops(True)
        self.file_list.setDragDropMode(QListWidget.DropOnly)
        self.file_list.setStyleSheet("""
            QListWidget {
                border: 2px dashed #ccc;
                border-radius: 5px;
                background-color: #fafafa;
                min-height: 200px;
            }
            QListWidget::item {
                padding: 5px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:selected {
                background-color: #e3f2fd;
            }
        """)
        self.file_list.itemDoubleClicked.connect(self.edit_struct_for_file)
        left_layout.addWidget(self.file_list)
        
        # 按钮区域
        button_layout = QHBoxLayout()

        self.add_file_btn = QPushButton("添加文件")
        self.add_file_btn.clicked.connect(self.add_sdata_file)
        self.add_file_btn.setToolTip("选择SData文件添加到处理列表")

        self.clear_btn = QPushButton("清空列表")
        self.clear_btn.clicked.connect(self.clear_file_list)
        self.clear_btn.setToolTip("清空所有已添加的文件")

        # 单个文件处理按钮
        self.process_single_btn = QPushButton("处理选中文件")
        self.process_single_btn.clicked.connect(self.process_selected_file)
        self.process_single_btn.setToolTip("处理当前选中的单个文件")
        self.process_single_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)

        self.batch_process_btn = QPushButton("批量处理")
        self.batch_process_btn.clicked.connect(self.batch_process_files)
        self.batch_process_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        self.batch_process_btn.setToolTip("批量处理所有文件")

        button_layout.addWidget(self.add_file_btn)
        button_layout.addWidget(self.clear_btn)
        button_layout.addWidget(self.process_single_btn)
        button_layout.addWidget(self.batch_process_btn)
        button_layout.addStretch()
        
        left_layout.addLayout(button_layout)
        
        splitter.addWidget(left_frame)
        
        # 右侧：处理状态区域
        right_frame = QFrame()
        right_frame.setFrameStyle(QFrame.StyledPanel)
        right_layout = QVBoxLayout(right_frame)
        
        # 状态标题
        status_label = QLabel("处理状态：")
        status_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        right_layout.addWidget(status_label)
        
        # 状态显示
        self.status_text = QTextEdit()
        self.status_text.setReadOnly(True)
        self.status_text.setMaximumHeight(150)
        self.status_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 3px;
                background-color: #f9f9f9;
                font-family: Consolas, monospace;
                font-size: 11px;
            }
        """)
        right_layout.addWidget(self.status_text)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        right_layout.addWidget(self.progress_bar)
        
        # 输出路径设置
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出路径:"))
        
        self.output_path_edit = QLabel("未设置")
        self.output_path_edit.setStyleSheet("border: 1px solid #ddd; padding: 5px; background-color: white;")
        output_layout.addWidget(self.output_path_edit)
        
        self.browse_output_btn = QPushButton("浏览...")
        self.browse_output_btn.clicked.connect(self.browse_output_path)
        output_layout.addWidget(self.browse_output_btn)
        
        right_layout.addLayout(output_layout)
        
        right_layout.addStretch()
        
        splitter.addWidget(right_frame)
        
        # 设置分割器比例
        splitter.setSizes([300, 400])
        
        layout.addWidget(splitter)
        
        # 初始化输出路径
        self.output_path = os.path.expanduser("~/Desktop")
        self.output_path_edit.setText(self.output_path)
        
        self.add_status_message("准备就绪，请拖拽SData文件到左侧列表")
        
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
            
    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        files = [url.toLocalFile() for url in event.mimeData().urls()]
        sdata_files = [f for f in files if f.endswith('.SData')]
        
        if sdata_files:
            for file_path in sdata_files:
                self.add_sdata_file_to_list(file_path)
            self.add_status_message(f"添加了 {len(sdata_files)} 个SData文件")
        else:
            self.add_status_message("未找到有效的SData文件")
            
    def add_sdata_file(self):
        """通过文件对话框添加SData文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择SData文件", "", "SData文件 (*.SData);;所有文件 (*)"
        )
        
        for file_path in files:
            self.add_sdata_file_to_list(file_path)
            
        if files:
            self.add_status_message(f"添加了 {len(files)} 个SData文件")
            
    def add_sdata_file_to_list(self, file_path):
        """添加SData文件到列表"""
        if not os.path.exists(file_path):
            return

        # 检查是否已存在
        for i in range(self.file_list.count()):
            item = self.file_list.item(i)
            if item.data(Qt.UserRole) == file_path:
                return

        # 添加到列表
        item = QListWidgetItem(os.path.basename(file_path))
        item.setData(Qt.UserRole, file_path)
        item.setToolTip(file_path)
        self.file_list.addItem(item)

        # 显示操作提示
        self.update_operation_hint()
        
    def clear_file_list(self):
        """清空文件列表"""
        self.file_list.clear()
        self.add_status_message("已清空文件列表")
        # 隐藏操作提示
        self.update_operation_hint()

    def update_operation_hint(self):
        """更新操作提示的显示状态"""
        if hasattr(self, 'operation_hint'):
            has_files = self.file_list.count() > 0
            self.operation_hint.setVisible(has_files)

    def edit_struct_for_file(self, item):
        """为选中的文件编辑结构体"""
        file_path = item.data(Qt.UserRole)
        if not file_path or not os.path.exists(file_path):
            QMessageBox.warning(self, "错误", "文件不存在！")
            return

        # 打开增强版结构体编辑对话框
        dialog = EnhancedStructEditDialog(file_path, self)
        dialog.struct_submitted.connect(self.process_sdata_file_enhanced)
        dialog.exec_()  # 使用exec_()显示模态对话框
        
    def process_sdata_file_enhanced(self, config):
        """处理SData文件（增强版）"""
        # 如果不是追加到现有文件，检查输出路径
        if not config["append_to_existing"]:
            if not self.output_path or not os.path.exists(self.output_path):
                QMessageBox.warning(self, "错误", "请先设置有效的输出路径！")
                return

        sdata_path = config["sdata_path"]
        self.add_status_message(f"开始处理: {os.path.basename(sdata_path)}")

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 创建增强版处理线程
        self.process_thread = EnhancedReverseProcessThread(config, self.output_path)
        self.process_thread.progress_updated.connect(self.progress_bar.setValue)
        self.process_thread.status_updated.connect(self.add_status_message)
        self.process_thread.finished_signal.connect(self.on_process_finished)
        self.process_thread.start()

    def process_sdata_file(self, struct_content, sdata_path):
        """处理SData文件（兼容旧版本）"""
        if not self.output_path or not os.path.exists(self.output_path):
            QMessageBox.warning(self, "错误", "请先设置有效的输出路径！")
            return

        # 转换为新的配置格式
        config = {
            "struct_type": "统一结构",
            "structs": {"unified": struct_content},
            "excel_name": "",
            "sheet_name": "",
            "append_to_existing": False,
            "existing_excel_path": "",
            "sdata_path": sdata_path
        }

        self.process_sdata_file_enhanced(config)

    def process_selected_file(self):
        """处理选中的单个文件"""
        current_item = self.file_list.currentItem()
        if not current_item:
            QMessageBox.information(self, "提示", "请先选择要处理的SData文件")
            return

        file_path = current_item.data(Qt.UserRole)
        if not file_path or not os.path.exists(file_path):
            QMessageBox.warning(self, "错误", "选中的文件不存在")
            return

        # 打开单个文件的配置对话框
        self.edit_struct_for_file(current_item)

    def batch_process_files(self):
        """批量处理文件"""
        if self.file_list.count() == 0:
            QMessageBox.information(self, "提示", "请先添加要处理的SData文件")
            return

        # 打开批量配置对话框
        dialog = BatchProcessDialog(self.get_file_list(), self)
        dialog.batch_process_started.connect(self.start_batch_process)
        dialog.exec_()  # 使用exec_()显示模态对话框

    def get_file_list(self):
        """获取文件列表"""
        files = []
        for i in range(self.file_list.count()):
            item = self.file_list.item(i)
            file_path = item.data(Qt.UserRole)
            if file_path and os.path.exists(file_path):
                files.append(file_path)
        return files

    def start_batch_process(self, batch_config):
        """开始批量处理"""
        if not self.output_path or not os.path.exists(self.output_path):
            QMessageBox.warning(self, "错误", "请先设置有效的输出路径！")
            return

        self.add_status_message(f"开始批量处理 {len(batch_config['files'])} 个文件")

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 创建批量处理线程
        self.batch_thread = BatchProcessThread(batch_config, self.output_path)
        self.batch_thread.progress_updated.connect(self.progress_bar.setValue)
        self.batch_thread.status_updated.connect(self.add_status_message)
        self.batch_thread.finished_signal.connect(self.on_batch_process_finished)
        self.batch_thread.start()

    def on_batch_process_finished(self, success, message):
        """批量处理完成"""
        self.progress_bar.setVisible(False)
        self.add_status_message(message)

        if success:
            QMessageBox.information(self, "成功", message)
        else:
            QMessageBox.critical(self, "失败", message)
        
    def on_process_finished(self, success, message):
        """处理完成"""
        self.progress_bar.setVisible(False)
        self.add_status_message(message)
        
        if success:
            QMessageBox.information(self, "成功", message)
        else:
            QMessageBox.critical(self, "失败", message)
            
    def browse_output_path(self):
        """浏览输出路径"""
        path = QFileDialog.getExistingDirectory(self, "选择输出目录", self.output_path)
        if path:
            self.output_path = path
            self.output_path_edit.setText(path)
            self.add_status_message(f"输出路径设置为: {path}")
            
    def add_status_message(self, message):
        """添加状态消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.status_text.append(f"[{timestamp}] {message}")
        
        # 滚动到底部
        cursor = self.status_text.textCursor()
        cursor.movePosition(cursor.End)
        self.status_text.setTextCursor(cursor)
