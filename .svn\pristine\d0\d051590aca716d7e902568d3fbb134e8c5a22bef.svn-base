#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PIL import Image, ImageDraw, ImageFont
import os

def create_excel_app_icon(output_file, size=(64, 64)):
    """创建Excel风格的应用程序图标
    
    Args:
        output_file: 输出文件路径
        size: 图标大小，默认64x64
    """
    # 创建透明背景的图像
    img = Image.new('RGBA', size, (255, 255, 255, 0))
    draw = ImageDraw.Draw(img)
    
    width, height = size
    
    # Excel图标配色
    excel_green = (33, 115, 70)  # Excel绿色
    background_white = (255, 255, 255)  # 背景白色
    grid_color = (220, 220, 220)  # 表格线颜色
    
    # 计算边距和内部区域
    margin = max(1, int(width * 0.1))
    inner_width = width - 2 * margin
    inner_height = height - 2 * margin
    
    # 绘制文档底色 - Excel绿色
    draw.rectangle(
        [(margin, margin), (width - margin, height - margin)],
        fill=excel_green,
        outline=None
    )
    
    # 表格开始位置（留出顶部标题栏）
    header_height = max(2, int(inner_height * 0.2))
    table_top = margin + header_height
    
    # 绘制表格区域（白色）
    table_rect = [(margin, table_top), (width - margin, height - margin)]
    draw.rectangle(table_rect, fill=background_white)
    
    # 计算表格行数和列数
    row_count = 5
    col_count = 3
    
    # 绘制水平线
    row_height = (height - margin - table_top) / row_count
    for i in range(row_count + 1):
        y = table_top + i * row_height
        draw.line([(margin, y), (width - margin, y)], fill=grid_color, width=1)
    
    # 绘制垂直线
    col_width = inner_width / col_count
    for i in range(col_count + 1):
        x = margin + i * col_width
        draw.line([(x, table_top), (x, height - margin)], fill=grid_color, width=1)
    
    # 绘制一些单元格内容（假数据）
    if width >= 32:  # 只在足够大的图标上绘制内容
        cell_margin = max(1, int(width * 0.02))
        cell_width = col_width - cell_margin * 2
        cell_height = row_height - cell_margin * 2
        
        # A1, B1, C1 表头
        for i in range(col_count):
            cell_x = margin + i * col_width + cell_margin
            cell_y = table_top + cell_margin
            cell_color = (240, 240, 240)  # 表头灰色
            draw.rectangle(
                [(cell_x, cell_y), (cell_x + cell_width, cell_y + cell_height)],
                fill=cell_color
            )
    
    # 保存图像
    img.save(output_file, 'PNG')
    print(f"Created Excel-style app icon: {output_file}")

if __name__ == "__main__":
    # 创建图标目录
    icons_dir = os.path.join("resources", "icons")
    os.makedirs(icons_dir, exist_ok=True)
    
    # 创建不同尺寸的应用图标
    sizes = [16, 32, 48, 64, 128, 256]
    for size in sizes:
        icon_path = os.path.join(icons_dir, f"app-icon-{size}.png")
        create_excel_app_icon(icon_path, size=(size, size))
    
    print("Excel-style application icons created successfully!") 