BOOL CGameData::LoadFestivalActSignInServerCfg()
{
    std::string DataPath = "data/FestivalActSignInServer.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        ::g_pClientToLog->PrintFileDirct("CDataFile::LoadFestivalActSignInCfg fopen error");
        return FALSE;
    }
    
    m_mapFestivalActSignInCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stFestivalActSignInServerCfg cfg;
        fread(&cfg, sizeof(stFestivalActSignInServerCfg), 1, fp);

        m_mapFestivalActSignInCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}