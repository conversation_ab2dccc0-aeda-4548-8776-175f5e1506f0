BOOL CGameData::LoadDragonBoatGridClientCfg()
{
    std::string DataPath = "data/DragonBoatGridClient.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        GSLogError(":LoadDragonBoatGridCfg Failed!  [%s]", DataPath.c_str());
        return FALSE;
    }
    
    m_mapDragonBoatGridCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stDragonBoatGridClientCfg cfg;
        fread(&cfg, sizeof(stDragonBoatGridClientCfg), 1, fp);

        m_mapDragonBoatGridCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}