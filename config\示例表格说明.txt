# Excel示例表格格式说明

请按照以下格式创建Excel配置表:

## 表头格式:
- 第一行: 字段说明
- 第二行: 字段类型
- 第三行: 字段名
- 第四行: 生成类型(server/client/all)
- 第五行及以后: 具体数据

## 示例数据:
```
id    对应活动id/签到组id    任务的重置类型    任务描述             任务类型    统计数值    活动进度奖励    战令积分奖励
uint  uint                  uint            char[128]           int        int         int[2]         int[2]
dwId  dwActId               byResetType     szQuestDesc         byQuestType      dwReward1      dwreward3
all   all                   all             client              all         all         server         server
1003001 1003                1               在线2小时           1           2           [1003,5]       [1004,20]
1003002 1003                1               击杀1个敌对阵营     2           1           [1003,10]      [1004,60]
1003003 1003                1               参与1次战场任务     3           1           [1003,10]      [1004,60]
```

## 支持的数据类型:
- 基本类型: uint、byte、ushort、ufloat、ulong、ulonglong、int、double、bool、char、short、float
- 数组类型: int[n]、uint[n]、char[n]等
- 字符串类型: char[n]

## 生成规则:
- server: 只在服务端文件中生成
- client: 只在客户端文件中生成
- all: 在服务端和客户端文件中都生成

## 生成文件路径:
导表工具会按照以下目录结构生成文件:

```
配置文件夹/
  └── Excel文件名/
      └── Sheet名/
          ├── GameData{Sheet名}.txt      # C++结构体定义文件
          ├── Server/                    # 服务端文件夹
          │   ├── {Sheet名}Server.SData  # 服务端二进制数据文件
          │   └── {Sheet名}Server.txt    # 服务端数据可视化文件
          └── Client/                    # 客户端文件夹
              ├── {Sheet名}Client.SData  # 客户端二进制数据文件
              └── {Sheet名}Client.txt    # 客户端数据可视化文件
```

## 生成文件说明:

- `GameData{Sheet名}.txt` - C++结构体定义文件:
```cpp
struct st{Sheet名}Cfg
{
    DWORD    dwId;              // ID
    DWORD    dwActId;           // 活动ID
    // ... 其他字段
};
```

- `{Sheet名}Server.txt` / `{Sheet名}Client.txt` - 数据可视化文件:
```
3

dwId(uint) | dwActId(uint) | ... 其他字段
----------------------------------------------
1003001 | 1003 | ...
1003002 | 1003 | ...
1003003 | 1003 | ...
```

请将符合上述格式的Excel文件保存为.xlsx格式，放置在配置文件夹中，然后使用导表工具进行处理。 