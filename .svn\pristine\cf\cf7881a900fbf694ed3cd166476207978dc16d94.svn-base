#!/usr/bin/env python
# -*- coding: utf-8 -*-

import struct
import re
import ast

class TypeConverter:
    def __init__(self):
        # 类型映射表
        self.type_map = {
            'ulong': 'DWORD',
            'uint': 'DWORD',
            'byte': 'BYTE',
            'ushort': 'WORD',
            'ufloat': 'FLOAT',
            'ulonglong': 'ULONGLONG',
            'int': 'INT',
            'double': 'double',
            'bool': 'bool',
            'char': 'char',
            'short': 'short',
            'float': 'float',
            'time_t': 'DWORD'
        }
        
        # 类型对应的struct格式字符
        self.format_map = {
            'uint': '<I',      # 无符号整数 (4字节)
            'ulong': '<I',     # 无符号长整型 (4字节)
            'byte': '<B',      # 无符号字符 (1字节)
            'ushort': '<H',    # 无符号短整型 (2字节)
            'ufloat': '<f',    # 浮点数 (4字节)
            'ulonglong': '<Q', # 无符号长长整型 (8字节)
            'int': '<i',       # 有符号整数 (4字节)
            'double': '<d',    # 双精度浮点数 (8字节)
            'bool': '<?',      # 布尔值 (1字节)
            'char': '<c',      # 字符 (1字节)
            'short': '<h',     # 有符号短整型 (2字节)
            'float': '<f',     # 浮点数 (4字节)
            'time_t': '<I'     # 时间戳 (4字节，用无符号整数表示)
        }
    
    def get_cpp_type(self, type_str):
        """获取C++类型名
        
        Args:
            type_str: 类型字符串
            
        Returns:
            str: C++类型名
        """
        # 检查是否是二维数组类型 (如 int[3][4])
        two_dim_array_match = re.match(r'(\w+)\[(\d+)\]\[(\d+)\]', type_str)
        if two_dim_array_match:
            base_type = two_dim_array_match.group(1)
            size1 = two_dim_array_match.group(2)
            size2 = two_dim_array_match.group(3)
            cpp_base_type = self.type_map.get(base_type, base_type)
            return f"{cpp_base_type}[{size1}][{size2}]"
        
        # 检查是否是一维数组类型
        array_match = re.match(r'(\w+)\[(\d+)\]', type_str)
        if array_match:
            base_type = array_match.group(1)
            size = array_match.group(2)
            cpp_base_type = self.type_map.get(base_type, base_type)
            return f"{cpp_base_type}[{size}]"
        else:
            # 返回映射的C++类型，如果没有对应关系则返回原类型
            return self.type_map.get(type_str, type_str)
    
    def convert_to_binary(self, value, type_str):
        """将值转换为二进制数据
        
        Args:
            value: 要转换的值
            type_str: 类型字符串
            
        Returns:
            bytes: 二进制数据
        """
        # 检查是否是二维数组类型
        two_dim_array_match = re.match(r'(\w+)\[(\d+)\]\[(\d+)\]', type_str)
        if two_dim_array_match:
            base_type = two_dim_array_match.group(1)
            rows = int(two_dim_array_match.group(2))
            cols = int(two_dim_array_match.group(3))
            total_size = rows * cols
            
            # 如果是字符串类型(char)二维数组，使用不同的处理逻辑
            if base_type == 'char':
                # 将字符串数组转换为二维字节数组
                string_array = []
                if value is None:
                    # 创建空字符串数组
                    string_array = [""] * rows
                elif isinstance(value, list):
                    # 处理列表类型输入 - 直接使用列表中的字符串，确保没有引号
                    string_array = []
                    for item in value:
                        if isinstance(item, str):
                            # 检查并去除可能残留的引号
                            cleaned_str = item
                            if cleaned_str.startswith('"') and cleaned_str.endswith('"') and len(cleaned_str) >= 2:
                                cleaned_str = cleaned_str[1:-1]
                            elif cleaned_str.startswith("'") and cleaned_str.endswith("'") and len(cleaned_str) >= 2:
                                cleaned_str = cleaned_str[1:-1]
                            string_array.append(cleaned_str)
                        elif isinstance(item, list):
                            # 对于列表，取第一个元素(二进制格式限制)
                            if len(item) > 0 and item[0] is not None:
                                val_str = str(item[0])
                                if val_str.startswith('"') and val_str.endswith('"') and len(val_str) >= 2:
                                    val_str = val_str[1:-1]
                                elif val_str.startswith("'") and val_str.endswith("'") and len(val_str) >= 2:
                                    val_str = val_str[1:-1]
                                string_array.append(val_str)
                            else:
                                string_array.append("")
                        elif item is not None:
                            val_str = str(item)
                            if val_str.startswith('"') and val_str.endswith('"') and len(val_str) >= 2:
                                val_str = val_str[1:-1]
                            elif val_str.startswith("'") and val_str.endswith("'") and len(val_str) >= 2:
                                val_str = val_str[1:-1]
                            string_array.append(val_str)
                        else:
                            string_array.append("")
                elif isinstance(value, str) and value.startswith('[') and value.endswith(']'):
                    try:
                        # 解析字符串为Python列表
                        parsed_data = ast.literal_eval(value)
                        if isinstance(parsed_data, list):
                            # 提取所有字符串，确保没有引号
                            string_array = []
                            for item in parsed_data:
                                if isinstance(item, list):
                                    # 嵌套列表，直接使用全部内容
                                    item_str = []
                                    for sub_item in item:
                                        if isinstance(sub_item, str):
                                            # 去除可能存在的引号
                                            cleaned_str = sub_item
                                            if cleaned_str.startswith('"') and cleaned_str.endswith('"') and len(cleaned_str) >= 2:
                                                cleaned_str = cleaned_str[1:-1]
                                            elif cleaned_str.startswith("'") and cleaned_str.endswith("'") and len(cleaned_str) >= 2:
                                                cleaned_str = cleaned_str[1:-1]
                                            item_str.append(cleaned_str)
                                        else:
                                            # 非字符串元素转为字符串
                                            val_str = str(sub_item) if sub_item is not None else ""
                                            if val_str.startswith('"') and val_str.endswith('"') and len(val_str) >= 2:
                                                val_str = val_str[1:-1]
                                            elif val_str.startswith("'") and val_str.endswith("'") and len(val_str) >= 2:
                                                val_str = val_str[1:-1]
                                            item_str.append(val_str)
                                    string_array.append(item_str[0] if item_str else "")  # 只取第一个元素，因为二进制格式限制
                                elif isinstance(item, str):
                                    # 去除可能存在的引号
                                    cleaned_str = item
                                    if cleaned_str.startswith('"') and cleaned_str.endswith('"') and len(cleaned_str) >= 2:
                                        cleaned_str = cleaned_str[1:-1]
                                    elif cleaned_str.startswith("'") and cleaned_str.endswith("'") and len(cleaned_str) >= 2:
                                        cleaned_str = cleaned_str[1:-1]
                                    string_array.append(cleaned_str)
                                elif item is not None:
                                    # 其他类型转为字符串
                                    val_str = str(item)
                                    if val_str.startswith('"') and val_str.endswith('"') and len(val_str) >= 2:
                                        val_str = val_str[1:-1]
                                    elif val_str.startswith("'") and val_str.endswith("'") and len(val_str) >= 2:
                                        val_str = val_str[1:-1]
                                    string_array.append(val_str)
                                else:
                                    # None处理为空字符串
                                    string_array.append("")
                        else:
                            val_str = str(parsed_data) if parsed_data is not None else ""
                            if val_str.startswith('"') and val_str.endswith('"') and len(val_str) >= 2:
                                val_str = val_str[1:-1]
                            elif val_str.startswith("'") and val_str.endswith("'") and len(val_str) >= 2:
                                val_str = val_str[1:-1]
                            string_array = [val_str]
                    except (SyntaxError, ValueError):
                        # 解析失败，使用默认值
                        string_array = [""] * rows
                else:
                    # 单个值转为数组
                    val_str = str(value) if value is not None else ""
                    if val_str.startswith('"') and val_str.endswith('"') and len(val_str) >= 2:
                        val_str = val_str[1:-1]
                    elif val_str.startswith("'") and val_str.endswith("'") and len(val_str) >= 2:
                        val_str = val_str[1:-1]
                    string_array = [val_str]
                
                # 确保数组长度符合定义
                if len(string_array) > rows:
                    string_array = string_array[:rows]
                elif len(string_array) < rows:
                    string_array.extend([""] * (rows - len(string_array)))
                
                # 每个字符串转换为固定长度的字节数组（包括结尾的空字符）
                result = bytearray()
                for i in range(rows):
                    # 获取当前行的字符串，如果超出数组范围则使用空字符串
                    if i < len(string_array):
                        str_value = str(string_array[i])
                    else:
                        str_value = ""
                    
                    # 转换为UTF-8字节数组
                    try:
                        # 使用GB2312编码处理字符串而不是UTF-8
                        byte_str = str_value.encode('gb2312')
                    except:
                        # 编码失败，使用空字符串
                        byte_str = b''
                    
                    # 确保长度不超过最大限制(列数)
                    max_bytes = cols - 1  # 预留一个字节作为结尾的\0
                    if len(byte_str) > max_bytes:
                        # 截断过长的字符串时要小心，确保不破坏GB2312编码
                        # 由于GB2312是双字节编码，需要确保保留完整的双字节字符
                        if max_bytes % 2 == 1 and len(byte_str) > 1:
                            # 如果最大长度是奇数，且字符串长度超过1个字节，确保截断后是偶数长度
                            truncated = byte_str[:max_bytes - 1]
                        else:
                            truncated = byte_str[:max_bytes]
                        byte_str = truncated
                    
                    # 补充空字节直到达到固定长度
                    byte_str = byte_str + b'\x00' * (cols - len(byte_str))
                    
                    # 将字节数组添加到结果中
                    result.extend(byte_str)
                
                return bytes(result)
            else:
                # 非字符串类型的二维数组处理
                # 将字符串表示的二维数组解析为Python列表
                if value is None:
                    # 根据基本类型创建合适的默认值二维数组
                    if base_type in ('float', 'ufloat', 'double'):
                        array_data = [[0.0] * cols for _ in range(rows)]
                    else:
                        array_data = [[0] * cols for _ in range(rows)]
                elif isinstance(value, str) and value.startswith('[') and value.endswith(']'):
                    try:
                        # 尝试解析字符串为Python列表
                        parsed_data = ast.literal_eval(value)
                        # 确保是列表类型
                        if not isinstance(parsed_data, list):
                            parsed_data = [[parsed_data] * cols]
                        
                        # 处理不完整的二维数组，确保是正确的二维结构
                        array_data = []
                        if all(isinstance(item, list) for item in parsed_data):
                            # 已经是二维数组结构
                            for i in range(rows):
                                if i < len(parsed_data):
                                    row_data = []
                                    for j in range(cols):
                                        if j < len(parsed_data[i]):
                                            row_data.append(parsed_data[i][j])
                                        else:
                                            # 填充默认值
                                            if base_type in ('float', 'ufloat', 'double'):
                                                row_data.append(0.0)
                                            else:
                                                row_data.append(0)
                                    array_data.append(row_data)
                                else:
                                    # 创建默认行
                                    if base_type in ('float', 'ufloat', 'double'):
                                        array_data.append([0.0] * cols)
                                    else:
                                        array_data.append([0] * cols)
                        else:
                            # 是一维数组，需要转换为二维
                            for i in range(rows):
                                row_data = []
                                for j in range(cols):
                                    idx = i * cols + j
                                    if idx < len(parsed_data):
                                        row_data.append(parsed_data[idx])
                                    else:
                                        # 填充默认值
                                        if base_type in ('float', 'ufloat', 'double'):
                                            row_data.append(0.0)
                                        else:
                                            row_data.append(0)
                                array_data.append(row_data)
                    except (SyntaxError, ValueError) as e:
                        # 解析失败，创建默认值二维数组
                        if base_type in ('float', 'ufloat', 'double'):
                            array_data = [[0.0] * cols for _ in range(rows)]
                        else:
                            array_data = [[0] * cols for _ in range(rows)]
                elif isinstance(value, list):
                    # 处理一维数组和混合格式的数组
                    array_data = []
                    
                    # 检查是否为一维数组
                    is_one_dim = True
                    for item in value:
                        if isinstance(item, list):
                            is_one_dim = False
                            break
                    
                    if is_one_dim and len(value) <= rows * cols:
                        # 将一维数组转换为二维数组
                        for i in range(rows):
                            row_data = []
                            for j in range(cols):
                                idx = i * cols + j
                                if idx < len(value):
                                    row_data.append(value[idx])
                                else:
                                    # 填充默认值
                                    if base_type in ('float', 'ufloat', 'double'):
                                        row_data.append(0.0)
                                    else:
                                        row_data.append(0)
                            array_data.append(row_data)
                    else:
                        # 处理混合格式或已是二维数组
                        for i in range(rows):
                            row_data = []
                            if i < len(value):
                                if isinstance(value[i], list):
                                    # 已经是二维数组的行
                                    for j in range(cols):
                                        if j < len(value[i]):
                                            row_data.append(value[i][j])
                                        else:
                                            # 填充默认值
                                            if base_type in ('float', 'ufloat', 'double'):
                                                row_data.append(0.0)
                                            else:
                                                row_data.append(0)
                                else:
                                    # 单值作为一行的第一个元素
                                    if base_type in ('float', 'ufloat', 'double'):
                                        row_data = [0.0] * cols
                                        row_data[0] = float(value[i])
                                    else:
                                        row_data = [0] * cols
                                        try:
                                            row_data[0] = int(value[i])
                                        except (ValueError, TypeError):
                                            row_data[0] = 0
                            else:
                                # 创建默认行
                                if base_type in ('float', 'ufloat', 'double'):
                                    row_data = [0.0] * cols
                                else:
                                    row_data = [0] * cols
                            array_data.append(row_data)
                else:
                    # 单个值转为二维数组，放在第一行第一列
                    if base_type in ('float', 'ufloat', 'double'):
                        array_data = [[0.0] * cols for _ in range(rows)]
                        try:
                            array_data[0][0] = float(value)
                        except (ValueError, TypeError):
                            pass
                    else:
                        array_data = [[0] * cols for _ in range(rows)]
                        try:
                            array_data[0][0] = int(value)
                        except (ValueError, TypeError):
                            pass
                
                # 转换每个元素为二进制
                result = bytearray()
                for i in range(rows):
                    for j in range(cols):
                        result.extend(self._convert_simple_type(array_data[i][j], base_type))
                
                return bytes(result)
        
        # 检查是否是一维数组类型
        array_match = re.match(r'(\w+)\[(\d+)\]', type_str)
        if array_match:
            base_type = array_match.group(1)
            size = int(array_match.group(2))
            
            # 特殊处理 char[] 类型，作为字符串处理
            if base_type == 'char':
                # 处理字符串类型 char[N]
                if value is None or value == "":
                    return b'\x00' * size
                
                # 转换为字符串
                value_str = str(value)
                
                # 使用GB2312编码处理中文字符串，使C++端能正确解析
                try:
                    # 使用GB2312编码而不是UTF-8，与C++端项目设置一致
                    gb_bytes = value_str.encode('gb2312')
                    
                    # 预留终止符的位置
                    max_len = size - 1
                    
                    # 如果GB2312字节数超过限制，需要截断
                    if len(gb_bytes) > max_len:
                        # 逐字符编码并检查长度，确保不会在多字节字符中间截断
                        result_bytes = b''
                        for char in value_str:
                            char_bytes = char.encode('gb2312')
                            # 检查添加这个字符后是否会超过限制
                            if len(result_bytes) + len(char_bytes) <= max_len:
                                result_bytes += char_bytes
                            else:
                                break
                        
                        gb_bytes = result_bytes
                    
                    # 添加终止符并填充到指定长度
                    result = gb_bytes + b'\x00' * (size - len(gb_bytes))
                    return result
                except Exception as e:
                    # 编码失败，返回空字符串
                    return b'\x00' * size
            
            # 其他类型的一维数组处理
            # 将字符串表示的数组解析为Python列表
            if value is None:
                # 根据基本类型创建合适的默认值数组
                if base_type in ('float', 'ufloat', 'double'):
                    array_data = [0.0] * size
                else:
                    array_data = [0] * size
            elif isinstance(value, str) and value.startswith('[') and value.endswith(']'):
                try:
                    # 尝试解析字符串为Python列表
                    array_data = ast.literal_eval(value)
                    # 确保是列表类型
                    if not isinstance(array_data, list):
                        array_data = [array_data]
                except (SyntaxError, ValueError) as e:
                    # 解析失败，根据基本类型创建合适的默认值数组
                    if base_type in ('float', 'ufloat', 'double'):
                        array_data = [0.0] * size
                    else:
                        array_data = [0] * size
            elif isinstance(value, list):
                array_data = value
            else:
                # 不是有效的数组表示，根据基本类型创建合适的默认值数组
                if base_type in ('float', 'ufloat', 'double'):
                    array_data = [0.0] * size
                else:
                    array_data = [0] * size
            
            # 确保数组长度符合定义
            if len(array_data) > size:
                array_data = array_data[:size]  # 截断过长的数组
            elif len(array_data) < size:
                # 根据基本类型填充合适的默认值
                if base_type in ('float', 'ufloat', 'double'):
                    array_data.extend([0.0] * (size - len(array_data)))
                else:
                    array_data.extend([0] * (size - len(array_data)))
            
            # 转换每个元素为二进制 - 不写入长度信息，适配C++结构体读取
            result = bytearray()
            for item in array_data:
                result.extend(self._convert_simple_type(item, base_type))
            
            return bytes(result)
        else:
            # 简单类型
            return self._convert_simple_type(value, type_str)
    
    def _convert_simple_type(self, value, type_str):
        """转换简单类型到二进制
        
        Args:
            value: 要转换的值
            type_str: 类型字符串
            
        Returns:
            bytes: 二进制数据
        """
        # 获取格式字符
        format_char = self.format_map.get(type_str, '<i')  # 默认为整型
        
        # 处理None值
        if value is None:
            value = 0
        
        # 根据类型进行转换
        if type_str == 'bool':
            # 布尔值转换
            return struct.pack(format_char, bool(value))
        elif type_str in ('float', 'ufloat', 'double'):
            # 浮点数转换
            try:
                return struct.pack(format_char, float(value))
            except (ValueError, TypeError):
                return struct.pack(format_char, 0.0)
        elif type_str == 'char':
            # 单个字符转换
            try:
                if value and isinstance(value, str) and len(str(value)) > 0:
                    # 取第一个字符并转为字节，使用GB2312编码
                    byte_val = str(value)[0].encode('gb2312')[0:1]
                    if len(byte_val) == 0:
                        byte_val = b'\x00'
                    return struct.pack('<c', byte_val)
                else:
                    return struct.pack('<c', b'\x00')
            except Exception:
                # 任何转换错误，返回空字符
                return struct.pack('<c', b'\x00')
        elif type_str == 'time_t':
            # 时间戳转换
            try:
                # 支持多种时间格式输入
                if isinstance(value, str):
                    # 尝试解析日期时间字符串，支持多种格式
                    from datetime import datetime
                    import time
                    
                    # 移除可能存在的引号
                    value_str = value.strip()
                    if (value_str.startswith('"') and value_str.endswith('"')) or \
                       (value_str.startswith("'") and value_str.endswith("'")):
                        value_str = value_str[1:-1]
                    
                    # 尝试几种常见的日期时间格式
                    for fmt in [
                        '%Y-%m-%d %H:%M:%S',     # 2023-01-01 12:34:56
                        '%Y/%m/%d %H:%M:%S',     # 2023/01/01 12:34:56
                        '%Y-%m-%d',              # 2023-01-01
                        '%Y/%m/%d',              # 2023/01/01
                        '%d-%m-%Y %H:%M:%S',     # 01-01-2023 12:34:56
                        '%d/%m/%Y %H:%M:%S',     # 01/01/2023 12:34:56
                        '%Y%m%d%H%M%S',          # 20230101123456
                        '%Y%m%d'                 # 20230101
                    ]:
                        try:
                            dt = datetime.strptime(value_str, fmt)
                            timestamp = int(time.mktime(dt.timetuple()))
                            return struct.pack(format_char, timestamp)
                        except ValueError:
                            continue
                    
                    # 如果所有格式都失败，尝试将其作为整数时间戳处理
                    try:
                        return struct.pack(format_char, int(float(value_str)))
                    except (ValueError, TypeError):
                        # 如果还是失败，返回0
                        return struct.pack(format_char, 0)
                elif isinstance(value, (int, float)):
                    # 直接作为时间戳处理
                    return struct.pack(format_char, int(value))
                else:
                    # 其他类型转换为0
                    return struct.pack(format_char, 0)
            except Exception:
                # 所有转换失败，返回0
                return struct.pack(format_char, 0)
        else:
            # 整数类型转换
            try:
                return struct.pack(format_char, int(value))
            except (ValueError, TypeError):
                return struct.pack(format_char, 0) 