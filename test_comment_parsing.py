#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试注释解析功能
"""

import sys
import os
import tempfile

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.sdata_parser import SDataParser

def test_comment_parsing():
    """测试注释解析功能"""
    
    # 创建测试结构体内容
    struct_content = """
struct stDragonBoatEventClientCfg
{
    BYTE     byEventType;               // 事件类型
    char     szEventDesc[128];          // 前端描述播报文本
    INT      iStep;                     // 步数参数
    INT      dwReward[4][2];            // 奖励道具内容
    INT      dwCurrency[2];             // 奖励货币内容
    char     szIcon[64];                // 棋盘资源图标
    DWORD    dwEventId;                 // 事件id
};
"""
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(struct_content)
        temp_file = f.name
    
    try:
        # 解析结构体
        parser = SDataParser()
        fields = parser._parse_struct_file(temp_file)
        
        print("解析结果：")
        print(f"找到 {len(fields)} 个字段")
        print()
        
        for field in fields:
            name = field['name']
            type_str = field['type']
            comment = field['comment']
            
            print(f"字段名: {name}")
            print(f"类型: {type_str}")
            print(f"注释: {comment}")
            print(f"表头显示: {comment or name}")
            print("-" * 40)
        
        # 验证特定字段
        expected_fields = [
            ('byEventType', 'byte', '事件类型'),
            ('szEventDesc', 'char[128]', '前端描述播报文本'),
            ('iStep', 'int', '步数参数'),
            ('dwReward', 'int[4][2]', '奖励道具内容'),
            ('dwCurrency', 'int[2]', '奖励货币内容'),
            ('szIcon', 'char[64]', '棋盘资源图标'),
            ('dwEventId', 'uint', '事件id')
        ]
        
        print("\n验证结果：")
        success = True
        for i, (expected_name, expected_type, expected_comment) in enumerate(expected_fields):
            if i < len(fields):
                field = fields[i]
                if field['name'] != expected_name:
                    print(f"❌ 字段名不匹配: 期望 {expected_name}, 实际 {field['name']}")
                    success = False
                elif field['type'] != expected_type:
                    print(f"❌ 类型不匹配: 期望 {expected_type}, 实际 {field['type']}")
                    success = False
                elif field['comment'] != expected_comment:
                    print(f"❌ 注释不匹配: 期望 '{expected_comment}', 实际 '{field['comment']}'")
                    success = False
                else:
                    print(f"✅ 字段 {expected_name} 解析正确")
            else:
                print(f"❌ 缺少字段: {expected_name}")
                success = False
        
        if success:
            print("\n🎉 所有字段解析正确！注释功能正常工作。")
        else:
            print("\n❌ 存在解析错误。")
            
    finally:
        # 清理临时文件
        os.unlink(temp_file)

if __name__ == "__main__":
    test_comment_parsing()
