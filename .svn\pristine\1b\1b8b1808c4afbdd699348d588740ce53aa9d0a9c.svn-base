2025-05-06 16:26:02,819 - INFO - 正在扫描路径: C:/MyFileAudit/导表工具/config
2025-05-06 16:26:02,819 - INFO - 正在扫描路径: C:/MyFileAudit/导表工具/config
2025-05-06 16:26:02,819 - INFO - 找到 2 个Excel配置文件
2025-05-06 16:26:02,819 - INFO - 找到 2 个Excel配置文件
2025-05-06 16:26:07,162 - INFO - 开始导出全部 2 个文件...
2025-05-06 16:26:07,162 - INFO - 开始导出全部 2 个文件...
2025-05-06 16:26:07,176 - INFO - 开始处理: DragonBoatFestival.xlsx
2025-05-06 16:26:07,176 - INFO - 开始处理: DragonBoatFestival.xlsx
2025-05-06 16:26:07,230 - ERROR - 处理页签  CurrencySystem 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,233 - ERROR - 处理页签  FestivalQuestActive 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,230 - ERROR - 处理页签  CurrencySystem 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,237 - ERROR - 处理页签  DragonBoatGrid 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,240 - ERROR - 处理页签  DragonBoatEvent 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,233 - ERROR - 处理页签  FestivalQuestActive 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,242 - ERROR - 处理页签  DragonBoatBoard 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,237 - ERROR - 处理页签  DragonBoatGrid 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,243 - ERROR - 处理页签  DragonBoatRankReward 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,240 - ERROR - 处理页签  DragonBoatEvent 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,245 - WARNING - 页签 'FestivalAct' 忽略列 G (说明: '备注
策划自用')，因为该列缺少类型或字段名
2025-05-06 16:26:07,242 - ERROR - 处理页签  DragonBoatBoard 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,246 - WARNING - 页签 'FestivalActController' 忽略列 M (说明: '类型备注(策划自用)')，因为该列缺少类型或字段名
2025-05-06 16:26:07,243 - ERROR - 处理页签  DragonBoatRankReward 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,247 - WARNING - 页签 'FestivalShop' 忽略列 N (说明: '备注')，因为该列缺少类型或字段名
2025-05-06 16:26:07,245 - WARNING - 页签 'FestivalAct' 忽略列 G (说明: '备注
策划自用')，因为该列缺少类型或字段名
2025-05-06 16:26:07,248 - ERROR - 处理页签  FestivalActSignIn 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,246 - WARNING - 页签 'FestivalActController' 忽略列 M (说明: '类型备注(策划自用)')，因为该列缺少类型或字段名
2025-05-06 16:26:07,249 - ERROR - 处理页签  FestivalAct 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,247 - WARNING - 页签 'FestivalShop' 忽略列 N (说明: '备注')，因为该列缺少类型或字段名
2025-05-06 16:26:07,250 - ERROR - 处理页签  FestivalActController 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,248 - ERROR - 处理页签  FestivalActSignIn 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,251 - ERROR - 处理页签  FestivalShop 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,249 - ERROR - 处理页签  FestivalAct 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,252 - ERROR - 处理页签  FestivalQuest 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,250 - ERROR - 处理页签  FestivalActController 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,254 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 I，因为该列缺少类型或字段名
2025-05-06 16:26:07,251 - ERROR - 处理页签  FestivalShop 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,252 - ERROR - 处理页签  FestivalQuest 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,254 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 I，因为该列缺少类型或字段名
2025-05-06 16:26:07,255 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 J，因为该列缺少类型或字段名
2025-05-06 16:26:07,255 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 J，因为该列缺少类型或字段名
2025-05-06 16:26:07,256 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 K，因为该列缺少类型或字段名
2025-05-06 16:26:07,256 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 K，因为该列缺少类型或字段名
2025-05-06 16:26:07,257 - ERROR - 处理页签  FestivalActWarOrderLevel 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,257 - ERROR - 处理页签  FestivalActWarOrderLevel 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,287 - INFO - 开始处理: test1.xlsx
2025-05-06 16:26:07,287 - INFO - 开始处理: test1.xlsx
2025-05-06 16:26:07,302 - ERROR - 处理页签  Sheet1 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,302 - ERROR - 处理页签  Sheet1 失败: name 'debug_f' is not defined
2025-05-06 16:26:07,306 - INFO - 导出完成! 成功: 0, 跳过: 0, 失败: 2
2025-05-06 16:26:07,306 - INFO - 导出完成! 成功: 0, 跳过: 0, 失败: 2
