BOOL CGameData::LoadFestivalShopClientCfg()
{
    std::string DataPath = "data/FestivalShopClient.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        GSLogError(":LoadFestivalShopCfg Failed!  [%s]", DataPath.c_str());
        return FALSE;
    }
    
    m_mapFestivalShopCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stFestivalShopClientCfg cfg;
        fread(&cfg, sizeof(stFestivalShopClientCfg), 1, fp);

        m_mapFestivalShopCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}