#!/usr/bin/env python
# -*- coding: utf-8 -*-

import struct
import binascii
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.type_converter import TypeConverter

def test_numeric_array():
    """测试数值类型一维数组的二进制转换"""
    converter = TypeConverter()
    
    # 测试用例1：uint[10] 类型，有3个数据
    test_array1 = [1001, 1002, 1003]
    type_str1 = "uint[10]"
    binary_data1 = converter.convert_to_binary(test_array1, type_str1)
    
    print(f"\n测试用例1: {type_str1}, 数据: {test_array1}")
    print(f"二进制长度: {len(binary_data1)} 字节")
    
    # 显示十六进制
    hex_data1 = binascii.hexlify(binary_data1).decode('ascii')
    print(f"十六进制: {hex_data1}")
    
    # 按4字节分组显示
    print("按4字节分组:")
    for i in range(0, len(hex_data1), 8):
        chunk = hex_data1[i:i+8]
        print(f"字节 {i//8}: {chunk}")
    
    # 读取并验证结果
    print("读取验证:")
    values1 = []
    for i in range(10):  # 固定长度为10
        offset = i * 4
        if offset < len(binary_data1):
            value = struct.unpack('<I', binary_data1[offset:offset+4])[0]
            values1.append(value)
        else:
            values1.append(0)  # 如果超出范围，填充0
    print(f"读取的值: {values1}")
    
    # 模拟C++结构体读取
    print("\n模拟C++结构体读取:")
    struct_values = []
    for i in range(10):  # 固定读取10个元素
        offset = i * 4
        if offset < len(binary_data1):
            value = struct.unpack('<I', binary_data1[offset:offset+4])[0]
            struct_values.append(value)
        else:
            struct_values.append(0)  # 填充0
    print(f"C++结构体读取结果: {struct_values}")
    
    # 测试用例2：int[5] 类型，有5个数据
    test_array2 = [101, -202, 303, -404, 505]
    type_str2 = "int[5]"
    binary_data2 = converter.convert_to_binary(test_array2, type_str2)
    
    print(f"\n测试用例2: {type_str2}, 数据: {test_array2}")
    print(f"二进制长度: {len(binary_data2)} 字节")
    
    # 显示十六进制
    hex_data2 = binascii.hexlify(binary_data2).decode('ascii')
    print(f"十六进制: {hex_data2}")
    
    # 按4字节分组显示
    print("按4字节分组:")
    for i in range(0, len(hex_data2), 8):
        chunk = hex_data2[i:i+8]
        print(f"字节 {i//8}: {chunk}")
    
    # 读取并验证结果
    print("读取验证:")
    values2 = []
    for i in range(5):  # 固定长度为5
        offset = i * 4
        if offset < len(binary_data2):
            value = struct.unpack('<i', binary_data2[offset:offset+4])[0]
            values2.append(value)
        else:
            values2.append(0)  # 如果超出范围，填充0
    print(f"读取的值: {values2}")

if __name__ == "__main__":
    test_numeric_array() 