# 导表工具字符数组编码修复说明

## 问题描述

导表工具在处理二进制数据中的字符数组（char[]）时存在编码问题，特别是处理中文字符串时，导致：

1. C++解析出来的二进制数据中字符串字段（如 cIconPath、szName、szDesc）为空字符串
2. 写入txt文件的数据正常显示中文
3. 二进制数据写入时未正确使用UTF-8编码

## 原因分析

问题出在 `core/type_converter.py` 中处理 `char[]` 类型的部分，当前实现没有正确地处理字符串的UTF-8编码，导致：

1. 在将字符串转换为二进制数据时没有正确进行UTF-8编码
2. 没有正确处理字符串截断和填充

## 解决方案

我们提供了两种修复方案：

### 1. 猴子补丁方式（推荐临时使用）

这种方式不修改源代码，通过在运行时动态替换TypeConverter的方法来解决问题：

```python
# 在使用TypeConverter的代码中添加以下导入和调用：
from utils.char_array_fix import patch_char_array
patch_char_array()  # 应用字符数组编码修复
```

### 2. 直接修改源文件（推荐永久解决）

此方式会修改 `core/type_converter.py` 文件，永久解决编码问题。修改会先创建备份，以防需要恢复。

## 使用修复工具

我们提供了一个方便的修复工具 `apply_fix.py`，可以选择应用上述任一修复方式：

```bash
# 默认应用猴子补丁
python apply_fix.py

# 应用猴子补丁(同上)
python apply_fix.py --patch

# 修改源文件(永久解决)
python apply_fix.py --modify

# 恢复备份(如果之前修改了源文件)
python apply_fix.py --revert

# 显示帮助信息
python apply_fix.py --help
```

## 修复原理

修复方案通过以下步骤解决问题：

1. 正确地使用 UTF-8 编码将字符串转换为字节数组
2. 确保在字符串过长需要截断时不会破坏UTF-8编码（避免截断多字节字符）
3. 正确地添加字符串终止符和填充

## 测试验证

修复后，可以通过以下测试验证问题是否已解决：

1. 运行 `char_array_fix.py` 检查字符数组编码是否正确
2. 运行 `verify_fix.py` 验证二进制文件的创建和解析是否正常

## 注意事项

1. 如果使用猴子补丁方式，需要在每次使用TypeConverter的地方都应用补丁
2. 如果修改了源文件，可能需要重启应用程序以确保修改生效
3. 修改源文件前会创建备份，如果出现问题可以使用 `apply_fix.py --revert` 恢复 