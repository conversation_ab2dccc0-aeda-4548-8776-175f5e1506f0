#include <iostream>
#include <fstream>
#include <vector>

int main() {
    std::ifstream file("test.bin", std::ios::binary);
    if (!file) {
        std::cerr << "无法打开文件" << std::endl;
        return 1;
    }

    // 读取数组长度
    uint32_t length;
    file.read(reinterpret_cast<char*>(&length), sizeof(length));
    std::cout << "数组长度: " << length << std::endl;

    // 读取数组数据
    std::vector<uint32_t> values(length);
    for (uint32_t i = 0; i < length; ++i) {
        file.read(reinterpret_cast<char*>(&values[i]), sizeof(uint32_t));
    }

    // 打印读取的值
    std::cout << "读取的值: [";
    for (size_t i = 0; i < values.size(); ++i) {
        std::cout << values[i];
        if (i < values.size() - 1) {
            std::cout << ", ";
        }
    }
    std::cout << "]" << std::endl;

    return 0;
} 