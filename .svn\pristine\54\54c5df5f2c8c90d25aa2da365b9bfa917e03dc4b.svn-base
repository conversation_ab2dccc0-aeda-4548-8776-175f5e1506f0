BOOL CGameData::LoadCurrencySystemClientCfg()
{
    std::string DataPath = "data/CurrencySystemClient.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        GSLogError(":LoadCurrencySystemCfg Failed!  [%s]", DataPath.c_str());
        return FALSE;
    }
    
    m_mapCurrencySystemCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stCurrencySystemClientCfg cfg;
        fread(&cfg, sizeof(stCurrencySystemClientCfg), 1, fp);

        m_mapCurrencySystemCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}