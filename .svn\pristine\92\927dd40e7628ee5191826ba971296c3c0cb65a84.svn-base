BOOL CGameData::LoadFestivalQuestActiveServerCfg()
{
    std::string DataPath = "data/FestivalQuestActiveServer.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        ::g_pClientToLog->PrintFileDirct("CDataFile::LoadFestivalQuestActiveCfg fopen error");
        return FALSE;
    }
    
    m_mapFestivalQuestActiveCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stFestivalQuestActiveServerCfg cfg;
        fread(&cfg, sizeof(stFestivalQuestActiveServerCfg), 1, fp);

        m_mapFestivalQuestActiveCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}