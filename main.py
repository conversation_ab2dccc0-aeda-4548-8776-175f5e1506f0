#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QIcon
from PyQt5.QtCore import QSize
from ui.main_window import MainWindow

def load_app_icons():
    """加载所有尺寸的应用图标"""
    app_icon = QIcon()
    icon_sizes = [16, 32, 48, 64, 128, 256]
    
    icons_dir = os.path.join(os.getcwd(), "resources", "icons")
    for size in icon_sizes:
        icon_path = os.path.join(icons_dir, f"app-icon-{size}.png")
        if os.path.exists(icon_path):
            app_icon.addFile(icon_path, QSize(size, size))
    
    return app_icon

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 设置应用图标
    app_icon = load_app_icons()
    app.setWindowIcon(app_icon)
    
    window = MainWindow()
    window.show()
    sys.exit(app.exec_()) 