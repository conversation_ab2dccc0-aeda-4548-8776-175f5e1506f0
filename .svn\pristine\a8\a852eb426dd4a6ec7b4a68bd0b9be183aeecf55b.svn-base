2025-05-27 15:39:42,253 - INFO - 正在扫描路径: F:/配置表/新导表文件/Config
2025-05-27 15:39:42,253 - INFO - 正在扫描路径: F:/配置表/新导表文件/Config
2025-05-27 15:39:42,254 - INFO - 找到 2 个Excel配置文件
2025-05-27 15:39:42,254 - INFO - 找到 2 个Excel配置文件
2025-05-27 15:39:42,375 - INFO - 找到 26 个SData文件 (服务端: 13, 客户端: 13)
2025-05-27 15:39:42,375 - INFO - 找到 26 个SData文件 (服务端: 13, 客户端: 13)
2025-05-27 15:39:42,376 - INFO - 找到 26 个加载函数文件
2025-05-27 15:39:42,376 - INFO - 找到 26 个加载函数文件
2025-05-27 15:39:47,040 - INFO - 开始导出全部 2 个文件...
2025-05-27 15:39:47,040 - INFO - 开始导出全部 2 个文件...
2025-05-27 15:39:47,050 - INFO - 开始处理: 活动货币表.xlsx
2025-05-27 15:39:47,050 - INFO - 开始处理: 活动货币表.xlsx
2025-05-27 15:39:47,076 - WARNING - 页签 'CurrencySystem' 忽略 A列 (说明: '货币表（非道具不在背包内）')，没有类型或字段名
2025-05-27 15:39:47,076 - WARNING - 页签 'CurrencySystem' 忽略 A列 (说明: '货币表（非道具不在背包内）')，没有类型或字段名
2025-05-27 15:39:47,081 - INFO - 为页签 CurrencySystem 生成加载函数文件...
2025-05-27 15:39:47,081 - INFO - 为页签 CurrencySystem 生成加载函数文件...
2025-05-27 15:39:47,082 - INFO - 服务端加载函数文件 LoadCurrencySystemServer.txt 生成成功
2025-05-27 15:39:47,082 - INFO - 服务端加载函数文件 LoadCurrencySystemServer.txt 生成成功
2025-05-27 15:39:47,083 - INFO - 客户端加载函数文件 LoadCurrencySystemClient.txt 生成成功
2025-05-27 15:39:47,083 - INFO - 客户端加载函数文件 LoadCurrencySystemClient.txt 生成成功
2025-05-27 15:39:47,083 - INFO - 页签 CurrencySystem 处理完成
2025-05-27 15:39:47,083 - INFO - 页签 CurrencySystem 处理完成
2025-05-27 15:39:47,102 - INFO - 开始处理: 节日活动配置(适配端午).xlsx
2025-05-27 15:39:47,102 - INFO - 开始处理: 节日活动配置(适配端午).xlsx
2025-05-27 15:39:47,149 - WARNING - 页签 'FestivalActController' 忽略 A列 (说明: '活动管理总表')，没有类型或字段名
2025-05-27 15:39:47,149 - WARNING - 页签 'FestivalActController' 忽略 A列 (说明: '活动管理总表')，没有类型或字段名
2025-05-27 15:39:47,152 - WARNING - 页签 'FestivalAct' 忽略 A列 (说明: '活动预告页面信息表')，没有类型或字段名
2025-05-27 15:39:47,152 - WARNING - 页签 'FestivalAct' 忽略 A列 (说明: '活动预告页面信息表')，没有类型或字段名
2025-05-27 15:39:47,155 - WARNING - 页签 'FestivalActController' 忽略 O列 (说明: '类型备注(策划自用)')，没有类型或字段名
2025-05-27 15:39:47,155 - WARNING - 页签 'FestivalActController' 忽略 O列 (说明: '类型备注(策划自用)')，没有类型或字段名
2025-05-27 15:39:47,155 - WARNING - 页签 'FestivalActSignIn' 忽略 A列 (说明: '签到奖励表')，没有类型或字段名
2025-05-27 15:39:47,155 - WARNING - 页签 'FestivalActSignIn' 忽略 A列 (说明: '签到奖励表')，没有类型或字段名
2025-05-27 15:39:47,158 - WARNING - 页签 'FestivalAct' 忽略 H列 (说明: '备注
策划自用')，没有类型或字段名
2025-05-27 15:39:47,158 - WARNING - 页签 'FestivalAct' 忽略 H列 (说明: '备注
策划自用')，没有类型或字段名
2025-05-27 15:39:47,158 - WARNING - 页签 'FestivalQuest' 忽略 A列 (说明: '活动任务表')，没有类型或字段名
2025-05-27 15:39:47,158 - WARNING - 页签 'FestivalQuest' 忽略 A列 (说明: '活动任务表')，没有类型或字段名
2025-05-27 15:39:47,161 - WARNING - 页签 'FestivalQuest' 忽略 K列，没有类型或字段名
2025-05-27 15:39:47,161 - WARNING - 页签 'FestivalQuest' 忽略 K列，没有类型或字段名
2025-05-27 15:39:47,162 - WARNING - 页签 'FestivalQuest' 忽略 L列，没有类型或字段名
2025-05-27 15:39:47,162 - WARNING - 页签 'FestivalQuest' 忽略 L列，没有类型或字段名
2025-05-27 15:39:47,162 - WARNING - 页签 'FestivalQuest' 忽略 M列，没有类型或字段名
2025-05-27 15:39:47,162 - WARNING - 页签 'FestivalQuest' 忽略 M列，没有类型或字段名
2025-05-27 15:39:47,163 - WARNING - 页签 'FestivalQuest' 忽略 N列，没有类型或字段名
2025-05-27 15:39:47,163 - WARNING - 页签 'FestivalQuest' 忽略 N列，没有类型或字段名
2025-05-27 15:39:47,175 - INFO - 为页签 FestivalActSignIn 生成加载函数文件...
2025-05-27 15:39:47,175 - INFO - 为页签 FestivalActSignIn 生成加载函数文件...
2025-05-27 15:39:47,176 - INFO - 为页签 FestivalAct 生成加载函数文件...
2025-05-27 15:39:47,176 - INFO - 为页签 FestivalAct 生成加载函数文件...
2025-05-27 15:39:47,177 - INFO - 服务端加载函数文件 LoadFestivalActServer.txt 生成成功
2025-05-27 15:39:47,177 - INFO - 服务端加载函数文件 LoadFestivalActServer.txt 生成成功
2025-05-27 15:39:47,177 - INFO - 服务端加载函数文件 LoadFestivalActSignInServer.txt 生成成功
2025-05-27 15:39:47,177 - INFO - 服务端加载函数文件 LoadFestivalActSignInServer.txt 生成成功
2025-05-27 15:39:47,179 - INFO - 为页签 FestivalActController 生成加载函数文件...
2025-05-27 15:39:47,179 - INFO - 为页签 FestivalActController 生成加载函数文件...
2025-05-27 15:39:47,181 - INFO - 客户端加载函数文件 LoadFestivalActClient.txt 生成成功
2025-05-27 15:39:47,181 - INFO - 客户端加载函数文件 LoadFestivalActClient.txt 生成成功
2025-05-27 15:39:47,182 - INFO - 客户端加载函数文件 LoadFestivalActSignInClient.txt 生成成功
2025-05-27 15:39:47,182 - INFO - 客户端加载函数文件 LoadFestivalActSignInClient.txt 生成成功
2025-05-27 15:39:47,182 - INFO - 页签 FestivalAct 处理完成
2025-05-27 15:39:47,182 - INFO - 页签 FestivalAct 处理完成
2025-05-27 15:39:47,182 - INFO - 为页签 FestivalQuest 生成加载函数文件...
2025-05-27 15:39:47,182 - INFO - 为页签 FestivalQuest 生成加载函数文件...
2025-05-27 15:39:47,182 - INFO - 页签 FestivalActSignIn 处理完成
2025-05-27 15:39:47,182 - INFO - 页签 FestivalActSignIn 处理完成
2025-05-27 15:39:47,183 - WARNING - 页签 'FestivalQuestActive' 忽略 A列 (说明: '任务进度奖励表')，没有类型或字段名
2025-05-27 15:39:47,183 - WARNING - 页签 'FestivalQuestActive' 忽略 A列 (说明: '任务进度奖励表')，没有类型或字段名
2025-05-27 15:39:47,183 - INFO - 服务端加载函数文件 LoadFestivalActControllerServer.txt 生成成功
2025-05-27 15:39:47,183 - INFO - 服务端加载函数文件 LoadFestivalActControllerServer.txt 生成成功
2025-05-27 15:39:47,184 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略 A列 (说明: '活动通行证等级')，没有类型或字段名
2025-05-27 15:39:47,184 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略 A列 (说明: '活动通行证等级')，没有类型或字段名
2025-05-27 15:39:47,187 - INFO - 服务端加载函数文件 LoadFestivalQuestServer.txt 生成成功
2025-05-27 15:39:47,187 - INFO - 服务端加载函数文件 LoadFestivalQuestServer.txt 生成成功
2025-05-27 15:39:47,187 - INFO - 客户端加载函数文件 LoadFestivalActControllerClient.txt 生成成功
2025-05-27 15:39:47,187 - INFO - 客户端加载函数文件 LoadFestivalActControllerClient.txt 生成成功
2025-05-27 15:39:47,187 - INFO - 页签 FestivalActController 处理完成
2025-05-27 15:39:47,187 - INFO - 页签 FestivalActController 处理完成
2025-05-27 15:39:47,188 - WARNING - 页签 'FestivalShop' 忽略 A列 (说明: '活动商店表')，没有类型或字段名
2025-05-27 15:39:47,188 - WARNING - 页签 'FestivalShop' 忽略 A列 (说明: '活动商店表')，没有类型或字段名
2025-05-27 15:39:47,188 - WARNING - 页签 'FestivalShop' 忽略 O列 (说明: '备注')，没有类型或字段名
2025-05-27 15:39:47,188 - WARNING - 页签 'FestivalShop' 忽略 O列 (说明: '备注')，没有类型或字段名
2025-05-27 15:39:47,196 - INFO - 客户端加载函数文件 LoadFestivalQuestClient.txt 生成成功
2025-05-27 15:39:47,196 - INFO - 客户端加载函数文件 LoadFestivalQuestClient.txt 生成成功
2025-05-27 15:39:47,196 - INFO - 页签 FestivalQuest 处理完成
2025-05-27 15:39:47,196 - INFO - 页签 FestivalQuest 处理完成
2025-05-27 15:39:47,199 - WARNING - 页签 'DragonBoatBoard' 忽略 A列 (说明: '棋盘参数总表')，没有类型或字段名
2025-05-27 15:39:47,199 - WARNING - 页签 'DragonBoatBoard' 忽略 A列 (说明: '棋盘参数总表')，没有类型或字段名
2025-05-27 15:39:47,202 - INFO - 为页签 FestivalQuestActive 生成加载函数文件...
2025-05-27 15:39:47,202 - INFO - 为页签 FestivalQuestActive 生成加载函数文件...
2025-05-27 15:39:47,205 - INFO - 为页签 FestivalActWarOrderLevel 生成加载函数文件...
2025-05-27 15:39:47,205 - INFO - 为页签 FestivalActWarOrderLevel 生成加载函数文件...
2025-05-27 15:39:47,206 - INFO - 服务端加载函数文件 LoadFestivalQuestActiveServer.txt 生成成功
2025-05-27 15:39:47,206 - INFO - 服务端加载函数文件 LoadFestivalQuestActiveServer.txt 生成成功
2025-05-27 15:39:47,207 - INFO - 服务端加载函数文件 LoadFestivalActWarOrderLevelServer.txt 生成成功
2025-05-27 15:39:47,207 - INFO - 服务端加载函数文件 LoadFestivalActWarOrderLevelServer.txt 生成成功
2025-05-27 15:39:47,208 - INFO - 客户端加载函数文件 LoadFestivalQuestActiveClient.txt 生成成功
2025-05-27 15:39:47,208 - INFO - 客户端加载函数文件 LoadFestivalQuestActiveClient.txt 生成成功
2025-05-27 15:39:47,209 - INFO - 页签 FestivalQuestActive 处理完成
2025-05-27 15:39:47,209 - INFO - 页签 FestivalQuestActive 处理完成
2025-05-27 15:39:47,209 - INFO - 为页签 FestivalShop 生成加载函数文件...
2025-05-27 15:39:47,209 - INFO - 为页签 FestivalShop 生成加载函数文件...
2025-05-27 15:39:47,210 - WARNING - 页签 'DragonBoatGrid' 忽略 A列 (说明: '龙舟棋盘表')，没有类型或字段名
2025-05-27 15:39:47,210 - WARNING - 页签 'DragonBoatGrid' 忽略 A列 (说明: '龙舟棋盘表')，没有类型或字段名
2025-05-27 15:39:47,219 - INFO - 客户端加载函数文件 LoadFestivalActWarOrderLevelClient.txt 生成成功
2025-05-27 15:39:47,219 - INFO - 客户端加载函数文件 LoadFestivalActWarOrderLevelClient.txt 生成成功
2025-05-27 15:39:47,219 - INFO - 页签 FestivalActWarOrderLevel 处理完成
2025-05-27 15:39:47,219 - INFO - 页签 FestivalActWarOrderLevel 处理完成
2025-05-27 15:39:47,219 - INFO - 为页签 DragonBoatBoard 生成加载函数文件...
2025-05-27 15:39:47,219 - INFO - 为页签 DragonBoatBoard 生成加载函数文件...
2025-05-27 15:39:47,220 - WARNING - 页签 'DragonBoatEvent' 忽略 A列 (说明: '龙舟事件表')，没有类型或字段名
2025-05-27 15:39:47,220 - INFO - 服务端加载函数文件 LoadFestivalShopServer.txt 生成成功
2025-05-27 15:39:47,220 - WARNING - 页签 'DragonBoatEvent' 忽略 A列 (说明: '龙舟事件表')，没有类型或字段名
2025-05-27 15:39:47,220 - INFO - 服务端加载函数文件 LoadFestivalShopServer.txt 生成成功
2025-05-27 15:39:47,221 - WARNING - 页签 'DragonBoatEvent' 忽略 I列 (说明: '自用备注')，没有类型或字段名
2025-05-27 15:39:47,221 - WARNING - 页签 'DragonBoatEvent' 忽略 I列 (说明: '自用备注')，没有类型或字段名
2025-05-27 15:39:47,222 - WARNING - 页签 'DragonBoatEvent' 忽略 J列 (说明: '自用备注')，没有类型或字段名
2025-05-27 15:39:47,222 - WARNING - 页签 'DragonBoatEvent' 忽略 J列 (说明: '自用备注')，没有类型或字段名
2025-05-27 15:39:47,222 - WARNING - 页签 'DragonBoatEvent' 忽略 K列 (说明: '自用备注')，没有类型或字段名
2025-05-27 15:39:47,222 - WARNING - 页签 'DragonBoatEvent' 忽略 K列 (说明: '自用备注')，没有类型或字段名
2025-05-27 15:39:47,223 - INFO - 服务端加载函数文件 LoadDragonBoatBoardServer.txt 生成成功
2025-05-27 15:39:47,223 - INFO - 服务端加载函数文件 LoadDragonBoatBoardServer.txt 生成成功
2025-05-27 15:39:47,225 - INFO - 客户端加载函数文件 LoadFestivalShopClient.txt 生成成功
2025-05-27 15:39:47,225 - INFO - 客户端加载函数文件 LoadFestivalShopClient.txt 生成成功
2025-05-27 15:39:47,228 - INFO - 页签 FestivalShop 处理完成
2025-05-27 15:39:47,228 - INFO - 页签 FestivalShop 处理完成
2025-05-27 15:39:47,232 - WARNING - 页签 'DragonBoatRankReward' 忽略 A列 (说明: '排名档位表')，没有类型或字段名
2025-05-27 15:39:47,232 - WARNING - 页签 'DragonBoatRankReward' 忽略 A列 (说明: '排名档位表')，没有类型或字段名
2025-05-27 15:39:47,235 - INFO - 客户端加载函数文件 LoadDragonBoatBoardClient.txt 生成成功
2025-05-27 15:39:47,235 - INFO - 客户端加载函数文件 LoadDragonBoatBoardClient.txt 生成成功
2025-05-27 15:39:47,239 - INFO - 页签 DragonBoatBoard 处理完成
2025-05-27 15:39:47,239 - INFO - 页签 DragonBoatBoard 处理完成
2025-05-27 15:39:47,262 - INFO - 为页签 FestivalZongzi 生成加载函数文件...
2025-05-27 15:39:47,262 - INFO - 为页签 FestivalZongzi 生成加载函数文件...
2025-05-27 15:39:47,262 - INFO - 为页签 DragonBoatRankReward 生成加载函数文件...
2025-05-27 15:39:47,262 - INFO - 为页签 DragonBoatRankReward 生成加载函数文件...
2025-05-27 15:39:47,263 - INFO - 为页签 DragonBoatGrid 生成加载函数文件...
2025-05-27 15:39:47,263 - INFO - 为页签 DragonBoatGrid 生成加载函数文件...
2025-05-27 15:39:47,263 - INFO - 为页签 DragonBoatEvent 生成加载函数文件...
2025-05-27 15:39:47,263 - INFO - 为页签 DragonBoatEvent 生成加载函数文件...
2025-05-27 15:39:47,264 - INFO - 服务端加载函数文件 LoadDragonBoatRankRewardServer.txt 生成成功
2025-05-27 15:39:47,264 - INFO - 服务端加载函数文件 LoadDragonBoatRankRewardServer.txt 生成成功
2025-05-27 15:39:47,264 - INFO - 服务端加载函数文件 LoadFestivalZongziServer.txt 生成成功
2025-05-27 15:39:47,264 - INFO - 服务端加载函数文件 LoadFestivalZongziServer.txt 生成成功
2025-05-27 15:39:47,264 - INFO - 服务端加载函数文件 LoadDragonBoatEventServer.txt 生成成功
2025-05-27 15:39:47,264 - INFO - 服务端加载函数文件 LoadDragonBoatGridServer.txt 生成成功
2025-05-27 15:39:47,264 - INFO - 服务端加载函数文件 LoadDragonBoatEventServer.txt 生成成功
2025-05-27 15:39:47,264 - INFO - 服务端加载函数文件 LoadDragonBoatGridServer.txt 生成成功
2025-05-27 15:39:47,265 - INFO - 客户端加载函数文件 LoadDragonBoatRankRewardClient.txt 生成成功
2025-05-27 15:39:47,265 - INFO - 客户端加载函数文件 LoadDragonBoatRankRewardClient.txt 生成成功
2025-05-27 15:39:47,265 - INFO - 页签 DragonBoatRankReward 处理完成
2025-05-27 15:39:47,265 - INFO - 页签 DragonBoatRankReward 处理完成
2025-05-27 15:39:47,265 - INFO - 客户端加载函数文件 LoadFestivalZongziClient.txt 生成成功
2025-05-27 15:39:47,265 - INFO - 客户端加载函数文件 LoadFestivalZongziClient.txt 生成成功
2025-05-27 15:39:47,266 - INFO - 客户端加载函数文件 LoadDragonBoatGridClient.txt 生成成功
2025-05-27 15:39:47,266 - INFO - 客户端加载函数文件 LoadDragonBoatGridClient.txt 生成成功
2025-05-27 15:39:47,266 - INFO - 页签 FestivalZongzi 处理完成
2025-05-27 15:39:47,266 - INFO - 页签 FestivalZongzi 处理完成
2025-05-27 15:39:47,266 - INFO - 客户端加载函数文件 LoadDragonBoatEventClient.txt 生成成功
2025-05-27 15:39:47,266 - INFO - 客户端加载函数文件 LoadDragonBoatEventClient.txt 生成成功
2025-05-27 15:39:47,266 - INFO - 页签 DragonBoatGrid 处理完成
2025-05-27 15:39:47,266 - INFO - 页签 DragonBoatGrid 处理完成
2025-05-27 15:39:47,267 - INFO - 页签 DragonBoatEvent 处理完成
2025-05-27 15:39:47,267 - INFO - 页签 DragonBoatEvent 处理完成
2025-05-27 15:39:47,280 - INFO - 导出完成! 成功: 2, 跳过: 0, 失败: 0
2025-05-27 15:39:47,280 - INFO - 导出完成! 成功: 2, 跳过: 0, 失败: 0
2025-05-27 15:39:47,293 - INFO - 找到 26 个SData文件 (服务端: 13, 客户端: 13)
2025-05-27 15:39:47,293 - INFO - 找到 26 个SData文件 (服务端: 13, 客户端: 13)
2025-05-27 15:39:47,294 - INFO - 找到 26 个加载函数文件
2025-05-27 15:39:47,294 - INFO - 找到 26 个加载函数文件
