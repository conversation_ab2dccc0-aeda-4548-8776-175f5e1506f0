2025-05-06 16:02:23,847 - INFO - 正在扫描路径: C:/MyFileAudit/导表工具/config
2025-05-06 16:02:23,847 - INFO - 正在扫描路径: C:/MyFileAudit/导表工具/config
2025-05-06 16:02:23,847 - INFO - 找到 1 个Excel配置文件
2025-05-06 16:02:23,847 - INFO - 找到 1 个Excel配置文件
2025-05-06 16:02:27,161 - INFO - 开始导出全部 1 个文件...
2025-05-06 16:02:27,161 - INFO - 开始导出全部 1 个文件...
2025-05-06 16:02:27,173 - INFO - 开始处理: DragonBoatFestival.xlsx
2025-05-06 16:02:27,173 - INFO - 开始处理: DragonBoatFestival.xlsx
2025-05-06 16:02:27,223 - WARNING - 页签 'DragonBoatGrid' 忽略列 S，因为该列缺少类型或字段名
2025-05-06 16:02:27,223 - WARNING - 页签 'DragonBoatGrid' 忽略列 S，因为该列缺少类型或字段名
2025-05-06 16:02:27,225 - WARNING - 页签 'DragonBoatGrid' 忽略列 T，因为该列缺少类型或字段名
2025-05-06 16:02:27,225 - WARNING - 页签 'DragonBoatGrid' 忽略列 T，因为该列缺少类型或字段名
2025-05-06 16:02:27,227 - WARNING - array_match: None , field_type: uint , cell_value: 1
2025-05-06 16:02:27,227 - WARNING - array_match: None , field_type: uint , cell_value: 1
2025-05-06 16:02:27,229 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,234 - WARNING - 表格 'DragonBoatEvent' 记录 #7 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,229 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,234 - WARNING - 表格 'DragonBoatEvent' 记录 #7 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,235 - WARNING - array_match: None , field_type: uint , cell_value: 1
2025-05-06 16:02:27,237 - WARNING - 表格 'DragonBoatEvent' 记录 #8 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,235 - WARNING - array_match: None , field_type: uint , cell_value: 1
2025-05-06 16:02:27,237 - WARNING - 表格 'DragonBoatEvent' 记录 #8 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,238 - WARNING - array_match: None , field_type: uint , cell_value: 1
2025-05-06 16:02:27,238 - WARNING - array_match: None , field_type: uint , cell_value: 1
2025-05-06 16:02:27,240 - WARNING - 表格 'DragonBoatEvent' 记录 #9 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,242 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,240 - WARNING - 表格 'DragonBoatEvent' 记录 #9 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,242 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,245 - WARNING - array_match: None , field_type: uint , cell_value: 1
2025-05-06 16:02:27,245 - WARNING - array_match: None , field_type: uint , cell_value: 1
2025-05-06 16:02:27,246 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,246 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,247 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,247 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,249 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,249 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,255 - INFO - 页签 FestivalQuestActive 处理完成
2025-05-06 16:02:27,256 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,255 - INFO - 页签 FestivalQuestActive 处理完成
2025-05-06 16:02:27,256 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,260 - INFO - 页签 CurrencySystem 处理完成
2025-05-06 16:02:27,260 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,260 - INFO - 页签 CurrencySystem 处理完成
2025-05-06 16:02:27,260 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,262 - WARNING - 表格 'DragonBoatEvent' 记录 #7 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,263 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,262 - WARNING - 表格 'DragonBoatEvent' 记录 #7 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,263 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,265 - WARNING - 表格 'DragonBoatEvent' 记录 #8 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,266 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,265 - WARNING - 表格 'DragonBoatEvent' 记录 #8 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,266 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,268 - WARNING - 表格 'DragonBoatRankReward' 记录 #1 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,269 - WARNING - 表格 'DragonBoatEvent' 记录 #9 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,268 - WARNING - 表格 'DragonBoatRankReward' 记录 #1 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,269 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,269 - WARNING - 表格 'DragonBoatEvent' 记录 #9 字段 'dwReward' (int[4][2]) 二维数组行数 1 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,270 - WARNING - 表格 'DragonBoatRankReward' 记录 #2 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,269 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,270 - WARNING - 表格 'DragonBoatRankReward' 记录 #2 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,272 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,273 - WARNING - 表格 'DragonBoatRankReward' 记录 #3 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,272 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,273 - WARNING - 表格 'DragonBoatRankReward' 记录 #3 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,275 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,275 - WARNING - 表格 'DragonBoatRankReward' 记录 #4 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,275 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,275 - WARNING - 表格 'DragonBoatRankReward' 记录 #4 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,277 - INFO - 页签 DragonBoatEvent 处理完成
2025-05-06 16:02:27,277 - WARNING - 页签 'DragonBoatGrid' 记录 #6 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,277 - INFO - 页签 DragonBoatEvent 处理完成
2025-05-06 16:02:27,279 - INFO - 页签 DragonBoatBoard 处理完成
2025-05-06 16:02:27,279 - WARNING - 表格 'DragonBoatRankReward' 记录 #5 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,277 - WARNING - 页签 'DragonBoatGrid' 记录 #6 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,280 - WARNING - 页签 'FestivalAct' 忽略列 G (说明: '备注
策划自用')，因为该列缺少类型或字段名
2025-05-06 16:02:27,279 - INFO - 页签 DragonBoatBoard 处理完成
2025-05-06 16:02:27,281 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,279 - WARNING - 表格 'DragonBoatRankReward' 记录 #5 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,282 - WARNING - 页签 'FestivalActController' 忽略列 M (说明: '类型备注(策划自用)')，因为该列缺少类型或字段名
2025-05-06 16:02:27,280 - WARNING - 页签 'FestivalAct' 忽略列 G (说明: '备注
策划自用')，因为该列缺少类型或字段名
2025-05-06 16:02:27,283 - WARNING - 表格 'DragonBoatRankReward' 记录 #6 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,281 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,282 - WARNING - 页签 'FestivalActController' 忽略列 M (说明: '类型备注(策划自用)')，因为该列缺少类型或字段名
2025-05-06 16:02:27,283 - WARNING - 表格 'DragonBoatRankReward' 记录 #6 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,285 - WARNING - array_match: None , field_type: uint , cell_value: 2
2025-05-06 16:02:27,285 - WARNING - array_match: None , field_type: uint , cell_value: 2
2025-05-06 16:02:27,286 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwPreviewStartTime' 字段的值 '2025-05-01 12:34:56' 不是有效的数值
2025-05-06 16:02:27,287 - WARNING - 表格 'DragonBoatRankReward' 记录 #7 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,286 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwPreviewStartTime' 字段的值 '2025-05-01 12:34:56' 不是有效的数值
2025-05-06 16:02:27,288 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,287 - WARNING - 表格 'DragonBoatRankReward' 记录 #7 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,288 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,290 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwStartTime' 字段的值 '2025-05-02 12:34:56' 不是有效的数值
2025-05-06 16:02:27,291 - WARNING - 表格 'DragonBoatRankReward' 记录 #8 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,290 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwStartTime' 字段的值 '2025-05-02 12:34:56' 不是有效的数值
2025-05-06 16:02:27,291 - WARNING - array_match: None , field_type: uint , cell_value: 1
2025-05-06 16:02:27,291 - WARNING - 表格 'DragonBoatRankReward' 记录 #8 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,292 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwEndTime' 字段的值 '2025-05-03 12:34:56' 不是有效的数值
2025-05-06 16:02:27,291 - WARNING - array_match: None , field_type: uint , cell_value: 1
2025-05-06 16:02:27,293 - WARNING - 表格 'DragonBoatRankReward' 记录 #9 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,292 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwEndTime' 字段的值 '2025-05-03 12:34:56' 不是有效的数值
2025-05-06 16:02:27,294 - WARNING - array_match: None , field_type: uint , cell_value: 2
2025-05-06 16:02:27,293 - WARNING - 表格 'DragonBoatRankReward' 记录 #9 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,295 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwRewardEndTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:02:27,294 - WARNING - array_match: None , field_type: uint , cell_value: 2
2025-05-06 16:02:27,297 - WARNING - 表格 'DragonBoatRankReward' 记录 #10 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,295 - WARNING - 页签 'FestivalActController' 记录 #6 行 'dwRewardEndTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:02:27,298 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,297 - WARNING - 表格 'DragonBoatRankReward' 记录 #10 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,299 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwPreviewStartTime' 字段的值 '2025-05-02 12:34:56' 不是有效的数值
2025-05-06 16:02:27,298 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,300 - WARNING - 表格 'DragonBoatRankReward' 记录 #11 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,299 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwPreviewStartTime' 字段的值 '2025-05-02 12:34:56' 不是有效的数值
2025-05-06 16:02:27,300 - WARNING - 表格 'DragonBoatRankReward' 记录 #11 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,301 - INFO - 页签 FestivalAct 处理完成
2025-05-06 16:02:27,301 - WARNING - array_match: None , field_type: uint , cell_value: 2
2025-05-06 16:02:27,301 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwStartTime' 字段的值 '2025-05-03 12:34:56' 不是有效的数值
2025-05-06 16:02:27,301 - INFO - 页签 FestivalAct 处理完成
2025-05-06 16:02:27,302 - WARNING - 表格 'DragonBoatRankReward' 记录 #12 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,301 - WARNING - array_match: None , field_type: uint , cell_value: 2
2025-05-06 16:02:27,303 - WARNING - 页签 'FestivalShop' 忽略列 N (说明: '备注')，因为该列缺少类型或字段名
2025-05-06 16:02:27,301 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwStartTime' 字段的值 '2025-05-03 12:34:56' 不是有效的数值
2025-05-06 16:02:27,304 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,302 - WARNING - 表格 'DragonBoatRankReward' 记录 #12 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,303 - WARNING - 页签 'FestivalShop' 忽略列 N (说明: '备注')，因为该列缺少类型或字段名
2025-05-06 16:02:27,305 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwEndTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:02:27,304 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,306 - WARNING - 表格 'DragonBoatRankReward' 记录 #13 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,305 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwEndTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:02:27,307 - WARNING - 页签 'FestivalShop' 记录 #6 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:02:27,308 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,306 - WARNING - 表格 'DragonBoatRankReward' 记录 #13 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,309 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwRewardEndTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:02:27,307 - WARNING - 页签 'FestivalShop' 记录 #6 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:02:27,310 - WARNING - 表格 'DragonBoatRankReward' 记录 #14 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,308 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,310 - WARNING - 页签 'FestivalShop' 记录 #6 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:02:27,309 - WARNING - 页签 'FestivalActController' 记录 #7 行 'dwRewardEndTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:02:27,311 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,310 - WARNING - 表格 'DragonBoatRankReward' 记录 #14 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,312 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwPreviewStartTime' 字段的值 '2025-05-03 12:34:56' 不是有效的数值
2025-05-06 16:02:27,310 - WARNING - 页签 'FestivalShop' 记录 #6 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:02:27,313 - WARNING - 表格 'DragonBoatRankReward' 记录 #15 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,311 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,314 - WARNING - 页签 'FestivalShop' 记录 #7 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:02:27,312 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwPreviewStartTime' 字段的值 '2025-05-03 12:34:56' 不是有效的数值
2025-05-06 16:02:27,315 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,313 - WARNING - 表格 'DragonBoatRankReward' 记录 #15 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,314 - WARNING - 页签 'FestivalShop' 记录 #7 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:02:27,316 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwStartTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:02:27,315 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,317 - WARNING - 表格 'DragonBoatRankReward' 记录 #16 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,316 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwStartTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:02:27,317 - WARNING - 页签 'FestivalShop' 记录 #7 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:02:27,317 - WARNING - 表格 'DragonBoatRankReward' 记录 #16 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,318 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,317 - WARNING - 页签 'FestivalShop' 记录 #7 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:02:27,319 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwEndTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:02:27,318 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,320 - WARNING - 表格 'DragonBoatRankReward' 记录 #17 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,319 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwEndTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:02:27,321 - WARNING - 页签 'FestivalShop' 记录 #8 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:02:27,320 - WARNING - 表格 'DragonBoatRankReward' 记录 #17 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,321 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,321 - WARNING - 页签 'FestivalShop' 记录 #8 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:02:27,322 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwRewardEndTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:02:27,321 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,323 - WARNING - 表格 'DragonBoatRankReward' 记录 #18 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,322 - WARNING - 页签 'FestivalActController' 记录 #8 行 'dwRewardEndTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:02:27,324 - WARNING - 页签 'FestivalShop' 记录 #8 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:02:27,323 - WARNING - 表格 'DragonBoatRankReward' 记录 #18 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,324 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,324 - WARNING - 页签 'FestivalShop' 记录 #8 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:02:27,325 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwPreviewStartTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:02:27,324 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,327 - WARNING - 页签 'FestivalShop' 记录 #9 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:02:27,325 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwPreviewStartTime' 字段的值 '2025-05-04 12:34:56' 不是有效的数值
2025-05-06 16:02:27,328 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,327 - WARNING - 页签 'FestivalShop' 记录 #9 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:02:27,328 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwStartTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:02:27,328 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,329 - WARNING - 页签 'FestivalShop' 记录 #9 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:02:27,328 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwStartTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:02:27,330 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,329 - WARNING - 页签 'FestivalShop' 记录 #9 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:02:27,331 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwEndTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:02:27,330 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,332 - WARNING - 页签 'FestivalShop' 记录 #10 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:02:27,331 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwEndTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:02:27,333 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,334 - WARNING - 表格 'DragonBoatRankReward' 记录 #1 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,332 - WARNING - 页签 'FestivalShop' 记录 #10 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:02:27,334 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwRewardEndTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:02:27,333 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,335 - WARNING - 页签 'FestivalShop' 记录 #10 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:02:27,334 - WARNING - 表格 'DragonBoatRankReward' 记录 #1 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,336 - WARNING - 页签 'DragonBoatGrid' 记录 #7 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,337 - WARNING - 表格 'DragonBoatRankReward' 记录 #2 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,334 - WARNING - 页签 'FestivalActController' 记录 #9 行 'dwRewardEndTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:02:27,335 - WARNING - 页签 'FestivalShop' 记录 #10 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:02:27,338 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwPreviewStartTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:02:27,336 - WARNING - 页签 'DragonBoatGrid' 记录 #7 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,339 - WARNING - 页签 'FestivalShop' 记录 #11 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:02:27,337 - WARNING - 表格 'DragonBoatRankReward' 记录 #2 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,340 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,338 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwPreviewStartTime' 字段的值 '2025-05-05 12:34:56' 不是有效的数值
2025-05-06 16:02:27,340 - WARNING - 表格 'DragonBoatRankReward' 记录 #3 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,339 - WARNING - 页签 'FestivalShop' 记录 #11 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:02:27,341 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwStartTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:02:27,340 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,342 - WARNING - 页签 'FestivalShop' 记录 #11 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:02:27,340 - WARNING - 表格 'DragonBoatRankReward' 记录 #3 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,341 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwStartTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:02:27,343 - WARNING - array_match: None , field_type: uint , cell_value: 3
2025-05-06 16:02:27,342 - WARNING - 页签 'FestivalShop' 记录 #11 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:02:27,344 - WARNING - 表格 'DragonBoatRankReward' 记录 #4 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,343 - WARNING - array_match: None , field_type: uint , cell_value: 3
2025-05-06 16:02:27,344 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwEndTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:02:27,344 - WARNING - 表格 'DragonBoatRankReward' 记录 #4 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,345 - WARNING - 页签 'FestivalShop' 记录 #12 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:02:27,344 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwEndTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:02:27,346 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,345 - WARNING - 页签 'FestivalShop' 记录 #12 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:02:27,347 - WARNING - 表格 'DragonBoatRankReward' 记录 #5 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,346 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,348 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwRewardEndTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:02:27,347 - WARNING - 表格 'DragonBoatRankReward' 记录 #5 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,349 - WARNING - 页签 'FestivalShop' 记录 #12 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:02:27,349 - WARNING - array_match: None , field_type: uint , cell_value: 1
2025-05-06 16:02:27,348 - WARNING - 页签 'FestivalActController' 记录 #10 行 'dwRewardEndTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:02:27,350 - WARNING - 表格 'DragonBoatRankReward' 记录 #6 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,349 - WARNING - 页签 'FestivalShop' 记录 #12 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:02:27,351 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwPreviewStartTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:02:27,352 - WARNING - 页签 'FestivalShop' 记录 #13 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:02:27,349 - WARNING - array_match: None , field_type: uint , cell_value: 1
2025-05-06 16:02:27,350 - WARNING - 表格 'DragonBoatRankReward' 记录 #6 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,353 - WARNING - array_match: None , field_type: uint , cell_value: 3
2025-05-06 16:02:27,351 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwPreviewStartTime' 字段的值 '2025-05-06 12:34:56' 不是有效的数值
2025-05-06 16:02:27,353 - WARNING - 表格 'DragonBoatRankReward' 记录 #7 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,352 - WARNING - 页签 'FestivalShop' 记录 #13 行 'dwStartTime' 字段的值 '20250422 06:00' 不是有效的数值
2025-05-06 16:02:27,354 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwStartTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:02:27,353 - WARNING - array_match: None , field_type: uint , cell_value: 3
2025-05-06 16:02:27,355 - WARNING - 页签 'FestivalShop' 记录 #13 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:02:27,353 - WARNING - 表格 'DragonBoatRankReward' 记录 #7 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,356 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,354 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwStartTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:02:27,357 - WARNING - 表格 'DragonBoatRankReward' 记录 #8 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,355 - WARNING - 页签 'FestivalShop' 记录 #13 行 'dwEndTime' 字段的值 '20250422 23:59' 不是有效的数值
2025-05-06 16:02:27,356 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,358 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwEndTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:02:27,357 - WARNING - 表格 'DragonBoatRankReward' 记录 #8 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,359 - WARNING - array_match: None , field_type: uint , cell_value: 3
2025-05-06 16:02:27,358 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwEndTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:02:27,359 - WARNING - 表格 'DragonBoatRankReward' 记录 #9 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,359 - WARNING - array_match: None , field_type: uint , cell_value: 3
2025-05-06 16:02:27,360 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwRewardEndTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:02:27,359 - WARNING - 表格 'DragonBoatRankReward' 记录 #9 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,361 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,362 - WARNING - 表格 'DragonBoatRankReward' 记录 #10 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,360 - WARNING - 页签 'FestivalActController' 记录 #11 行 'dwRewardEndTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:02:27,361 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,363 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwPreviewStartTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:02:27,362 - WARNING - 表格 'DragonBoatRankReward' 记录 #10 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,363 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,363 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwPreviewStartTime' 字段的值 '2025-05-07 12:34:56' 不是有效的数值
2025-05-06 16:02:27,365 - WARNING - 表格 'DragonBoatRankReward' 记录 #11 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,363 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,366 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwStartTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:02:27,365 - WARNING - 表格 'DragonBoatRankReward' 记录 #11 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,366 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,366 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwStartTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:02:27,368 - WARNING - 表格 'DragonBoatRankReward' 记录 #12 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,366 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,369 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwEndTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:02:27,368 - WARNING - 表格 'DragonBoatRankReward' 记录 #12 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,369 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwEndTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:02:27,370 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,371 - WARNING - 表格 'DragonBoatRankReward' 记录 #13 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,370 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,375 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwRewardEndTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:02:27,371 - WARNING - 表格 'DragonBoatRankReward' 记录 #13 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,376 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,375 - WARNING - 页签 'FestivalActController' 记录 #12 行 'dwRewardEndTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:02:27,376 - WARNING - 表格 'DragonBoatRankReward' 记录 #14 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,376 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,377 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwPreviewStartTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:02:27,376 - WARNING - 表格 'DragonBoatRankReward' 记录 #14 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,378 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,377 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwPreviewStartTime' 字段的值 '2025-05-08 12:34:56' 不是有效的数值
2025-05-06 16:02:27,379 - WARNING - 表格 'DragonBoatRankReward' 记录 #15 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,378 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,380 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwStartTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:02:27,379 - WARNING - 表格 'DragonBoatRankReward' 记录 #15 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,381 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,380 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwStartTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:02:27,383 - WARNING - 表格 'DragonBoatRankReward' 记录 #16 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,381 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,384 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwEndTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:02:27,383 - WARNING - 表格 'DragonBoatRankReward' 记录 #16 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,385 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,384 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwEndTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:02:27,385 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,386 - WARNING - 表格 'DragonBoatRankReward' 记录 #17 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,386 - WARNING - 表格 'DragonBoatRankReward' 记录 #17 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,387 - INFO - 页签 FestivalShop 处理完成
2025-05-06 16:02:27,387 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwRewardEndTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:02:27,388 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,387 - INFO - 页签 FestivalShop 处理完成
2025-05-06 16:02:27,389 - WARNING - 表格 'DragonBoatRankReward' 记录 #18 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,387 - WARNING - 页签 'FestivalActController' 记录 #13 行 'dwRewardEndTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:02:27,388 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,391 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwPreviewStartTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:02:27,389 - WARNING - 表格 'DragonBoatRankReward' 记录 #18 字段 'dwPersonalRewards' (int[4][2]) 二维数组行数 2 小于定义的行数 4，将补充空行
2025-05-06 16:02:27,391 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,391 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwPreviewStartTime' 字段的值 '2025-05-09 12:34:56' 不是有效的数值
2025-05-06 16:02:27,391 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,393 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwStartTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:02:27,394 - WARNING - 页签 'DragonBoatGrid' 记录 #8 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,393 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwStartTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:02:27,394 - WARNING - 表格 'FestivalActSignIn' 记录 #1 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,394 - WARNING - 页签 'DragonBoatGrid' 记录 #8 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,395 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwEndTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:02:27,394 - WARNING - 表格 'FestivalActSignIn' 记录 #1 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,396 - INFO - 页签 DragonBoatRankReward 处理完成
2025-05-06 16:02:27,396 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,395 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwEndTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:02:27,397 - WARNING - 表格 'FestivalActSignIn' 记录 #3 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,396 - INFO - 页签 DragonBoatRankReward 处理完成
2025-05-06 16:02:27,398 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwRewardEndTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:02:27,396 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,397 - WARNING - 表格 'FestivalActSignIn' 记录 #3 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,398 - WARNING - 页签 'FestivalActController' 记录 #14 行 'dwRewardEndTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:02:27,400 - WARNING - array_match: None , field_type: uint , cell_value: 4
2025-05-06 16:02:27,400 - WARNING - array_match: None , field_type: uint , cell_value: 4
2025-05-06 16:02:27,400 - WARNING - 表格 'FestivalActSignIn' 记录 #5 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,400 - WARNING - 表格 'FestivalActSignIn' 记录 #5 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,401 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwPreviewStartTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:02:27,402 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,401 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwPreviewStartTime' 字段的值 '2025-05-10 12:34:56' 不是有效的数值
2025-05-06 16:02:27,402 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,403 - WARNING - 表格 'FestivalActSignIn' 记录 #7 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,405 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwStartTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:02:27,403 - WARNING - 表格 'FestivalActSignIn' 记录 #7 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,406 - WARNING - array_match: None , field_type: uint , cell_value: 1
2025-05-06 16:02:27,405 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwStartTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:02:27,407 - WARNING - 表格 'FestivalActSignIn' 记录 #9 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,406 - WARNING - array_match: None , field_type: uint , cell_value: 1
2025-05-06 16:02:27,407 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwEndTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:02:27,407 - WARNING - 表格 'FestivalActSignIn' 记录 #9 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,408 - WARNING - array_match: None , field_type: uint , cell_value: 4
2025-05-06 16:02:27,409 - WARNING - 表格 'FestivalActSignIn' 记录 #11 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,407 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwEndTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:02:27,408 - WARNING - array_match: None , field_type: uint , cell_value: 4
2025-05-06 16:02:27,409 - WARNING - 表格 'FestivalActSignIn' 记录 #11 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,410 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwRewardEndTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:02:27,411 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,410 - WARNING - 页签 'FestivalActController' 记录 #15 行 'dwRewardEndTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:02:27,412 - WARNING - 表格 'FestivalActSignIn' 记录 #13 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,411 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,415 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwPreviewStartTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:02:27,412 - WARNING - 表格 'FestivalActSignIn' 记录 #13 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,415 - WARNING - array_match: None , field_type: uint , cell_value: 4
2025-05-06 16:02:27,415 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwPreviewStartTime' 字段的值 '2025-05-11 12:34:56' 不是有效的数值
2025-05-06 16:02:27,416 - WARNING - 表格 'FestivalActSignIn' 记录 #15 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,415 - WARNING - array_match: None , field_type: uint , cell_value: 4
2025-05-06 16:02:27,417 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwStartTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:02:27,416 - WARNING - 表格 'FestivalActSignIn' 记录 #15 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,418 - INFO - 页签 FestivalQuest 处理完成
2025-05-06 16:02:27,418 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,417 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwStartTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:02:27,419 - WARNING - 表格 'FestivalActSignIn' 记录 #17 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,420 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwEndTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:02:27,418 - INFO - 页签 FestivalQuest 处理完成
2025-05-06 16:02:27,418 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,421 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 I，因为该列缺少类型或字段名
2025-05-06 16:02:27,419 - WARNING - 表格 'FestivalActSignIn' 记录 #17 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,421 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,420 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwEndTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:02:27,422 - WARNING - 表格 'FestivalActSignIn' 记录 #19 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,421 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 I，因为该列缺少类型或字段名
2025-05-06 16:02:27,423 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwRewardEndTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:02:27,421 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,424 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 J，因为该列缺少类型或字段名
2025-05-06 16:02:27,422 - WARNING - 表格 'FestivalActSignIn' 记录 #19 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,425 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,423 - WARNING - 页签 'FestivalActController' 记录 #16 行 'dwRewardEndTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:02:27,424 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 J，因为该列缺少类型或字段名
2025-05-06 16:02:27,426 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwPreviewStartTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:02:27,425 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,427 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 K，因为该列缺少类型或字段名
2025-05-06 16:02:27,426 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwPreviewStartTime' 字段的值 '2025-05-12 12:34:56' 不是有效的数值
2025-05-06 16:02:27,427 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,427 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略列 K，因为该列缺少类型或字段名
2025-05-06 16:02:27,428 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwStartTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:02:27,427 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,429 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #6 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,428 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwStartTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:02:27,430 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,429 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #6 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,431 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwEndTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:02:27,431 - WARNING - 表格 'FestivalActSignIn' 记录 #1 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,430 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,432 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #6 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,431 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwEndTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:02:27,431 - WARNING - 表格 'FestivalActSignIn' 记录 #1 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,433 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,432 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #6 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,434 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwRewardEndTime' 字段的值 '2025-05-15 12:34:56' 不是有效的数值
2025-05-06 16:02:27,433 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,435 - WARNING - 表格 'FestivalActSignIn' 记录 #3 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,434 - WARNING - 页签 'FestivalActController' 记录 #17 行 'dwRewardEndTime' 字段的值 '2025-05-15 12:34:56' 不是有效的数值
2025-05-06 16:02:27,435 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #7 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,435 - WARNING - 表格 'FestivalActSignIn' 记录 #3 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,436 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,437 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwPreviewStartTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:02:27,435 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #7 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,438 - WARNING - 表格 'FestivalActSignIn' 记录 #5 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,436 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,437 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwPreviewStartTime' 字段的值 '2025-05-13 12:34:56' 不是有效的数值
2025-05-06 16:02:27,438 - WARNING - 表格 'FestivalActSignIn' 记录 #5 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,440 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,439 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #7 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,440 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwStartTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:02:27,440 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,441 - WARNING - 表格 'FestivalActSignIn' 记录 #7 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,440 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwStartTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:02:27,442 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #8 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,441 - WARNING - 表格 'FestivalActSignIn' 记录 #7 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,442 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,442 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #8 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,443 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwEndTime' 字段的值 '2025-05-15 12:34:56' 不是有效的数值
2025-05-06 16:02:27,442 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,444 - WARNING - 表格 'FestivalActSignIn' 记录 #9 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,443 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwEndTime' 字段的值 '2025-05-15 12:34:56' 不是有效的数值
2025-05-06 16:02:27,445 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #8 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,444 - WARNING - 表格 'FestivalActSignIn' 记录 #9 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,446 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,445 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #8 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,447 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwRewardEndTime' 字段的值 '2025-05-16 12:34:56' 不是有效的数值
2025-05-06 16:02:27,447 - WARNING - 表格 'FestivalActSignIn' 记录 #11 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,446 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,448 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #9 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,447 - WARNING - 页签 'FestivalActController' 记录 #18 行 'dwRewardEndTime' 字段的值 '2025-05-16 12:34:56' 不是有效的数值
2025-05-06 16:02:27,449 - WARNING - 页签 'DragonBoatGrid' 记录 #9 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,447 - WARNING - 表格 'FestivalActSignIn' 记录 #11 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,450 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwPreviewStartTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:02:27,448 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #9 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,451 - WARNING - 表格 'FestivalActSignIn' 记录 #13 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,449 - WARNING - 页签 'DragonBoatGrid' 记录 #9 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,452 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #9 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,450 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwPreviewStartTime' 字段的值 '2025-05-14 12:34:56' 不是有效的数值
2025-05-06 16:02:27,452 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,451 - WARNING - 表格 'FestivalActSignIn' 记录 #13 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,452 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #9 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,453 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwStartTime' 字段的值 '2025-05-15 12:34:56' 不是有效的数值
2025-05-06 16:02:27,452 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,454 - WARNING - 表格 'FestivalActSignIn' 记录 #15 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,453 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwStartTime' 字段的值 '2025-05-15 12:34:56' 不是有效的数值
2025-05-06 16:02:27,454 - WARNING - 表格 'FestivalActSignIn' 记录 #15 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,455 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #10 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,456 - WARNING - array_match: None , field_type: uint , cell_value: 5
2025-05-06 16:02:27,455 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #10 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,456 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwEndTime' 字段的值 '2025-05-16 12:34:56' 不是有效的数值
2025-05-06 16:02:27,456 - WARNING - array_match: None , field_type: uint , cell_value: 5
2025-05-06 16:02:27,457 - WARNING - 表格 'FestivalActSignIn' 记录 #17 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,456 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwEndTime' 字段的值 '2025-05-16 12:34:56' 不是有效的数值
2025-05-06 16:02:27,457 - WARNING - 表格 'FestivalActSignIn' 记录 #17 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,458 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #10 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,459 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,458 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #10 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,459 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwRewardEndTime' 字段的值 '2025-05-17 12:34:56' 不是有效的数值
2025-05-06 16:02:27,459 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,460 - WARNING - 表格 'FestivalActSignIn' 记录 #19 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,459 - WARNING - 页签 'FestivalActController' 记录 #19 行 'dwRewardEndTime' 字段的值 '2025-05-17 12:34:56' 不是有效的数值
2025-05-06 16:02:27,461 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #11 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,460 - WARNING - 表格 'FestivalActSignIn' 记录 #19 字段 'dwRewards' (uint[2][2]) 二维数组行数 1 小于定义的行数 2，将补充空行
2025-05-06 16:02:27,462 - WARNING - array_match: None , field_type: uint , cell_value: 1
2025-05-06 16:02:27,461 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #11 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,462 - WARNING - array_match: None , field_type: uint , cell_value: 1
2025-05-06 16:02:27,463 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #11 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,463 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #11 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,464 - WARNING - array_match: None , field_type: uint , cell_value: 5
2025-05-06 16:02:27,465 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #12 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,464 - WARNING - array_match: None , field_type: uint , cell_value: 5
2025-05-06 16:02:27,465 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #12 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,466 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,467 - INFO - 页签 FestivalActSignIn 处理完成
2025-05-06 16:02:27,467 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #12 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,466 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,467 - INFO - 页签 FestivalActSignIn 处理完成
2025-05-06 16:02:27,468 - WARNING - array_match: None , field_type: uint , cell_value: 5
2025-05-06 16:02:27,467 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #12 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,468 - WARNING - array_match: None , field_type: uint , cell_value: 5
2025-05-06 16:02:27,470 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #13 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,471 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,470 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #13 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,471 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,472 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #13 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,472 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,472 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #13 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,472 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,473 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #14 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,474 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,473 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #14 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,475 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #14 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,474 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,475 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #14 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,477 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,477 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #15 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,477 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,477 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #15 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,480 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,481 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #15 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,480 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,481 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #15 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,482 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,482 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,483 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #16 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,483 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #16 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,484 - INFO - 页签 FestivalActController 处理完成
2025-05-06 16:02:27,484 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,484 - INFO - 页签 FestivalActController 处理完成
2025-05-06 16:02:27,484 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #16 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,484 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,484 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #16 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,486 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,486 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #17 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,486 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,486 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #17 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,487 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,488 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #17 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,487 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,488 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #17 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,489 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,489 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #18 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,489 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,489 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #18 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,490 - WARNING - 页签 'DragonBoatGrid' 记录 #10 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,491 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #18 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,490 - WARNING - 页签 'DragonBoatGrid' 记录 #10 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,491 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #18 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,492 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,492 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #19 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,492 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,492 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #19 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,493 - WARNING - array_match: None , field_type: uint , cell_value: 6
2025-05-06 16:02:27,494 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #19 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,493 - WARNING - array_match: None , field_type: uint , cell_value: 6
2025-05-06 16:02:27,494 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #19 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,495 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,495 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #20 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,495 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,495 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #20 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,496 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,497 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #20 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,496 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,497 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #20 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,498 - WARNING - array_match: None , field_type: uint , cell_value: 6
2025-05-06 16:02:27,498 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #21 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,498 - WARNING - array_match: None , field_type: uint , cell_value: 6
2025-05-06 16:02:27,498 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #21 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,499 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,500 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #21 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,499 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,500 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #21 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,501 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,502 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #22 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,501 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,502 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #22 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,502 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,503 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #22 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,502 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,503 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #22 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,504 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,504 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #23 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,504 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,504 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #23 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,505 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,506 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #23 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,505 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,506 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #23 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,507 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,507 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #24 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,507 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,507 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #24 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,508 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,509 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #24 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,508 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,509 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #24 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,510 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,510 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #25 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,510 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,510 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #25 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,511 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,512 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #25 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,511 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,512 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #25 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,513 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,513 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #26 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,513 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,513 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #26 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,514 - WARNING - 页签 'DragonBoatGrid' 记录 #11 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,514 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #26 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,514 - WARNING - 页签 'DragonBoatGrid' 记录 #11 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,514 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #26 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,516 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,516 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #27 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,516 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,516 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #27 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,517 - WARNING - array_match: None , field_type: uint , cell_value: 7
2025-05-06 16:02:27,518 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #27 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,517 - WARNING - array_match: None , field_type: uint , cell_value: 7
2025-05-06 16:02:27,518 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #27 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,518 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,519 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #28 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,518 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,519 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #28 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,520 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,520 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #28 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,520 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,520 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #28 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,521 - WARNING - array_match: None , field_type: uint , cell_value: 7
2025-05-06 16:02:27,522 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #29 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,521 - WARNING - array_match: None , field_type: uint , cell_value: 7
2025-05-06 16:02:27,522 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #29 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,523 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,523 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #29 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,523 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,523 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #29 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,524 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,525 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #30 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,524 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,525 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #30 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,526 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,527 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #30 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,526 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,527 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #30 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,527 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,528 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #31 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,527 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,528 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #31 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,529 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,529 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #31 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,529 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,529 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #31 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,530 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,531 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #32 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,530 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,531 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #32 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,532 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,532 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #32 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,532 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,532 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #32 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,533 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,534 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #33 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,533 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,534 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #33 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,535 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,535 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #33 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,535 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,535 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #33 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,536 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,537 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #34 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,536 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,537 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #34 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,538 - WARNING - 页签 'DragonBoatGrid' 记录 #12 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,538 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #34 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,538 - WARNING - 页签 'DragonBoatGrid' 记录 #12 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,538 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #34 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,539 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,540 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #35 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,539 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,540 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #35 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,541 - WARNING - array_match: None , field_type: uint , cell_value: 8
2025-05-06 16:02:27,541 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #35 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,541 - WARNING - array_match: None , field_type: uint , cell_value: 8
2025-05-06 16:02:27,541 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #35 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,542 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,542 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #36 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,542 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,542 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #36 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,543 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,544 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #36 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,543 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,544 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #36 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,545 - WARNING - array_match: None , field_type: uint , cell_value: 8
2025-05-06 16:02:27,545 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #37 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,545 - WARNING - array_match: None , field_type: uint , cell_value: 8
2025-05-06 16:02:27,545 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #37 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,546 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,547 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #37 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,546 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,547 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #37 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,547 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,548 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #38 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,547 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,548 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #38 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,549 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,550 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #38 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,549 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,550 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #38 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,550 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,551 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #39 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,550 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,551 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #39 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,552 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,553 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #39 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,552 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,553 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #39 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,553 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,554 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #40 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,553 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,554 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #40 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,555 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,556 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #40 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,555 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,556 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #40 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,557 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,557 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #41 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,557 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,557 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #41 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,558 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,559 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #41 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,558 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,559 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #41 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,559 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,560 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #42 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,559 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,560 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #42 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,561 - WARNING - 页签 'DragonBoatGrid' 记录 #13 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,562 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #42 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,561 - WARNING - 页签 'DragonBoatGrid' 记录 #13 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,562 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #42 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,562 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,563 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #43 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,562 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,563 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #43 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,564 - WARNING - array_match: None , field_type: uint , cell_value: 9
2025-05-06 16:02:27,565 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #43 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,564 - WARNING - array_match: None , field_type: uint , cell_value: 9
2025-05-06 16:02:27,565 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #43 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,565 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,566 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #44 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,565 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,566 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #44 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,567 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,568 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #44 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,567 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,568 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #44 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,568 - WARNING - array_match: None , field_type: uint , cell_value: 9
2025-05-06 16:02:27,569 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #45 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,568 - WARNING - array_match: None , field_type: uint , cell_value: 9
2025-05-06 16:02:27,569 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #45 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,570 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,571 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #45 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,570 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,571 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #45 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,572 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,572 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #46 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,572 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,572 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #46 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,573 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,573 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,574 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #46 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,574 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #46 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,575 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,575 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,575 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #47 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,575 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #47 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,576 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,576 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,577 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #47 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,578 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,577 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #47 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,578 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,578 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #48 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,579 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,578 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #48 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,579 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,580 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #48 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,581 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,580 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #48 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,581 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,582 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #49 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,582 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,582 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #49 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,582 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,583 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #49 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,584 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,583 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #49 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,584 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,585 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #50 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,586 - WARNING - 页签 'DragonBoatGrid' 记录 #14 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,585 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #50 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,586 - WARNING - 页签 'DragonBoatGrid' 记录 #14 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,587 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #50 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,588 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,587 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #50 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,588 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,588 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #51 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,589 - WARNING - array_match: None , field_type: uint , cell_value: 10
2025-05-06 16:02:27,588 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #51 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,589 - WARNING - array_match: None , field_type: uint , cell_value: 10
2025-05-06 16:02:27,590 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #51 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,591 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,590 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #51 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,591 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,592 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #52 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,593 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,592 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #52 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,593 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,594 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #52 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,595 - WARNING - array_match: None , field_type: uint , cell_value: 10
2025-05-06 16:02:27,594 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #52 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,595 - WARNING - array_match: None , field_type: uint , cell_value: 10
2025-05-06 16:02:27,596 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #53 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,597 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,596 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #53 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,597 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,598 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #53 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,599 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,598 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #53 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,599 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,600 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #54 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,601 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,600 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #54 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,601 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,602 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #54 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,602 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,602 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #54 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,602 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,603 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #55 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,604 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,603 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #55 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,604 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,605 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #55 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,605 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,605 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #55 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,605 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,606 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #56 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,607 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,606 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #56 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,607 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,608 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #56 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,609 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,608 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #56 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,609 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,610 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #57 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,610 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,610 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #57 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,610 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,611 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #57 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,612 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,611 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #57 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,612 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,613 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #58 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,613 - WARNING - 页签 'DragonBoatGrid' 记录 #15 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,613 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #58 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,613 - WARNING - 页签 'DragonBoatGrid' 记录 #15 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,614 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #58 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,615 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,614 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #58 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,615 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,616 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #59 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,616 - WARNING - array_match: None , field_type: uint , cell_value: 11
2025-05-06 16:02:27,616 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #59 行 'dwReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,616 - WARNING - array_match: None , field_type: uint , cell_value: 11
2025-05-06 16:02:27,617 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #59 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,618 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,617 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #59 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,618 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,619 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #60 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,619 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,619 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #60 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,619 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,620 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #60 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,621 - WARNING - array_match: None , field_type: uint , cell_value: 11
2025-05-06 16:02:27,620 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #60 行 'dwPayCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,621 - WARNING - array_match: None , field_type: uint , cell_value: 11
2025-05-06 16:02:27,622 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #61 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,623 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,622 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #61 行 'dwCurrency' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,623 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,623 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #61 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,624 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,623 - WARNING - 页签 'FestivalActWarOrderLevel' 记录 #61 行 'dwPayReward' 字段的第 1 个元素 '' 不是有效的数值
2025-05-06 16:02:27,624 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,625 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,625 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,626 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,626 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,627 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,627 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,628 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,628 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,631 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,631 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,631 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,631 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,632 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,632 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,633 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,633 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,633 - WARNING - 页签 'DragonBoatGrid' 记录 #16 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,633 - WARNING - 页签 'DragonBoatGrid' 记录 #16 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,634 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,634 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,635 - WARNING - array_match: None , field_type: uint , cell_value: 12
2025-05-06 16:02:27,635 - WARNING - array_match: None , field_type: uint , cell_value: 12
2025-05-06 16:02:27,636 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,636 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,640 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,640 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,641 - WARNING - array_match: None , field_type: uint , cell_value: 12
2025-05-06 16:02:27,641 - WARNING - array_match: None , field_type: uint , cell_value: 12
2025-05-06 16:02:27,642 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,642 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,643 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,643 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,643 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,643 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,644 - INFO - 页签 FestivalActWarOrderLevel 处理完成
2025-05-06 16:02:27,644 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,644 - INFO - 页签 FestivalActWarOrderLevel 处理完成
2025-05-06 16:02:27,644 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,645 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,645 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,646 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,646 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,647 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,647 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,647 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,647 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,648 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,648 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,649 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,649 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,649 - WARNING - 页签 'DragonBoatGrid' 记录 #17 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,649 - WARNING - 页签 'DragonBoatGrid' 记录 #17 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,650 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,650 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,651 - WARNING - array_match: None , field_type: uint , cell_value: 13
2025-05-06 16:02:27,651 - WARNING - array_match: None , field_type: uint , cell_value: 13
2025-05-06 16:02:27,652 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,652 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,652 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,652 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,653 - WARNING - array_match: None , field_type: uint , cell_value: 13
2025-05-06 16:02:27,653 - WARNING - array_match: None , field_type: uint , cell_value: 13
2025-05-06 16:02:27,653 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,653 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,654 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,654 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,655 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,655 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,655 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,655 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,656 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,656 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,657 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,657 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,658 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,658 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,658 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,658 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,659 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,659 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,660 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,660 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,660 - WARNING - 页签 'DragonBoatGrid' 记录 #18 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,660 - WARNING - 页签 'DragonBoatGrid' 记录 #18 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,661 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,661 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,662 - WARNING - array_match: None , field_type: uint , cell_value: 14
2025-05-06 16:02:27,662 - WARNING - array_match: None , field_type: uint , cell_value: 14
2025-05-06 16:02:27,662 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,662 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,663 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,663 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,663 - WARNING - array_match: None , field_type: uint , cell_value: 14
2025-05-06 16:02:27,663 - WARNING - array_match: None , field_type: uint , cell_value: 14
2025-05-06 16:02:27,664 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,664 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,665 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,665 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,666 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,666 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,666 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,666 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,667 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,667 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,668 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,668 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,668 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,668 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,669 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,669 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,670 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,670 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,670 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,670 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,671 - WARNING - 页签 'DragonBoatGrid' 记录 #19 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,671 - WARNING - 页签 'DragonBoatGrid' 记录 #19 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,672 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,672 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,673 - WARNING - array_match: None , field_type: uint , cell_value: 15
2025-05-06 16:02:27,673 - WARNING - array_match: None , field_type: uint , cell_value: 15
2025-05-06 16:02:27,673 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,673 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,674 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,674 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,674 - WARNING - array_match: None , field_type: uint , cell_value: 15
2025-05-06 16:02:27,674 - WARNING - array_match: None , field_type: uint , cell_value: 15
2025-05-06 16:02:27,675 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,675 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,676 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,676 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,676 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,676 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,677 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,677 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,678 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,678 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,678 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,678 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,679 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,679 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,680 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,680 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,681 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,681 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,682 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,682 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,682 - WARNING - 页签 'DragonBoatGrid' 记录 #20 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,682 - WARNING - 页签 'DragonBoatGrid' 记录 #20 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,683 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,683 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,683 - WARNING - array_match: None , field_type: uint , cell_value: 16
2025-05-06 16:02:27,683 - WARNING - array_match: None , field_type: uint , cell_value: 16
2025-05-06 16:02:27,684 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,684 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,685 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,685 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,685 - WARNING - array_match: None , field_type: uint , cell_value: 16
2025-05-06 16:02:27,685 - WARNING - array_match: None , field_type: uint , cell_value: 16
2025-05-06 16:02:27,686 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,686 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,687 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,687 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,687 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,687 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,688 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,688 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,689 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,689 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,690 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,690 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,690 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,690 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,691 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,691 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,692 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,692 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,692 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,692 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,693 - WARNING - 页签 'DragonBoatGrid' 记录 #21 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,693 - WARNING - 页签 'DragonBoatGrid' 记录 #21 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,693 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,693 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,694 - WARNING - array_match: None , field_type: uint , cell_value: 17
2025-05-06 16:02:27,694 - WARNING - array_match: None , field_type: uint , cell_value: 17
2025-05-06 16:02:27,695 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,695 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,696 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,696 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,696 - WARNING - array_match: None , field_type: uint , cell_value: 17
2025-05-06 16:02:27,696 - WARNING - array_match: None , field_type: uint , cell_value: 17
2025-05-06 16:02:27,697 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,697 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,698 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,698 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,698 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,698 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,699 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,699 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,699 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,699 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,700 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,700 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,701 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,701 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,701 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,701 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,702 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,702 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,703 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,703 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,703 - WARNING - 页签 'DragonBoatGrid' 记录 #22 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,703 - WARNING - 页签 'DragonBoatGrid' 记录 #22 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,704 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,704 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,705 - WARNING - array_match: None , field_type: uint , cell_value: 18
2025-05-06 16:02:27,705 - WARNING - array_match: None , field_type: uint , cell_value: 18
2025-05-06 16:02:27,706 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,706 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,706 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,706 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,707 - WARNING - array_match: None , field_type: uint , cell_value: 18
2025-05-06 16:02:27,707 - WARNING - array_match: None , field_type: uint , cell_value: 18
2025-05-06 16:02:27,708 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,708 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,708 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,708 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,709 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,709 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,710 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,710 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,711 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,711 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,711 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,711 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,712 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,712 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,712 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,712 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,713 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,713 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,714 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,714 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,714 - WARNING - 页签 'DragonBoatGrid' 记录 #23 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,714 - WARNING - 页签 'DragonBoatGrid' 记录 #23 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,715 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,715 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,716 - WARNING - array_match: None , field_type: uint , cell_value: 19
2025-05-06 16:02:27,716 - WARNING - array_match: None , field_type: uint , cell_value: 19
2025-05-06 16:02:27,717 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,717 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,717 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,717 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,718 - WARNING - array_match: None , field_type: uint , cell_value: 19
2025-05-06 16:02:27,718 - WARNING - array_match: None , field_type: uint , cell_value: 19
2025-05-06 16:02:27,719 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,719 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,719 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,719 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,720 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,720 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,721 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,721 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,722 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,722 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,722 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,722 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,723 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,723 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,724 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,724 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,724 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,724 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,725 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,725 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,726 - WARNING - 页签 'DragonBoatGrid' 记录 #24 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,726 - WARNING - 页签 'DragonBoatGrid' 记录 #24 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,727 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,727 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,728 - WARNING - array_match: None , field_type: uint , cell_value: 20
2025-05-06 16:02:27,728 - WARNING - array_match: None , field_type: uint , cell_value: 20
2025-05-06 16:02:27,728 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,728 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,729 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,729 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,730 - WARNING - array_match: None , field_type: uint , cell_value: 20
2025-05-06 16:02:27,730 - WARNING - array_match: None , field_type: uint , cell_value: 20
2025-05-06 16:02:27,730 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,730 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,731 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,731 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,732 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,732 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,732 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,732 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,733 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,733 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,733 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,733 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,734 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,734 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,735 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,735 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,736 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,736 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,736 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,736 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,737 - WARNING - 页签 'DragonBoatGrid' 记录 #25 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,737 - WARNING - 页签 'DragonBoatGrid' 记录 #25 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,738 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,738 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,738 - WARNING - array_match: None , field_type: uint , cell_value: 21
2025-05-06 16:02:27,738 - WARNING - array_match: None , field_type: uint , cell_value: 21
2025-05-06 16:02:27,739 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,739 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,740 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,740 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,740 - WARNING - array_match: None , field_type: uint , cell_value: 21
2025-05-06 16:02:27,740 - WARNING - array_match: None , field_type: uint , cell_value: 21
2025-05-06 16:02:27,741 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,741 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,741 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,741 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,742 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,742 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,742 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,742 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,743 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,743 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,743 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,743 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,744 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,744 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,745 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,745 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,746 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,746 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,746 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,746 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,747 - WARNING - 页签 'DragonBoatGrid' 记录 #26 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,747 - WARNING - 页签 'DragonBoatGrid' 记录 #26 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,748 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,748 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,748 - WARNING - array_match: None , field_type: uint , cell_value: 22
2025-05-06 16:02:27,748 - WARNING - array_match: None , field_type: uint , cell_value: 22
2025-05-06 16:02:27,749 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,749 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,750 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,750 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,750 - WARNING - array_match: None , field_type: uint , cell_value: 22
2025-05-06 16:02:27,750 - WARNING - array_match: None , field_type: uint , cell_value: 22
2025-05-06 16:02:27,751 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,751 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,751 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,751 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,752 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,752 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,753 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,753 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,753 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,753 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,754 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,754 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,755 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,755 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,755 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,755 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,756 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,756 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,757 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,757 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,758 - WARNING - 页签 'DragonBoatGrid' 记录 #27 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,758 - WARNING - 页签 'DragonBoatGrid' 记录 #27 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,758 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,758 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,759 - WARNING - array_match: None , field_type: uint , cell_value: 23
2025-05-06 16:02:27,759 - WARNING - array_match: None , field_type: uint , cell_value: 23
2025-05-06 16:02:27,760 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,760 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,760 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,760 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,761 - WARNING - array_match: None , field_type: uint , cell_value: 23
2025-05-06 16:02:27,761 - WARNING - array_match: None , field_type: uint , cell_value: 23
2025-05-06 16:02:27,761 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,761 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,762 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,762 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,763 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,763 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,763 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,763 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,764 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,764 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,764 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,764 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,765 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,765 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,766 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,766 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,767 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,767 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,768 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,768 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,768 - WARNING - 页签 'DragonBoatGrid' 记录 #28 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,768 - WARNING - 页签 'DragonBoatGrid' 记录 #28 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,769 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,769 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,769 - WARNING - array_match: None , field_type: uint , cell_value: 24
2025-05-06 16:02:27,769 - WARNING - array_match: None , field_type: uint , cell_value: 24
2025-05-06 16:02:27,770 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,770 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,771 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,771 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,771 - WARNING - array_match: None , field_type: uint , cell_value: 24
2025-05-06 16:02:27,771 - WARNING - array_match: None , field_type: uint , cell_value: 24
2025-05-06 16:02:27,772 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,772 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,772 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,772 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,773 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,773 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,774 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,774 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,775 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,775 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,775 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,775 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,776 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,776 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,777 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,777 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,777 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,777 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,778 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,778 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,779 - WARNING - 页签 'DragonBoatGrid' 记录 #29 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,779 - WARNING - 页签 'DragonBoatGrid' 记录 #29 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,780 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,780 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,780 - WARNING - array_match: None , field_type: uint , cell_value: 25
2025-05-06 16:02:27,780 - WARNING - array_match: None , field_type: uint , cell_value: 25
2025-05-06 16:02:27,781 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,781 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,782 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,782 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,782 - WARNING - array_match: None , field_type: uint , cell_value: 25
2025-05-06 16:02:27,782 - WARNING - array_match: None , field_type: uint , cell_value: 25
2025-05-06 16:02:27,783 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,783 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,784 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,784 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,784 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,784 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,785 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,785 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,786 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,786 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,787 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,787 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,788 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,788 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,788 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,788 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,789 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,789 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,790 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,790 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,790 - WARNING - 页签 'DragonBoatGrid' 记录 #30 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,790 - WARNING - 页签 'DragonBoatGrid' 记录 #30 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,791 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,791 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,792 - WARNING - array_match: None , field_type: uint , cell_value: 26
2025-05-06 16:02:27,792 - WARNING - array_match: None , field_type: uint , cell_value: 26
2025-05-06 16:02:27,792 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,792 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,793 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,793 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,794 - WARNING - array_match: None , field_type: uint , cell_value: 26
2025-05-06 16:02:27,794 - WARNING - array_match: None , field_type: uint , cell_value: 26
2025-05-06 16:02:27,794 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,794 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,795 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,795 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,796 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,796 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,796 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,796 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,797 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,797 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,798 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,798 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,798 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,798 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,799 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,799 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,800 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,800 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,801 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,801 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,801 - WARNING - 页签 'DragonBoatGrid' 记录 #31 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,801 - WARNING - 页签 'DragonBoatGrid' 记录 #31 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,802 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,802 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,802 - WARNING - array_match: None , field_type: uint , cell_value: 27
2025-05-06 16:02:27,802 - WARNING - array_match: None , field_type: uint , cell_value: 27
2025-05-06 16:02:27,803 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,803 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,803 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,803 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,804 - WARNING - array_match: None , field_type: uint , cell_value: 27
2025-05-06 16:02:27,804 - WARNING - array_match: None , field_type: uint , cell_value: 27
2025-05-06 16:02:27,805 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,805 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,806 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,806 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,806 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,806 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,807 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,807 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,807 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,807 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,808 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,808 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,809 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,809 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,810 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,810 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,810 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,810 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,811 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,811 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,812 - WARNING - 页签 'DragonBoatGrid' 记录 #32 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,812 - WARNING - 页签 'DragonBoatGrid' 记录 #32 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,812 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,812 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,813 - WARNING - array_match: None , field_type: uint , cell_value: 28
2025-05-06 16:02:27,813 - WARNING - array_match: None , field_type: uint , cell_value: 28
2025-05-06 16:02:27,814 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,814 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,814 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,814 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,815 - WARNING - array_match: None , field_type: uint , cell_value: 28
2025-05-06 16:02:27,815 - WARNING - array_match: None , field_type: uint , cell_value: 28
2025-05-06 16:02:27,816 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,816 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,816 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,816 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,817 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,817 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,817 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,817 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,818 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,818 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,819 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,819 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,820 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,820 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,820 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,820 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,821 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,821 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,822 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,822 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,822 - WARNING - 页签 'DragonBoatGrid' 记录 #33 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,822 - WARNING - 页签 'DragonBoatGrid' 记录 #33 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,823 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,823 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,824 - WARNING - array_match: None , field_type: uint , cell_value: 29
2025-05-06 16:02:27,824 - WARNING - array_match: None , field_type: uint , cell_value: 29
2025-05-06 16:02:27,824 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,824 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,825 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,825 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,826 - WARNING - array_match: None , field_type: uint , cell_value: 29
2025-05-06 16:02:27,826 - WARNING - array_match: None , field_type: uint , cell_value: 29
2025-05-06 16:02:27,826 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,826 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,827 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,827 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,828 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,828 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,828 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,828 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,829 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,829 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,830 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,830 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,830 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,830 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,831 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,831 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,831 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,831 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,832 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,832 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,833 - WARNING - 页签 'DragonBoatGrid' 记录 #34 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,833 - WARNING - 页签 'DragonBoatGrid' 记录 #34 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,833 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,833 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,834 - WARNING - array_match: None , field_type: uint , cell_value: 30
2025-05-06 16:02:27,834 - WARNING - array_match: None , field_type: uint , cell_value: 30
2025-05-06 16:02:27,835 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,835 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,836 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,836 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,836 - WARNING - array_match: None , field_type: uint , cell_value: 30
2025-05-06 16:02:27,836 - WARNING - array_match: None , field_type: uint , cell_value: 30
2025-05-06 16:02:27,837 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,837 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,838 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,838 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,838 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,838 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,839 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,839 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,840 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,840 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,840 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,840 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,841 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,841 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,842 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,842 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,842 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,842 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,843 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,843 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,843 - WARNING - 页签 'DragonBoatGrid' 记录 #35 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,843 - WARNING - 页签 'DragonBoatGrid' 记录 #35 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,844 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,844 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,845 - WARNING - array_match: None , field_type: uint , cell_value: 31
2025-05-06 16:02:27,845 - WARNING - array_match: None , field_type: uint , cell_value: 31
2025-05-06 16:02:27,846 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,846 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,847 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,847 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,847 - WARNING - array_match: None , field_type: uint , cell_value: 31
2025-05-06 16:02:27,847 - WARNING - array_match: None , field_type: uint , cell_value: 31
2025-05-06 16:02:27,848 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,848 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,848 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,848 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,849 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,849 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,849 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,849 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,850 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,850 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,851 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,851 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,852 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,852 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,852 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,852 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,853 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,853 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,853 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,853 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,854 - WARNING - 页签 'DragonBoatGrid' 记录 #36 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,854 - WARNING - 页签 'DragonBoatGrid' 记录 #36 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,855 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,855 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,856 - WARNING - array_match: None , field_type: uint , cell_value: 32
2025-05-06 16:02:27,856 - WARNING - array_match: None , field_type: uint , cell_value: 32
2025-05-06 16:02:27,856 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,856 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,857 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,857 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,858 - WARNING - array_match: None , field_type: uint , cell_value: 32
2025-05-06 16:02:27,858 - WARNING - array_match: None , field_type: uint , cell_value: 32
2025-05-06 16:02:27,858 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,858 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,859 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,859 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,859 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,859 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,860 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,860 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,860 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,860 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,861 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,861 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,862 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,862 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,862 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,862 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,863 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,863 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,863 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,863 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,864 - WARNING - 页签 'DragonBoatGrid' 记录 #37 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,864 - WARNING - 页签 'DragonBoatGrid' 记录 #37 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,865 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,865 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,865 - WARNING - array_match: None , field_type: uint , cell_value: 33
2025-05-06 16:02:27,865 - WARNING - array_match: None , field_type: uint , cell_value: 33
2025-05-06 16:02:27,866 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,866 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,867 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,867 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,867 - WARNING - array_match: None , field_type: uint , cell_value: 33
2025-05-06 16:02:27,867 - WARNING - array_match: None , field_type: uint , cell_value: 33
2025-05-06 16:02:27,868 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,868 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,868 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,868 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,869 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,869 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,870 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,870 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,870 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,870 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,871 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,871 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,872 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,872 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,872 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,872 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,873 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,873 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,873 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,873 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,874 - WARNING - 页签 'DragonBoatGrid' 记录 #38 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,874 - WARNING - 页签 'DragonBoatGrid' 记录 #38 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,875 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,875 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,875 - WARNING - array_match: None , field_type: uint , cell_value: 34
2025-05-06 16:02:27,875 - WARNING - array_match: None , field_type: uint , cell_value: 34
2025-05-06 16:02:27,876 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,876 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,877 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,877 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,877 - WARNING - array_match: None , field_type: uint , cell_value: 34
2025-05-06 16:02:27,877 - WARNING - array_match: None , field_type: uint , cell_value: 34
2025-05-06 16:02:27,878 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,878 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,878 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,878 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,879 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,879 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,880 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,880 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,880 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,880 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,881 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,881 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,881 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,881 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,882 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,882 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,883 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,883 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,883 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,883 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,884 - WARNING - 页签 'DragonBoatGrid' 记录 #39 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,884 - WARNING - 页签 'DragonBoatGrid' 记录 #39 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,885 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,885 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,886 - WARNING - array_match: None , field_type: uint , cell_value: 35
2025-05-06 16:02:27,886 - WARNING - array_match: None , field_type: uint , cell_value: 35
2025-05-06 16:02:27,886 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,886 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,887 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,887 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,887 - WARNING - array_match: None , field_type: uint , cell_value: 35
2025-05-06 16:02:27,887 - WARNING - array_match: None , field_type: uint , cell_value: 35
2025-05-06 16:02:27,888 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,888 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,888 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,888 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,889 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,889 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,889 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,889 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,890 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,890 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,891 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,891 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,891 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,891 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,892 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,892 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,893 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,893 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,893 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,893 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,894 - WARNING - 页签 'DragonBoatGrid' 记录 #40 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,894 - WARNING - 页签 'DragonBoatGrid' 记录 #40 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,894 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,894 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,895 - WARNING - array_match: None , field_type: uint , cell_value: 36
2025-05-06 16:02:27,895 - WARNING - array_match: None , field_type: uint , cell_value: 36
2025-05-06 16:02:27,896 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,896 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,896 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,896 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,897 - WARNING - array_match: None , field_type: uint , cell_value: 36
2025-05-06 16:02:27,897 - WARNING - array_match: None , field_type: uint , cell_value: 36
2025-05-06 16:02:27,898 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,898 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,898 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,898 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,899 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,899 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,900 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,900 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,901 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,901 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,901 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,901 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,902 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,902 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,903 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,903 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,903 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,903 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,904 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,904 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,905 - WARNING - 页签 'DragonBoatGrid' 记录 #41 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,905 - WARNING - 页签 'DragonBoatGrid' 记录 #41 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,905 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,905 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,906 - WARNING - array_match: None , field_type: uint , cell_value: 37
2025-05-06 16:02:27,906 - WARNING - array_match: None , field_type: uint , cell_value: 37
2025-05-06 16:02:27,907 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,907 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,907 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,907 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,908 - WARNING - array_match: None , field_type: uint , cell_value: 37
2025-05-06 16:02:27,908 - WARNING - array_match: None , field_type: uint , cell_value: 37
2025-05-06 16:02:27,909 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,909 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,909 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,909 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,910 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,910 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,911 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,911 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,911 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,911 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,912 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,912 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,912 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,912 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,4000,3000]
2025-05-06 16:02:27,913 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,913 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,913 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,913 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,914 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,914 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,915 - WARNING - 页签 'DragonBoatGrid' 记录 #42 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,915 - WARNING - 页签 'DragonBoatGrid' 记录 #42 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,916 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,916 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,917 - WARNING - array_match: None , field_type: uint , cell_value: 38
2025-05-06 16:02:27,917 - WARNING - array_match: None , field_type: uint , cell_value: 38
2025-05-06 16:02:27,917 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,917 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,918 - WARNING - array_match: None , field_type: byte , cell_value: 1
2025-05-06 16:02:27,918 - WARNING - array_match: None , field_type: byte , cell_value: 1
2025-05-06 16:02:27,918 - WARNING - array_match: None , field_type: uint , cell_value: 38
2025-05-06 16:02:27,918 - WARNING - array_match: None , field_type: uint , cell_value: 38
2025-05-06 16:02:27,919 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,919 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,920 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,920 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,920 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,920 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,921 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,921 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,922 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,922 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,922 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,922 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,923 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,923 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,2000,5000]
2025-05-06 16:02:27,924 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,924 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,924 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,924 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,925 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,925 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,926 - WARNING - 页签 'DragonBoatGrid' 记录 #43 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,926 - WARNING - 页签 'DragonBoatGrid' 记录 #43 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,926 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,926 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,927 - WARNING - array_match: None , field_type: uint , cell_value: 39
2025-05-06 16:02:27,927 - WARNING - array_match: None , field_type: uint , cell_value: 39
2025-05-06 16:02:27,927 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,927 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,928 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,928 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,928 - WARNING - array_match: None , field_type: uint , cell_value: 39
2025-05-06 16:02:27,928 - WARNING - array_match: None , field_type: uint , cell_value: 39
2025-05-06 16:02:27,929 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,929 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,930 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,930 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,931 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,931 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,931 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,931 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,932 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,932 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,932 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,932 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,933 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,933 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [4000,3000,3000]
2025-05-06 16:02:27,934 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,934 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,934 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,934 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,935 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,935 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,936 - WARNING - 页签 'DragonBoatGrid' 记录 #44 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,936 - WARNING - 页签 'DragonBoatGrid' 记录 #44 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,936 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,936 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,937 - WARNING - array_match: None , field_type: uint , cell_value: 40
2025-05-06 16:02:27,937 - WARNING - array_match: None , field_type: uint , cell_value: 40
2025-05-06 16:02:27,938 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,938 - WARNING - array_match: None , field_type: uint , cell_value: 1005
2025-05-06 16:02:27,938 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,938 - WARNING - array_match: None , field_type: byte , cell_value: 2
2025-05-06 16:02:27,939 - WARNING - array_match: None , field_type: uint , cell_value: 40
2025-05-06 16:02:27,939 - WARNING - array_match: None , field_type: uint , cell_value: 40
2025-05-06 16:02:27,940 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,940 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,940 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,940 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,941 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,941 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [10001,10002,10003]
2025-05-06 16:02:27,941 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,941 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,942 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,942 - WARNING - array_match: None , field_type: uint , cell_value: 3000
2025-05-06 16:02:27,943 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,943 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [20001,20002,20003]
2025-05-06 16:02:27,943 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,943 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [3000,5000,2000]
2025-05-06 16:02:27,944 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,944 - WARNING - array_match: None , field_type: uint , cell_value: 1000
2025-05-06 16:02:27,945 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,945 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [40001,40002,40003]
2025-05-06 16:02:27,946 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,946 - WARNING - array_match: None , field_type: uint , cell_value: [30001,30002,30003]
2025-05-06 16:02:27,947 - WARNING - 页签 'DragonBoatGrid' 记录 #45 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,947 - WARNING - 页签 'DragonBoatGrid' 记录 #45 行 'dwRewardEventIds' 字段的值 '[30001,30002,30003]' 不是有效的数值
2025-05-06 16:02:27,947 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,947 - WARNING - array_match: <re.Match object; span=(0, 8), match='uint[10]'> , field_type: uint[10] , cell_value: [1000,2000,3000]
2025-05-06 16:02:27,972 - INFO - 页签 DragonBoatGrid 处理完成
2025-05-06 16:02:27,972 - INFO - 页签 DragonBoatGrid 处理完成
2025-05-06 16:02:28,152 - INFO - 导出完成! 成功: 1, 跳过: 0, 失败: 0
2025-05-06 16:02:28,152 - INFO - 导出完成! 成功: 1, 跳过: 0, 失败: 0
