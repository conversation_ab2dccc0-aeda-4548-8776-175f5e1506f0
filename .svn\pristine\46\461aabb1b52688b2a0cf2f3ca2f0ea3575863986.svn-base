struct stFestivalActControllerClientCfg
{
    DWORD    dwId;                  // 分活动id
    DWORD    dwActId;               // 归属主活动id
    char     szActName[128];             // 活动名称
按钮名称
    DWORD    dwPreviewStartTime;    // 预告开始时间
    DWORD    dwStartTime;           // 活动开始时间
    DWORD    dwEndTime;             // 结束截止时间
    DWORD    dwRewardEndTime;       // 领奖截止时间
    BYTE     byActType;             // 活动类型
    BYTE     bySort;                // 页签排序
    char     szImage[128];               // 资源底图
    char     szEntryIcon[128];           // 界面功能栏入口icon
    DWORD    dwCurrency;            // 货币
    DWORD    dwStamps;              // 点券消耗
};
