#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import struct
import sys
import binascii

def hex_dump(data, offset=0, length=None, width=16):
    """将二进制数据转换为十六进制格式显示"""
    if length is None:
        length = len(data)
    result = []
    for i in range(0, length, width):
        chunk = data[i:i+width]
        hex_data = ' '.join(f"{b:02x}" for b in chunk)
        ascii_data = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
        result.append(f"{offset+i:08x}:  {hex_data:<{width*3}}  {ascii_data}")
    return '\n'.join(result)

class BinaryDebugger:
    def __init__(self, file_path):
        """初始化二进制调试器"""
        self.file_path = file_path
    
    def dump_char_array(self, offset, size, encoding='utf-8'):
        """
        读取并显示指定位置的char数组原始数据
        
        Args:
            offset: 字段在文件中的偏移量
            size: 字段大小（字节数）
            encoding: 尝试解码的编码方式
        """
        try:
            with open(self.file_path, 'rb') as f:
                # 跳转到指定位置
                f.seek(offset)
                
                # 读取指定大小的数据
                data = f.read(size)
                
                print(f"字段大小: {size} 字节")
                print(f"读取到的数据大小: {len(data)} 字节")
                
                # 显示十六进制和ASCII表示
                print("十六进制表示:")
                print(hex_dump(data))
                
                # 查找第一个空字节的位置
                try:
                    null_pos = data.index(0)
                    print(f"第一个空字节位置: {null_pos}")
                    
                    # 尝试解码到空字节之前的内容
                    if null_pos > 0:
                        try:
                            print(f"\n尝试用 {encoding} 解码到空字节之前的内容:")
                            decoded = data[:null_pos].decode(encoding, errors='replace')
                            print(f"解码结果: \"{decoded}\"")
                        except Exception as e:
                            print(f"解码失败: {str(e)}")
                    else:
                        print("\n字段以空字节开始，表示空字符串")
                except ValueError:
                    print("没有找到空字节，尝试解码整个字段")
                
                # 尝试用不同编码解码整个数据
                encodings = ['utf-8', 'gb2312', 'gbk', 'latin1']
                print("\n使用不同编码尝试解码整个数据:")
                for enc in encodings:
                    try:
                        decoded = data.decode(enc, errors='replace').rstrip('\0')
                        print(f"{enc}: \"{decoded}\"")
                    except Exception as e:
                        print(f"{enc}: 解码失败 - {str(e)}")
        
        except Exception as e:
            print(f"读取文件出错: {str(e)}")

def main():
    if len(sys.argv) < 4:
        print("用法: python binary_debug.py <二进制文件路径> <字段偏移量> <字段大小> [编码格式]")
        print("例如: python binary_debug.py data.bin 132 128 utf-8")
        return
    
    file_path = sys.argv[1]
    offset = int(sys.argv[2])
    size = int(sys.argv[3])
    encoding = sys.argv[4] if len(sys.argv) > 4 else 'utf-8'
    
    debugger = BinaryDebugger(file_path)
    debugger.dump_char_array(offset, size, encoding)

if __name__ == "__main__":
    main() 