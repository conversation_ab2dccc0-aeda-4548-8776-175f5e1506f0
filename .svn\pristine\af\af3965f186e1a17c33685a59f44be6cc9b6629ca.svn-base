('C:\\MyFileAudit\\导表工具2.0\\dist\\导表工具.exe',
 <PERSON>alse,
 <PERSON>alse,
 False,
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-windowed.ico',
 None,
 False,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'C:\\MyFileAudit\\导表工具2.0\\build\\build\\导表工具.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', 'C:\\MyFileAudit\\导表工具2.0\\build\\build\\PYZ-00.pyz', 'PYZ'),
  ('struct',
   'C:\\MyFileAudit\\导表工具2.0\\build\\build\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\MyFileAudit\\导表工具2.0\\build\\build\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\MyFileAudit\\导表工具2.0\\build\\build\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\MyFileAudit\\导表工具2.0\\build\\build\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\MyFileAudit\\导表工具2.0\\build\\build\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('main', 'C:\\MyFileAudit\\导表工具2.0\\main.py', 'PYSOURCE'),
  ('python310.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python310.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes310.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pywin32_system32\\pywintypes310.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom310.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\pywin32_system32\\pythoncom310.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('win32\\win32gui.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\win32gui.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('win32\\win32clipboard.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\win32\\win32clipboard.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_webp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_imagingtk.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_imagingcms.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_imagingmath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PIL\\_imaging.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\sip.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libffi-7.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python3.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\Windows\\system32\\ucrtbase.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('config\\DragonBoatFestival\\CurrencySystem\\Client\\CurrencySystemClient.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\CurrencySystem\\Client\\CurrencySystemClient.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\CurrencySystem\\Client\\CurrencySystemClient.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\CurrencySystem\\Client\\CurrencySystemClient.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\CurrencySystem\\Client\\GameDataCurrencySystemClient.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\CurrencySystem\\Client\\GameDataCurrencySystemClient.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\CurrencySystem\\Server\\CurrencySystemServer.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\CurrencySystem\\Server\\CurrencySystemServer.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\CurrencySystem\\Server\\CurrencySystemServer.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\CurrencySystem\\Server\\CurrencySystemServer.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\CurrencySystem\\Server\\GameDataCurrencySystemServer.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\CurrencySystem\\Server\\GameDataCurrencySystemServer.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatBoard\\Client\\DragonBoatBoardClient.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatBoard\\Client\\DragonBoatBoardClient.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatBoard\\Client\\DragonBoatBoardClient.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatBoard\\Client\\DragonBoatBoardClient.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatBoard\\Client\\GameDataDragonBoatBoardClient.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatBoard\\Client\\GameDataDragonBoatBoardClient.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatBoard\\Server\\DragonBoatBoardServer.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatBoard\\Server\\DragonBoatBoardServer.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatBoard\\Server\\DragonBoatBoardServer.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatBoard\\Server\\DragonBoatBoardServer.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatBoard\\Server\\GameDataDragonBoatBoardServer.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatBoard\\Server\\GameDataDragonBoatBoardServer.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatEvent\\Client\\DragonBoatEventClient.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatEvent\\Client\\DragonBoatEventClient.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatEvent\\Client\\DragonBoatEventClient.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatEvent\\Client\\DragonBoatEventClient.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatEvent\\Client\\GameDataDragonBoatEventClient.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatEvent\\Client\\GameDataDragonBoatEventClient.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatEvent\\Server\\DragonBoatEventServer.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatEvent\\Server\\DragonBoatEventServer.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatEvent\\Server\\DragonBoatEventServer.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatEvent\\Server\\DragonBoatEventServer.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatEvent\\Server\\GameDataDragonBoatEventServer.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatEvent\\Server\\GameDataDragonBoatEventServer.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatGrid\\Client\\DragonBoatGridClient.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatGrid\\Client\\DragonBoatGridClient.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatGrid\\Client\\DragonBoatGridClient.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatGrid\\Client\\DragonBoatGridClient.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatGrid\\Client\\GameDataDragonBoatGridClient.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatGrid\\Client\\GameDataDragonBoatGridClient.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatGrid\\Server\\DragonBoatGridServer.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatGrid\\Server\\DragonBoatGridServer.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatGrid\\Server\\DragonBoatGridServer.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatGrid\\Server\\DragonBoatGridServer.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatGrid\\Server\\GameDataDragonBoatGridServer.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatGrid\\Server\\GameDataDragonBoatGridServer.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatRankReward\\Client\\DragonBoatRankRewardClient.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatRankReward\\Client\\DragonBoatRankRewardClient.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatRankReward\\Client\\DragonBoatRankRewardClient.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatRankReward\\Client\\DragonBoatRankRewardClient.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatRankReward\\Client\\GameDataDragonBoatRankRewardClient.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatRankReward\\Client\\GameDataDragonBoatRankRewardClient.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatRankReward\\Server\\DragonBoatRankRewardServer.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatRankReward\\Server\\DragonBoatRankRewardServer.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatRankReward\\Server\\DragonBoatRankRewardServer.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatRankReward\\Server\\DragonBoatRankRewardServer.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\DragonBoatRankReward\\Server\\GameDataDragonBoatRankRewardServer.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\DragonBoatRankReward\\Server\\GameDataDragonBoatRankRewardServer.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalActController\\Client\\FestivalActControllerClient.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalActController\\Client\\FestivalActControllerClient.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalActController\\Client\\FestivalActControllerClient.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalActController\\Client\\FestivalActControllerClient.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalActController\\Client\\GameDataFestivalActControllerClient.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalActController\\Client\\GameDataFestivalActControllerClient.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalActController\\Server\\FestivalActControllerServer.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalActController\\Server\\FestivalActControllerServer.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalActController\\Server\\FestivalActControllerServer.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalActController\\Server\\FestivalActControllerServer.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalActController\\Server\\GameDataFestivalActControllerServer.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalActController\\Server\\GameDataFestivalActControllerServer.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalActSignIn\\Client\\FestivalActSignInClient.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalActSignIn\\Client\\FestivalActSignInClient.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalActSignIn\\Client\\FestivalActSignInClient.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalActSignIn\\Client\\FestivalActSignInClient.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalActSignIn\\Client\\GameDataFestivalActSignInClient.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalActSignIn\\Client\\GameDataFestivalActSignInClient.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalActSignIn\\Server\\FestivalActSignInServer.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalActSignIn\\Server\\FestivalActSignInServer.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalActSignIn\\Server\\FestivalActSignInServer.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalActSignIn\\Server\\FestivalActSignInServer.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalActSignIn\\Server\\GameDataFestivalActSignInServer.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalActSignIn\\Server\\GameDataFestivalActSignInServer.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalActWarOrderLevel\\Client\\FestivalActWarOrderLevelClient.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalActWarOrderLevel\\Client\\FestivalActWarOrderLevelClient.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalActWarOrderLevel\\Client\\FestivalActWarOrderLevelClient.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalActWarOrderLevel\\Client\\FestivalActWarOrderLevelClient.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalActWarOrderLevel\\Client\\GameDataFestivalActWarOrderLevelClient.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalActWarOrderLevel\\Client\\GameDataFestivalActWarOrderLevelClient.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalActWarOrderLevel\\Server\\FestivalActWarOrderLevelServer.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalActWarOrderLevel\\Server\\FestivalActWarOrderLevelServer.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalActWarOrderLevel\\Server\\FestivalActWarOrderLevelServer.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalActWarOrderLevel\\Server\\FestivalActWarOrderLevelServer.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalActWarOrderLevel\\Server\\GameDataFestivalActWarOrderLevelServer.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalActWarOrderLevel\\Server\\GameDataFestivalActWarOrderLevelServer.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalAct\\Client\\FestivalActClient.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalAct\\Client\\FestivalActClient.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalAct\\Client\\FestivalActClient.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalAct\\Client\\FestivalActClient.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalAct\\Client\\GameDataFestivalActClient.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalAct\\Client\\GameDataFestivalActClient.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalAct\\Server\\FestivalActServer.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalAct\\Server\\FestivalActServer.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalAct\\Server\\FestivalActServer.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalAct\\Server\\FestivalActServer.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalAct\\Server\\GameDataFestivalActServer.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalAct\\Server\\GameDataFestivalActServer.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalQuestActive\\Client\\FestivalQuestActiveClient.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalQuestActive\\Client\\FestivalQuestActiveClient.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalQuestActive\\Client\\FestivalQuestActiveClient.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalQuestActive\\Client\\FestivalQuestActiveClient.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalQuestActive\\Client\\GameDataFestivalQuestActiveClient.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalQuestActive\\Client\\GameDataFestivalQuestActiveClient.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalQuestActive\\Server\\FestivalQuestActiveServer.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalQuestActive\\Server\\FestivalQuestActiveServer.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalQuestActive\\Server\\FestivalQuestActiveServer.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalQuestActive\\Server\\FestivalQuestActiveServer.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalQuestActive\\Server\\GameDataFestivalQuestActiveServer.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalQuestActive\\Server\\GameDataFestivalQuestActiveServer.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalQuest\\Client\\FestivalQuestClient.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalQuest\\Client\\FestivalQuestClient.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalQuest\\Client\\FestivalQuestClient.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalQuest\\Client\\FestivalQuestClient.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalQuest\\Client\\GameDataFestivalQuestClient.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalQuest\\Client\\GameDataFestivalQuestClient.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalQuest\\Server\\FestivalQuestServer.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalQuest\\Server\\FestivalQuestServer.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalQuest\\Server\\FestivalQuestServer.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalQuest\\Server\\FestivalQuestServer.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalQuest\\Server\\GameDataFestivalQuestServer.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalQuest\\Server\\GameDataFestivalQuestServer.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalShop\\Client\\FestivalShopClient.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalShop\\Client\\FestivalShopClient.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalShop\\Client\\FestivalShopClient.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalShop\\Client\\FestivalShopClient.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalShop\\Client\\GameDataFestivalShopClient.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalShop\\Client\\GameDataFestivalShopClient.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalShop\\Server\\FestivalShopServer.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalShop\\Server\\FestivalShopServer.SData',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalShop\\Server\\FestivalShopServer.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalShop\\Server\\FestivalShopServer.SData.txt',
   'DATA'),
  ('config\\DragonBoatFestival\\FestivalShop\\Server\\GameDataFestivalShopServer.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\DragonBoatFestival\\FestivalShop\\Server\\GameDataFestivalShopServer.txt',
   'DATA'),
  ('config\\test1.xlsx',
   'C:\\MyFileAudit\\导表工具2.0\\config\\test1.xlsx',
   'DATA'),
  ('config\\test1\\Sheet1\\Client\\GameDataSheet1Client.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\test1\\Sheet1\\Client\\GameDataSheet1Client.txt',
   'DATA'),
  ('config\\test1\\Sheet1\\Client\\Sheet1Client.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\test1\\Sheet1\\Client\\Sheet1Client.SData',
   'DATA'),
  ('config\\test1\\Sheet1\\Client\\Sheet1Client.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\test1\\Sheet1\\Client\\Sheet1Client.SData.txt',
   'DATA'),
  ('config\\test1\\Sheet1\\Server\\GameDataSheet1Server.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\test1\\Sheet1\\Server\\GameDataSheet1Server.txt',
   'DATA'),
  ('config\\test1\\Sheet1\\Server\\Sheet1Server.SData',
   'C:\\MyFileAudit\\导表工具2.0\\config\\test1\\Sheet1\\Server\\Sheet1Server.SData',
   'DATA'),
  ('config\\test1\\Sheet1\\Server\\Sheet1Server.SData.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\test1\\Sheet1\\Server\\Sheet1Server.SData.txt',
   'DATA'),
  ('config\\示例表格说明.txt',
   'C:\\MyFileAudit\\导表工具2.0\\config\\示例表格说明.txt',
   'DATA'),
  ('config\\节日活动配置(适配端午).xlsx',
   'C:\\MyFileAudit\\导表工具2.0\\config\\节日活动配置(适配端午).xlsx',
   'DATA'),
  ('resources\\icons\\app-icon-128.png',
   'C:\\MyFileAudit\\导表工具2.0\\resources\\icons\\app-icon-128.png',
   'DATA'),
  ('resources\\icons\\app-icon-16.png',
   'C:\\MyFileAudit\\导表工具2.0\\resources\\icons\\app-icon-16.png',
   'DATA'),
  ('resources\\icons\\app-icon-256.png',
   'C:\\MyFileAudit\\导表工具2.0\\resources\\icons\\app-icon-256.png',
   'DATA'),
  ('resources\\icons\\app-icon-32.png',
   'C:\\MyFileAudit\\导表工具2.0\\resources\\icons\\app-icon-32.png',
   'DATA'),
  ('resources\\icons\\app-icon-48.png',
   'C:\\MyFileAudit\\导表工具2.0\\resources\\icons\\app-icon-48.png',
   'DATA'),
  ('resources\\icons\\app-icon-64.png',
   'C:\\MyFileAudit\\导表工具2.0\\resources\\icons\\app-icon-64.png',
   'DATA'),
  ('resources\\icons\\branch-closed-dark.png',
   'C:\\MyFileAudit\\导表工具2.0\\resources\\icons\\branch-closed-dark.png',
   'DATA'),
  ('resources\\icons\\branch-closed.png',
   'C:\\MyFileAudit\\导表工具2.0\\resources\\icons\\branch-closed.png',
   'DATA'),
  ('resources\\icons\\branch-open-dark.png',
   'C:\\MyFileAudit\\导表工具2.0\\resources\\icons\\branch-open-dark.png',
   'DATA'),
  ('resources\\icons\\branch-open.png',
   'C:\\MyFileAudit\\导表工具2.0\\resources\\icons\\branch-open.png',
   'DATA'),
  ('resources\\icons\\excel-file-24.png',
   'C:\\MyFileAudit\\导表工具2.0\\resources\\icons\\excel-file-24.png',
   'DATA'),
  ('resources\\icons\\excel-file.png',
   'C:\\MyFileAudit\\导表工具2.0\\resources\\icons\\excel-file.png',
   'DATA'),
  ('resources\\icons\\function-file.png',
   'C:\\MyFileAudit\\导表工具2.0\\resources\\icons\\function-file.png',
   'DATA'),
  ('resources\\icons\\path-settings.png',
   'C:\\MyFileAudit\\导表工具2.0\\resources\\icons\\path-settings.png',
   'DATA'),
  ('resources\\icons\\sdata-file.png',
   'C:\\MyFileAudit\\导表工具2.0\\resources\\icons\\sdata-file.png',
   'DATA'),
  ('resources\\icons\\struct-file.png',
   'C:\\MyFileAudit\\导表工具2.0\\resources\\icons\\struct-file.png',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('base_library.zip',
   'C:\\MyFileAudit\\导表工具2.0\\build\\build\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 1750901577,
 [('runw.exe',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\runw.exe',
   'EXECUTABLE')],
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python310.dll')
