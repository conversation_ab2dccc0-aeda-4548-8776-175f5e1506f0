# 中文字符串编码修复说明

## 问题描述

在导表工具项目中，C++解析出来的二进制数据中的字符数组(char[])数据有问题，所有字符串字段（如cIconPath、szName、szDesc）显示为空字符串或乱码，但写入txt文件的数据是正确的。

## 问题原因

问题出在`core/type_converter.py`文件中处理char[]类型的部分，使用了UTF-8编码处理中文字符串，而C++端使用的是GB2312编码（根据VS项目文件设置CharacterSet="2"），导致编码不匹配，C++端在读取二进制数据时无法正确解析中文字符。

## 修复方案

修复主要集中在两个文件：

1. `core/type_converter.py`：修改字符串编码方式，从UTF-8改为GB2312
2. `utils/sdata_parser.py`：修改解析逻辑，优先使用GB2312解码

具体修改包括：

1. 将所有字符串编码从UTF-8改为GB2312
2. 修改字符串截断逻辑，确保不会破坏GB2312编码的完整性
3. 更新解析逻辑，优先使用GB2312解码二进制数据

关键代码修改（以`type_converter.py`中一维char数组处理为例）：

```python
# 特殊处理 char[] 类型，作为字符串处理
if base_type == 'char':
    # 处理字符串类型 char[N]
    if value is None or value == "":
        return b'\x00' * size
    
    # 转换为字符串
    value_str = str(value)
    
    # 使用GB2312编码处理中文字符串，使C++端能正确解析
    try:
        # 使用GB2312编码而不是UTF-8，与C++端项目设置一致
        gb_bytes = value_str.encode('gb2312')
        
        # 预留终止符的位置
        max_len = size - 1
        
        # 如果GB2312字节数超过限制，需要截断
        if len(gb_bytes) > max_len:
            # 逐字符编码并检查长度，确保不会在多字节字符中间截断
            result_bytes = b''
            for char in value_str:
                char_bytes = char.encode('gb2312')
                # 检查添加这个字符后是否会超过限制
                if len(result_bytes) + len(char_bytes) <= max_len:
                    result_bytes += char_bytes
                else:
                    break
            
            gb_bytes = result_bytes
        
        # 添加终止符并填充到指定长度
        result = gb_bytes + b'\x00' * (size - len(gb_bytes))
        return result
    except Exception as e:
        # 编码失败，返回空字符串
        return b'\x00' * size
```

## 测试过程

修复后进行了多种测试，包括：

1. 基本字符串转换测试
2. 各种长度限制下的截断测试
3. 二进制文件读写测试
4. 使用SDataParser解析编码后的文件

所有测试都成功通过，证明修复方案有效。

## 注意事项

这个修复确保了：
1. 所有中文字符串都能正确转换为GB2312编码的二进制数据
2. 字符串截断时不会破坏GB2312编码的完整性
3. 二进制文件可以被C++程序正确读取和解析
4. 解析器可以正确解析GB2312编码的二进制数据

## C++项目说明

C++项目默认使用GB2312编码（在项目设置中CharacterSet="2"），这是Windows中文版常用的编码方式。修改后的Python代码使用相同的GB2312编码，确保两端编码一致，解决了字符串解析问题。 