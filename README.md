# 配置导表工具 v2.0

这是一个用于处理Excel配置文件并导出为多种格式的工具，支持导出结构体定义、SData二进制文件等。

## 依赖安装

### 自动安装（推荐）

运行依赖安装脚本：
```bash
python install_dependencies.py
```

### 手动安装

如果自动安装失败，可以手动安装依赖：
```bash
pip install -r requirements.txt
```

**注意**: 本工具需要 `pywin32` 库来支持文件复制功能。如果您在使用过程中遇到 "缺少pywin32库" 的错误，请确保已正确安装此依赖。

## 构建可执行文件

### 一键构建
```bash
setup_and_build.bat
```

### 手动构建
```bash
python build.py
```

构建完成后，可执行文件将位于 `dist/导表工具.exe`

## 文件说明

- `test_binary_reader.cpp` - C++测试程序，用于读取并打印二进制数据文件
- `generate_test_data.py` - Python脚本，用于生成测试用的二进制数据文件
- `compile_instructions.txt` - 编译和运行说明
- `README.md` - 本说明文档

## 快速开始

### 步骤1: 生成测试数据

运行Python脚本生成测试数据文件：

```
python generate_test_data.py [记录数量]
```

这将生成两个数据文件：
- `FestivalQuestClient.SData` - UTF-8编码的数据文件
- `FestivalQuestClient_GB2312.SData` - GB2312编码的数据文件(适合VS2008等旧编译器)

### 步骤2: 编译C++程序

#### 使用VS2008

1. 打开VS2008命令提示符
2. 导航到源代码目录
3. 执行：`cl test_binary_reader.cpp`

#### 使用其他编译器

参见 `compile_instructions.txt` 中的编译说明

### 步骤3: 运行测试程序

```
test_binary_reader FestivalQuestClient_GB2312.SData
```

若使用VS2008或其他不支持UTF-8的旧编译器，请使用GB2312编码的数据文件。

## 数据结构

程序中定义的结构体如下：

```cpp
struct stFestivalQuestClientCfg
{
    DWORD    dwId;            // id
    DWORD    dwActId[2][3];   // 对应活动id/签到组id
    BYTE     byResetType[2];  // 任务的重置类型
    DWORD    byQuestType;     // 任务类型
    char     charTets[128];   // 字符串测试
    DWORD    dwFinishpara;    // 统计数值
    char     charTets2[3][128]; // 字符串测试2
};
```

## 问题排查

1. **字符串乱码问题**
   - 检查C++程序的字符编码设置
   - VS2008等旧编译器可能不支持UTF-8，请使用GB2312编码的数据文件

2. **数据结构解析错误**
   - 确保C++程序与Python脚本中的数据类型定义一致
   - 检查内存对齐设置（程序中已添加 `#pragma pack(1)`）
   - 验证字节序（程序默认使用小端序/little-endian）

3. **文件大小不匹配警告**
   - 验证结构体定义是否正确
   - 检查是否有填充字节影响 