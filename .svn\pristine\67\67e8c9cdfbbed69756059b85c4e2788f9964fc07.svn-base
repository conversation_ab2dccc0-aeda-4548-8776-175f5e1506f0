BOOL CGameData::LoadCurrencySystemServerCfg()
{
    std::string DataPath = "data/CurrencySystemServer.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        ::g_pClientToLog->PrintFileDirct("CDataFile::LoadCurrencySystemCfg fopen error");
        return FALSE;
    }
    
    m_mapCurrencySystemCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stCurrencySystemServerCfg cfg;
        fread(&cfg, sizeof(stCurrencySystemServerCfg), 1, fp);

        m_mapCurrencySystemCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}