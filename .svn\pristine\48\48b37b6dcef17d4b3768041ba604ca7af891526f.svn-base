2025-05-27 15:40:14,525 - INFO - 正在扫描路径: F:/配置表/新导表文件/Config
2025-05-27 15:40:14,525 - INFO - 正在扫描路径: F:/配置表/新导表文件/Config
2025-05-27 15:40:14,526 - INFO - 找到 2 个Excel配置文件
2025-05-27 15:40:14,526 - INFO - 找到 2 个Excel配置文件
2025-05-27 15:40:14,674 - INFO - 找到 26 个SData文件 (服务端: 13, 客户端: 13)
2025-05-27 15:40:14,674 - INFO - 找到 26 个SData文件 (服务端: 13, 客户端: 13)
2025-05-27 15:40:14,675 - INFO - 找到 26 个加载函数文件
2025-05-27 15:40:14,675 - INFO - 找到 26 个加载函数文件
2025-05-27 15:40:17,191 - INFO - 开始导出全部 2 个文件...
2025-05-27 15:40:17,191 - INFO - 开始导出全部 2 个文件...
2025-05-27 15:40:17,203 - INFO - 开始处理: 活动货币表.xlsx
2025-05-27 15:40:17,203 - INFO - 开始处理: 活动货币表.xlsx
2025-05-27 15:40:17,235 - WARNING - 页签 'CurrencySystem' 忽略 A列 (说明: '货币表（非道具不在背包内）')，没有类型或字段名
2025-05-27 15:40:17,235 - WARNING - 页签 'CurrencySystem' 忽略 A列 (说明: '货币表（非道具不在背包内）')，没有类型或字段名
2025-05-27 15:40:17,243 - INFO - 服务端加载函数文件 LoadCurrencySystemServer.txt 生成成功
2025-05-27 15:40:17,243 - INFO - 服务端加载函数文件 LoadCurrencySystemServer.txt 生成成功
2025-05-27 15:40:17,244 - INFO - 客户端加载函数文件 LoadCurrencySystemClient.txt 生成成功
2025-05-27 15:40:17,244 - INFO - 客户端加载函数文件 LoadCurrencySystemClient.txt 生成成功
2025-05-27 15:40:17,244 - INFO - 页签 CurrencySystem 处理完成
2025-05-27 15:40:17,244 - INFO - 页签 CurrencySystem 处理完成
2025-05-27 15:40:17,262 - INFO - 开始处理: 节日活动配置(适配端午).xlsx
2025-05-27 15:40:17,262 - INFO - 开始处理: 节日活动配置(适配端午).xlsx
2025-05-27 15:40:17,315 - WARNING - 页签 'FestivalActController' 忽略 A列 (说明: '活动管理总表')，没有类型或字段名
2025-05-27 15:40:17,315 - WARNING - 页签 'FestivalActController' 忽略 A列 (说明: '活动管理总表')，没有类型或字段名
2025-05-27 15:40:17,318 - WARNING - 页签 'FestivalAct' 忽略 A列 (说明: '活动预告页面信息表')，没有类型或字段名
2025-05-27 15:40:17,321 - WARNING - 页签 'FestivalActSignIn' 忽略 A列 (说明: '签到奖励表')，没有类型或字段名
2025-05-27 15:40:17,324 - WARNING - 页签 'FestivalQuest' 忽略 A列 (说明: '活动任务表')，没有类型或字段名
2025-05-27 15:40:17,318 - WARNING - 页签 'FestivalAct' 忽略 A列 (说明: '活动预告页面信息表')，没有类型或字段名
2025-05-27 15:40:17,324 - WARNING - 页签 'FestivalActController' 忽略 O列 (说明: '类型备注(策划自用)')，没有类型或字段名
2025-05-27 15:40:17,321 - WARNING - 页签 'FestivalActSignIn' 忽略 A列 (说明: '签到奖励表')，没有类型或字段名
2025-05-27 15:40:17,326 - WARNING - 页签 'FestivalAct' 忽略 H列 (说明: '备注
策划自用')，没有类型或字段名
2025-05-27 15:40:17,324 - WARNING - 页签 'FestivalQuest' 忽略 A列 (说明: '活动任务表')，没有类型或字段名
2025-05-27 15:40:17,324 - WARNING - 页签 'FestivalActController' 忽略 O列 (说明: '类型备注(策划自用)')，没有类型或字段名
2025-05-27 15:40:17,329 - WARNING - 页签 'FestivalQuest' 忽略 K列，没有类型或字段名
2025-05-27 15:40:17,326 - WARNING - 页签 'FestivalAct' 忽略 H列 (说明: '备注
策划自用')，没有类型或字段名
2025-05-27 15:40:17,329 - WARNING - 页签 'FestivalQuest' 忽略 K列，没有类型或字段名
2025-05-27 15:40:17,331 - WARNING - 页签 'FestivalQuest' 忽略 L列，没有类型或字段名
2025-05-27 15:40:17,331 - WARNING - 页签 'FestivalQuest' 忽略 L列，没有类型或字段名
2025-05-27 15:40:17,333 - WARNING - 页签 'FestivalQuest' 忽略 M列，没有类型或字段名
2025-05-27 15:40:17,333 - WARNING - 页签 'FestivalQuest' 忽略 M列，没有类型或字段名
2025-05-27 15:40:17,335 - WARNING - 页签 'FestivalQuest' 忽略 N列，没有类型或字段名
2025-05-27 15:40:17,335 - WARNING - 页签 'FestivalQuest' 忽略 N列，没有类型或字段名
2025-05-27 15:40:17,338 - INFO - 服务端加载函数文件 LoadFestivalActSignInServer.txt 生成成功
2025-05-27 15:40:17,338 - INFO - 服务端加载函数文件 LoadFestivalActSignInServer.txt 生成成功
2025-05-27 15:40:17,342 - INFO - 客户端加载函数文件 LoadFestivalActSignInClient.txt 生成成功
2025-05-27 15:40:17,342 - INFO - 客户端加载函数文件 LoadFestivalActSignInClient.txt 生成成功
2025-05-27 15:40:17,344 - INFO - 页签 FestivalActSignIn 处理完成
2025-05-27 15:40:17,344 - INFO - 页签 FestivalActSignIn 处理完成
2025-05-27 15:40:17,346 - INFO - 服务端加载函数文件 LoadFestivalActServer.txt 生成成功
2025-05-27 15:40:17,346 - WARNING - 页签 'FestivalQuestActive' 忽略 A列 (说明: '任务进度奖励表')，没有类型或字段名
2025-05-27 15:40:17,346 - INFO - 服务端加载函数文件 LoadFestivalActServer.txt 生成成功
2025-05-27 15:40:17,346 - WARNING - 页签 'FestivalQuestActive' 忽略 A列 (说明: '任务进度奖励表')，没有类型或字段名
2025-05-27 15:40:17,352 - INFO - 客户端加载函数文件 LoadFestivalActClient.txt 生成成功
2025-05-27 15:40:17,353 - INFO - 服务端加载函数文件 LoadFestivalQuestServer.txt 生成成功
2025-05-27 15:40:17,352 - INFO - 客户端加载函数文件 LoadFestivalActClient.txt 生成成功
2025-05-27 15:40:17,353 - INFO - 服务端加载函数文件 LoadFestivalActControllerServer.txt 生成成功
2025-05-27 15:40:17,353 - INFO - 服务端加载函数文件 LoadFestivalQuestServer.txt 生成成功
2025-05-27 15:40:17,354 - INFO - 页签 FestivalAct 处理完成
2025-05-27 15:40:17,353 - INFO - 服务端加载函数文件 LoadFestivalActControllerServer.txt 生成成功
2025-05-27 15:40:17,354 - INFO - 页签 FestivalAct 处理完成
2025-05-27 15:40:17,355 - INFO - 客户端加载函数文件 LoadFestivalQuestClient.txt 生成成功
2025-05-27 15:40:17,355 - INFO - 客户端加载函数文件 LoadFestivalQuestClient.txt 生成成功
2025-05-27 15:40:17,356 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略 A列 (说明: '活动通行证等级')，没有类型或字段名
2025-05-27 15:40:17,356 - INFO - 客户端加载函数文件 LoadFestivalActControllerClient.txt 生成成功
2025-05-27 15:40:17,357 - INFO - 页签 FestivalQuest 处理完成
2025-05-27 15:40:17,356 - WARNING - 页签 'FestivalActWarOrderLevel' 忽略 A列 (说明: '活动通行证等级')，没有类型或字段名
2025-05-27 15:40:17,356 - INFO - 客户端加载函数文件 LoadFestivalActControllerClient.txt 生成成功
2025-05-27 15:40:17,360 - INFO - 服务端加载函数文件 LoadFestivalQuestActiveServer.txt 生成成功
2025-05-27 15:40:17,357 - INFO - 页签 FestivalQuest 处理完成
2025-05-27 15:40:17,360 - INFO - 页签 FestivalActController 处理完成
2025-05-27 15:40:17,360 - INFO - 服务端加载函数文件 LoadFestivalQuestActiveServer.txt 生成成功
2025-05-27 15:40:17,361 - WARNING - 页签 'FestivalShop' 忽略 A列 (说明: '活动商店表')，没有类型或字段名
2025-05-27 15:40:17,360 - INFO - 页签 FestivalActController 处理完成
2025-05-27 15:40:17,361 - WARNING - 页签 'FestivalShop' 忽略 A列 (说明: '活动商店表')，没有类型或字段名
2025-05-27 15:40:17,365 - WARNING - 页签 'DragonBoatBoard' 忽略 A列 (说明: '棋盘参数总表')，没有类型或字段名
2025-05-27 15:40:17,366 - WARNING - 页签 'FestivalShop' 忽略 O列 (说明: '备注')，没有类型或字段名
2025-05-27 15:40:17,365 - WARNING - 页签 'DragonBoatBoard' 忽略 A列 (说明: '棋盘参数总表')，没有类型或字段名
2025-05-27 15:40:17,366 - INFO - 客户端加载函数文件 LoadFestivalQuestActiveClient.txt 生成成功
2025-05-27 15:40:17,366 - WARNING - 页签 'FestivalShop' 忽略 O列 (说明: '备注')，没有类型或字段名
2025-05-27 15:40:17,366 - INFO - 客户端加载函数文件 LoadFestivalQuestActiveClient.txt 生成成功
2025-05-27 15:40:17,377 - INFO - 服务端加载函数文件 LoadFestivalActWarOrderLevelServer.txt 生成成功
2025-05-27 15:40:17,377 - INFO - 页签 FestivalQuestActive 处理完成
2025-05-27 15:40:17,377 - INFO - 服务端加载函数文件 LoadFestivalActWarOrderLevelServer.txt 生成成功
2025-05-27 15:40:17,377 - INFO - 页签 FestivalQuestActive 处理完成
2025-05-27 15:40:17,379 - WARNING - 页签 'DragonBoatGrid' 忽略 A列 (说明: '龙舟棋盘表')，没有类型或字段名
2025-05-27 15:40:17,379 - WARNING - 页签 'DragonBoatGrid' 忽略 A列 (说明: '龙舟棋盘表')，没有类型或字段名
2025-05-27 15:40:17,380 - INFO - 客户端加载函数文件 LoadFestivalActWarOrderLevelClient.txt 生成成功
2025-05-27 15:40:17,380 - INFO - 客户端加载函数文件 LoadFestivalActWarOrderLevelClient.txt 生成成功
2025-05-27 15:40:17,391 - INFO - 页签 FestivalActWarOrderLevel 处理完成
2025-05-27 15:40:17,391 - INFO - 页签 FestivalActWarOrderLevel 处理完成
2025-05-27 15:40:17,392 - WARNING - 页签 'DragonBoatEvent' 忽略 A列 (说明: '龙舟事件表')，没有类型或字段名
2025-05-27 15:40:17,396 - INFO - 服务端加载函数文件 LoadDragonBoatBoardServer.txt 生成成功
2025-05-27 15:40:17,392 - WARNING - 页签 'DragonBoatEvent' 忽略 A列 (说明: '龙舟事件表')，没有类型或字段名
2025-05-27 15:40:17,396 - INFO - 服务端加载函数文件 LoadDragonBoatBoardServer.txt 生成成功
2025-05-27 15:40:17,403 - WARNING - 页签 'DragonBoatEvent' 忽略 I列 (说明: '自用备注')，没有类型或字段名
2025-05-27 15:40:17,403 - WARNING - 页签 'DragonBoatEvent' 忽略 I列 (说明: '自用备注')，没有类型或字段名
2025-05-27 15:40:17,409 - WARNING - 页签 'DragonBoatEvent' 忽略 J列 (说明: '自用备注')，没有类型或字段名
2025-05-27 15:40:17,409 - INFO - 客户端加载函数文件 LoadDragonBoatBoardClient.txt 生成成功
2025-05-27 15:40:17,409 - WARNING - 页签 'DragonBoatEvent' 忽略 J列 (说明: '自用备注')，没有类型或字段名
2025-05-27 15:40:17,409 - INFO - 服务端加载函数文件 LoadFestivalShopServer.txt 生成成功
2025-05-27 15:40:17,409 - INFO - 客户端加载函数文件 LoadDragonBoatBoardClient.txt 生成成功
2025-05-27 15:40:17,410 - WARNING - 页签 'DragonBoatEvent' 忽略 K列 (说明: '自用备注')，没有类型或字段名
2025-05-27 15:40:17,409 - INFO - 服务端加载函数文件 LoadFestivalShopServer.txt 生成成功
2025-05-27 15:40:17,410 - INFO - 页签 DragonBoatBoard 处理完成
2025-05-27 15:40:17,410 - WARNING - 页签 'DragonBoatEvent' 忽略 K列 (说明: '自用备注')，没有类型或字段名
2025-05-27 15:40:17,410 - INFO - 页签 DragonBoatBoard 处理完成
2025-05-27 15:40:17,422 - INFO - 客户端加载函数文件 LoadFestivalShopClient.txt 生成成功
2025-05-27 15:40:17,423 - WARNING - 页签 'DragonBoatRankReward' 忽略 A列 (说明: '排名档位表')，没有类型或字段名
2025-05-27 15:40:17,422 - INFO - 客户端加载函数文件 LoadFestivalShopClient.txt 生成成功
2025-05-27 15:40:17,423 - WARNING - 页签 'DragonBoatRankReward' 忽略 A列 (说明: '排名档位表')，没有类型或字段名
2025-05-27 15:40:17,427 - INFO - 页签 FestivalShop 处理完成
2025-05-27 15:40:17,427 - INFO - 页签 FestivalShop 处理完成
2025-05-27 15:40:17,429 - INFO - 服务端加载函数文件 LoadDragonBoatGridServer.txt 生成成功
2025-05-27 15:40:17,429 - INFO - 服务端加载函数文件 LoadDragonBoatGridServer.txt 生成成功
2025-05-27 15:40:17,432 - INFO - 客户端加载函数文件 LoadDragonBoatGridClient.txt 生成成功
2025-05-27 15:40:17,432 - INFO - 客户端加载函数文件 LoadDragonBoatGridClient.txt 生成成功
2025-05-27 15:40:17,435 - INFO - 页签 DragonBoatGrid 处理完成
2025-05-27 15:40:17,435 - INFO - 页签 DragonBoatGrid 处理完成
2025-05-27 15:40:17,439 - INFO - 服务端加载函数文件 LoadDragonBoatEventServer.txt 生成成功
2025-05-27 15:40:17,439 - INFO - 服务端加载函数文件 LoadDragonBoatEventServer.txt 生成成功
2025-05-27 15:40:17,439 - INFO - 服务端加载函数文件 LoadDragonBoatRankRewardServer.txt 生成成功
2025-05-27 15:40:17,439 - INFO - 服务端加载函数文件 LoadDragonBoatRankRewardServer.txt 生成成功
2025-05-27 15:40:17,440 - INFO - 客户端加载函数文件 LoadDragonBoatEventClient.txt 生成成功
2025-05-27 15:40:17,440 - INFO - 服务端加载函数文件 LoadFestivalZongziServer.txt 生成成功
2025-05-27 15:40:17,440 - INFO - 客户端加载函数文件 LoadDragonBoatEventClient.txt 生成成功
2025-05-27 15:40:17,441 - INFO - 客户端加载函数文件 LoadDragonBoatRankRewardClient.txt 生成成功
2025-05-27 15:40:17,440 - INFO - 服务端加载函数文件 LoadFestivalZongziServer.txt 生成成功
2025-05-27 15:40:17,441 - INFO - 页签 DragonBoatEvent 处理完成
2025-05-27 15:40:17,441 - INFO - 客户端加载函数文件 LoadDragonBoatRankRewardClient.txt 生成成功
2025-05-27 15:40:17,441 - INFO - 页签 DragonBoatEvent 处理完成
2025-05-27 15:40:17,442 - INFO - 页签 DragonBoatRankReward 处理完成
2025-05-27 15:40:17,442 - INFO - 客户端加载函数文件 LoadFestivalZongziClient.txt 生成成功
2025-05-27 15:40:17,442 - INFO - 页签 DragonBoatRankReward 处理完成
2025-05-27 15:40:17,442 - INFO - 客户端加载函数文件 LoadFestivalZongziClient.txt 生成成功
2025-05-27 15:40:17,443 - INFO - 页签 FestivalZongzi 处理完成
2025-05-27 15:40:17,443 - INFO - 页签 FestivalZongzi 处理完成
2025-05-27 15:40:17,457 - INFO - 导出完成! 成功: 2, 跳过: 0, 失败: 0
2025-05-27 15:40:17,457 - INFO - 导出完成! 成功: 2, 跳过: 0, 失败: 0
2025-05-27 15:40:17,469 - INFO - 找到 26 个SData文件 (服务端: 13, 客户端: 13)
2025-05-27 15:40:17,469 - INFO - 找到 26 个SData文件 (服务端: 13, 客户端: 13)
2025-05-27 15:40:17,470 - INFO - 找到 26 个加载函数文件
2025-05-27 15:40:17,470 - INFO - 找到 26 个加载函数文件
