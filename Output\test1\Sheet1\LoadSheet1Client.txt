BOOL CGameData::LoadSheet1ClientCfg()
{
    std::string DataPath = "data/Sheet1Client.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        GSLogError(":LoadSheet1Cfg Failed!  [%s]", DataPath.c_str());
        return FALSE;
    }
    
    m_mapSheet1Cfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stSheet1ClientCfg cfg;
        fread(&cfg, sizeof(stSheet1ClientCfg), 1, fp);

        m_mapSheet1Cfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}