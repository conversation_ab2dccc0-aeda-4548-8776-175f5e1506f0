# 配置导表工具

## 功能介绍

配置导表工具是一个将Excel格式的游戏配置表转换为游戏引擎可直接读取的二进制数据文件(.SData)和结构说明文件(.txt)的工具。该工具支持多种类型的配置表。

## 主要功能

1. **Excel解析**：解析Excel文件中的表头和数据
2. **二进制转换**：将Excel数据转换为游戏可用的二进制格式
3. **结构定义**：生成C++结构体定义文件
4. **数据可视化**：生成与二进制文件对应的可视化文本
5. **增量生成**：支持根据文件修改时间戳进行增量生成
6. **并行处理**：使用多线程提高处理效率

### 系统要求

- Windows 10及以上版本
- 至少1GB可用内存
- 至少200MB硬盘空间

### 安装

1. 双击`配置导表工具.exe`即可运行程序

## 使用说明

### 基本操作流程

1. 打开软件后，默认配置文件夹路径为当前目录下的`config`文件夹
2. 可以通过「浏览...」按钮修改配置文件夹路径
3. 软件会自动扫描该目录下的所有.xlsx文件
4. 选择一个或多个文件，点击「导出选中」按钮进行导出
5. 或者点击「导出全部」按钮一次性导出所有Excel文件
6. 导出过程将在日志区域显示处理进度和结果

### Excel格式要求

Excel文件格式需遵循以下规范：

- 第一行：字段说明
- 第二行：字段类型（如uint, int, char[n]等）
- 第三行：字段名
- 第四行：生成类型（server/client/all）
- 第五行及以后：具体数据

### 支持的数据类型

基本类型：
- uint（对应C++中的DWORD）
- byte（对应C++中的BYTE）
- ushort（对应C++中的WORD）
- ufloat（对应C++中的FLOAT）
- ulong（对应C++中的DWORD）
- ulonglong（对应C++中的ULONGLONG）
- int（对应C++中的INT）
- double
- bool
- char
- short
- float

数组类型：
- int[n]
- uint[n]
- char[n]（字符串）
- 其他基本类型数组

### 字段名命名规则
命名标准：驼峰式

配置类型    项目类型    命名规则          示例
-uint       DWORD      dw{字段意义}   例如 id      dwId
-byte       BYTE       by{字段意义}   例如 day     byDay
-ushort     WORD       w{字段意义}    例如 len     wLen
-ufloat     FLOAT      f{字段意义}    例如 height  fHeight
-ulong      DWORD      dw{字段意义}   例如 id      dwId
-ulonglong  ULONGLONG  l{字段意义}    例如 id      lId
-int        INT        i{字段意义}    例如 id      iId
-double                d{字段意义}
-bool                  b{字段意义}
-char                  c{字段意义}
-short                 s{字段意义}
-float                 f{字段意义}
数组：
-int[n]     INT[n]     i{字段意义}  
-uint[n]    DWORD[n]   dw{字段意义}        
-char[n]               sz{字段意义}

### 生成文件说明

程序会按照以下目录结构生成文件：

```
配置文件夹/
  └── Excel文件名/
      └── Sheet名/
          ├── GameData{Sheet名}.txt      # C++结构体定义文件
          ├── Server/                    # 服务端文件夹
          │   ├── {Sheet名}Server.SData  # 服务端二进制数据文件
          │   └── {Sheet名}Server.txt    # 服务端数据可视化文件
          └── Client/                    # 客户端文件夹
              ├── {Sheet名}Client.SData  # 客户端二进制数据文件
              └── {Sheet名}Client.txt    # 客户端数据可视化文件
```

## 常见问题

### Q: 为什么某些Excel文件没有生成二进制文件？
A: 请检查Excel格式是否符合要求，表格的前4行必须包含字段说明、类型、名称和生成目标。

### Q: 如何强制重新生成所有文件？
A: 勾选「强制生成」复选框，然后点击「导出全部」按钮。

### Q: 程序支持哪些Excel文件格式？
A: 程序支持.xlsx格式的Excel文件。

## 联系与支持

如有任何问题或建议，请联系技术二组-寇晓晨。 