#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.type_converter import TypeConverter
import ast

def test_2d_array_conversion():
    converter = TypeConverter()
    
    # 测试二维数组类型转换
    cpp_type = converter.get_cpp_type("int[3][4]")
    print(f"类型转换测试 - int[3][4] => {cpp_type}")
    
    # 测试二维数组二进制转换
    print("\n二维数组二进制转换测试:")
    test_cases = [
        ("int[2][3]", "[[1, 2, 3], [4, 5, 6]]"),
        ("int[2][3]", "[[1, 2], [4, 5]]"),             # 列数不足
        ("int[2][3]", "[[1, 2, 3, 4], [5, 6, 7, 8]]"), # 列数超出
        ("int[2][3]", "[[1, 2, 3]]"),                  # 行数不足
        ("int[2][3]", "[[1, 2, 3], [4, 5, 6], [7, 8, 9]]"), # 行数超出
        ("float[2][2]", "[[1.1, 2.2], [3.3, 4.4]]"),
        ("byte[2][2]", "[[1, 2], [3, 4]]"),
        ("char[2][3]", None),                          # 空值测试
    ]
    
    for type_str, value_str in test_cases:
        if value_str:
            value = ast.literal_eval(value_str)
        else:
            value = None
        
        binary_data = converter.convert_to_binary(value, type_str)
        print(f"类型: {type_str}, 值: {value_str}")
        print(f"二进制长度: {len(binary_data)} bytes, 数据: {binary_data[:20]}...")
        print("-" * 50)
    
    # 测试不规则二维数组解析
    print("\n不规则二维数组解析测试:")
    irregular_cases = [
        ("int[2][3]", "[1, 2, 3, 4, 5, 6]"),       # 一维数组转二维
        ("int[2][3]", "1"),                         # 单值转二维
        ("float[2][2]", "[[1.1], [2.2]]"),          # 列数不一致
        ("int[2][2]", "[1, [2, 3]]"),               # 混合格式
    ]
    
    for type_str, value_str in irregular_cases:
        if value_str:
            try:
                value = ast.literal_eval(value_str)
            except:
                value = value_str
        else:
            value = None
        
        binary_data = converter.convert_to_binary(value, type_str)
        print(f"类型: {type_str}, 值: {value_str}")
        print(f"二进制长度: {len(binary_data)} bytes, 数据: {binary_data[:20]}...")
        print("-" * 50)

if __name__ == "__main__":
    test_2d_array_conversion() 