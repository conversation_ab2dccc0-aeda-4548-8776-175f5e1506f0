BOOL CGameData::LoadFestivalActServerCfg()
{
    std::string DataPath = "data/FestivalActServer.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        ::g_pClientToLog->PrintFileDirct("CDataFile::LoadFestivalActCfg fopen error");
        return FALSE;
    }
    
    m_mapFestivalActCfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stFestivalActServerCfg cfg;
        fread(&cfg, sizeof(stFestivalActServerCfg), 1, fp);

        m_mapFestivalActCfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}