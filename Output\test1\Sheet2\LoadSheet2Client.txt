BOOL CGameData::LoadSheet2ClientCfg()
{
    std::string DataPath = "data/Sheet2Client.SData"；
    FILE* fp = fopen(DataPath.c_str(), "rb");
    if (fp == NULL)
    {
        GSLogError(":LoadSheet2Cfg Failed!  [%s]", DataPath.c_str());
        return FALSE;
    }
    
    m_mapSheet2Cfg.clear();

    int nSize = 0;
    fread(&nSize, sizeof(nSize), 1, fp);

    for (int i = 0; i < nSize; i++)
    {
        stSheet2ClientCfg cfg;
        fread(&cfg, sizeof(stSheet2ClientCfg), 1, fp);

        m_mapSheet2Cfg[cfg.dwId] = cfg；
    }

    fclose(fp);
    return TRUE;
}