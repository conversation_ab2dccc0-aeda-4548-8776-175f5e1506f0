#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QFileDialog, QTableWidget,
                            QTableWidgetItem, QHeaderView, QAbstractItemView,
                            QMessageBox, QDialogButtonBox, QGroupBox, QScrollArea,
                            QWidget)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon


class SDataPathDialog(QDialog):
    def __init__(self, parent=None, excel_files=None, config_path="", sdata_paths=None):
        super().__init__(parent)
        self.parent_window = parent
        self.excel_files = excel_files or []
        self.config_path = config_path
        self.sdata_paths = sdata_paths or {}
        self.sdata_items = []  # 存储所有SData项目信息
        
        self.setWindowTitle("SData导出位置设置")
        self.setModal(True)
        self.resize(800, 600)
        
        self.init_ui()
        self.load_sdata_items()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 说明标签
        info_label = QLabel("为每个Excel文件的每个页签设置Client.SData和Server.SData的导出目录：")
        info_label.setStyleSheet("font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(info_label)

        # 提示标签
        tip_label = QLabel("注意：请选择目录路径，SData文件将直接生成在所选目录中")
        tip_label.setStyleSheet("color: #666; font-size: 12px; margin-bottom: 5px;")
        layout.addWidget(tip_label)
        
        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["Excel文件", "SData文件名", "导出目录"])
        
        # 设置表格属性
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.setAlternatingRowColors(True)
        
        # 设置列宽
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Excel文件列
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # SData文件名列
        header.setSectionResizeMode(2, QHeaderView.Stretch)           # 导出目录列
        
        layout.addWidget(self.table)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 批量设置按钮
        batch_set_button = QPushButton("批量设置路径")
        batch_set_button.setToolTip("为所有SData文件设置相同的基础路径")
        batch_set_button.clicked.connect(self.batch_set_path)
        button_layout.addWidget(batch_set_button)
        
        # 清空所有路径按钮
        clear_all_button = QPushButton("清空所有路径")
        clear_all_button.setToolTip("清空所有SData文件的导出路径设置")
        clear_all_button.clicked.connect(self.clear_all_paths)
        button_layout.addWidget(clear_all_button)
        
        button_layout.addStretch()
        
        # 对话框按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept_settings)
        button_box.rejected.connect(self.reject)
        button_layout.addWidget(button_box)
        
        layout.addLayout(button_layout)
        
    def load_sdata_items(self):
        """加载所有SData项目"""
        self.sdata_items.clear()
        
        # 扫描所有Excel文件，获取页签信息
        for excel_file in self.excel_files:
            excel_path = os.path.join(self.config_path, excel_file)
            if not os.path.exists(excel_path):
                continue
                
            try:
                import openpyxl
                workbook = openpyxl.load_workbook(excel_path, data_only=True)
                
                for sheet_name in workbook.sheetnames:
                    # 检查页签是否有效（至少5行数据）
                    sheet = workbook[sheet_name]
                    if sheet.max_row < 5:
                        continue
                    
                    # 读取第4行的生成类型信息
                    field_targets = [cell.value for cell in sheet[4]]
                    
                    # 检查是否有server或client字段
                    has_server = any(target in ['server', 'all'] for target in field_targets if target)
                    has_client = any(target in ['client', 'all'] for target in field_targets if target)
                    
                    excel_name = os.path.splitext(excel_file)[0]
                    
                    if has_server:
                        server_key = f"{excel_name}_{sheet_name}_Server"
                        self.sdata_items.append({
                            'excel_file': excel_file,
                            'sheet_name': sheet_name,
                            'type': 'Server.SData',
                            'key': server_key,
                            'path': self.sdata_paths.get(server_key, '')
                        })
                    
                    if has_client:
                        client_key = f"{excel_name}_{sheet_name}_Client"
                        self.sdata_items.append({
                            'excel_file': excel_file,
                            'sheet_name': sheet_name,
                            'type': 'Client.SData',
                            'key': client_key,
                            'path': self.sdata_paths.get(client_key, '')
                        })
                
                workbook.close()
                
            except Exception as e:
                if self.parent_window and hasattr(self.parent_window, 'logger'):
                    self.parent_window.logger.error(f"读取Excel文件失败 {excel_file}: {str(e)}")
        
        self.populate_table()
        
    def populate_table(self):
        """填充表格数据"""
        self.table.setRowCount(len(self.sdata_items))
        
        for row, item in enumerate(self.sdata_items):
            # Excel文件名
            excel_item = QTableWidgetItem(item['excel_file'])
            excel_item.setFlags(excel_item.flags() & ~Qt.ItemIsEditable)
            self.table.setItem(row, 0, excel_item)
            
            # SData文件名（页签名+类型）
            sdata_filename = f"{item['sheet_name']}{item['type'].replace('.SData', '.SData')}"
            sdata_item = QTableWidgetItem(sdata_filename)
            sdata_item.setFlags(sdata_item.flags() & ~Qt.ItemIsEditable)
            self.table.setItem(row, 1, sdata_item)
            
            # 路径（可编辑）
            path_widget = QWidget()
            path_layout = QHBoxLayout(path_widget)
            path_layout.setContentsMargins(2, 2, 2, 2)
            
            path_edit = QLineEdit(item['path'])
            path_edit.setPlaceholderText("选择SData文件导出目录...")
            path_edit.textChanged.connect(lambda text, r=row: self.on_path_changed(r, text))
            
            browse_button = QPushButton("浏览")
            browse_button.setMaximumWidth(60)
            browse_button.clicked.connect(lambda checked, r=row: self.browse_path(r))
            
            path_layout.addWidget(path_edit)
            path_layout.addWidget(browse_button)
            
            self.table.setCellWidget(row, 2, path_widget)
            
    def on_path_changed(self, row, text):
        """路径文本改变时更新数据"""
        if row < len(self.sdata_items):
            self.sdata_items[row]['path'] = text
            
    def browse_path(self, row):
        """浏览选择路径"""
        if row >= len(self.sdata_items):
            return

        item = self.sdata_items[row]
        current_path = item['path']

        # 如果当前路径为空，使用默认路径
        if not current_path:
            current_path = os.path.expanduser("~")
        elif os.path.isfile(current_path):
            current_path = os.path.dirname(current_path)
        elif not os.path.exists(current_path):
            current_path = os.path.expanduser("~")

        # 选择目录而不是文件
        dir_path = QFileDialog.getExistingDirectory(
            self,
            f"选择{item['type']}导出目录",
            current_path
        )

        if dir_path:
            # 更新路径
            self.sdata_items[row]['path'] = dir_path

            # 更新界面
            path_widget = self.table.cellWidget(row, 2)
            if path_widget:
                path_edit = path_widget.findChild(QLineEdit)
                if path_edit:
                    path_edit.setText(dir_path)
                    
    def batch_set_path(self):
        """批量设置路径"""
        base_path = QFileDialog.getExistingDirectory(self, "选择SData文件导出基础目录")
        if not base_path:
            return

        for row, item in enumerate(self.sdata_items):
            # 直接使用基础路径，不创建额外的文件夹结构
            # 更新数据
            self.sdata_items[row]['path'] = base_path

            # 更新界面
            path_widget = self.table.cellWidget(row, 2)
            if path_widget:
                path_edit = path_widget.findChild(QLineEdit)
                if path_edit:
                    path_edit.setText(base_path)
                    
    def clear_all_paths(self):
        """清空所有路径"""
        reply = QMessageBox.question(
            self, 
            "确认清空", 
            "确定要清空所有SData文件的导出路径设置吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            for row, item in enumerate(self.sdata_items):
                # 更新数据
                self.sdata_items[row]['path'] = ''
                
                # 更新界面
                path_widget = self.table.cellWidget(row, 2)
                if path_widget:
                    path_edit = path_widget.findChild(QLineEdit)
                    if path_edit:
                        path_edit.setText('')
                        
    def validate_paths(self):
        """验证所有路径的有效性"""
        invalid_paths = []

        for item in self.sdata_items:
            path = item['path'].strip()
            if not path:
                continue  # 空路径跳过验证

            # 检查目录是否存在
            if not os.path.exists(path):
                try:
                    os.makedirs(path, exist_ok=True)
                except Exception:
                    invalid_paths.append(f"{item['excel_file']}/{item['sheet_name']}/{item['type']}: 无法创建目录 {path}")
                    continue

            # 检查是否有写入权限
            try:
                test_file = os.path.join(path, "test.tmp")
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)
            except Exception:
                invalid_paths.append(f"{item['excel_file']}/{item['sheet_name']}/{item['type']}: 没有写入权限 {path}")

        return invalid_paths
        
    def accept_settings(self):
        """确认设置"""
        # 验证路径
        invalid_paths = self.validate_paths()
        if invalid_paths:
            error_msg = "以下路径无效：\n\n" + "\n".join(invalid_paths)
            QMessageBox.warning(self, "路径验证失败", error_msg)
            return
            
        # 保存设置
        self.save_settings()
        self.accept()
        
    def save_settings(self):
        """保存设置到父窗口"""
        if self.parent_window:
            # 更新父窗口的sdata_paths
            for item in self.sdata_items:
                if hasattr(self.parent_window, 'sdata_paths'):
                    self.parent_window.sdata_paths[item['key']] = item['path']
                    
    def get_sdata_paths(self):
        """获取当前的SData路径设置"""
        paths = {}
        for item in self.sdata_items:
            paths[item['key']] = item['path']
        return paths
