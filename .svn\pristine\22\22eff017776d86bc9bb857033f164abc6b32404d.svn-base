#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
打包脚本，将Python程序打包为独立的Windows可执行文件
"""

import os
import sys
import shutil
import subprocess

def check_dependencies():
    """检查必要的依赖是否已安装"""
    print("检查依赖...")

    required_modules = [
        'openpyxl',
        'PyQt5',
        'win32clipboard',
        'win32con',
        'win32com.client'
    ]

    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✓ {module}")
        except ImportError:
            print(f"✗ {module} 未安装")
            missing_modules.append(module)

    if missing_modules:
        print(f"\n❌ 缺少依赖: {', '.join(missing_modules)}")
        print("请先运行: python install_dependencies.py")
        return False

    print("✓ 所有依赖检查通过")
    return True

def build_exe():
    print("开始打包配置导表工具...")

    # 检查依赖
    if not check_dependencies():
        return False

    # 清理旧的打包文件
    for dir_name in ["build", "dist"]:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
    
    # 使用PyInstaller打包，使用build.spec配置文件
    cmd = [
        "pyinstaller",
        "--clean",  # 清理缓存
        "build.spec"
    ]
    
    # 执行打包命令
    try:
        subprocess.check_call(cmd)
        print("打包完成!")
        print(f"可执行文件路径: {os.path.abspath('dist/导表工具.exe')}")
    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    # 创建resources目录
    if not os.path.exists("resources"):
        os.makedirs("resources")
    
    # 创建icon.ico示例文件（如果不存在）
    icon_path = "resources/icon.ico"
    if not os.path.exists(icon_path):
        print("警告: 图标文件不存在，请手动添加icon.ico文件到resources目录")
    
    if build_exe():
        print("打包成功！")
    else:
        print("打包失败，请检查错误信息。") 