# 二进制文件读取测试工具

这个项目提供了一组工具，用于测试二进制数据文件的生成和读取。主要适用于通过Python生成二进制数据，然后由C++程序读取的场景。

## 文件说明

- `test_binary_reader.cpp` - C++测试程序，用于读取并打印二进制数据文件
- `generate_test_data.py` - Python脚本，用于生成测试用的二进制数据文件
- `compile_instructions.txt` - 编译和运行说明
- `README.md` - 本说明文档

## 快速开始

### 步骤1: 生成测试数据

运行Python脚本生成测试数据文件：

```
python generate_test_data.py [记录数量]
```

这将生成两个数据文件：
- `FestivalQuestClient.SData` - UTF-8编码的数据文件
- `FestivalQuestClient_GB2312.SData` - GB2312编码的数据文件(适合VS2008等旧编译器)

### 步骤2: 编译C++程序

#### 使用VS2008

1. 打开VS2008命令提示符
2. 导航到源代码目录
3. 执行：`cl test_binary_reader.cpp`

#### 使用其他编译器

参见 `compile_instructions.txt` 中的编译说明

### 步骤3: 运行测试程序

```
test_binary_reader FestivalQuestClient_GB2312.SData
```

若使用VS2008或其他不支持UTF-8的旧编译器，请使用GB2312编码的数据文件。

## 数据结构

程序中定义的结构体如下：

```cpp
struct stFestivalQuestClientCfg
{
    DWORD    dwId;            // id
    DWORD    dwActId[2][3];   // 对应活动id/签到组id
    BYTE     byResetType[2];  // 任务的重置类型
    DWORD    byQuestType;     // 任务类型
    char     charTets[128];   // 字符串测试
    DWORD    dwFinishpara;    // 统计数值
    char     charTets2[3][128]; // 字符串测试2
};
```

## 问题排查

1. **字符串乱码问题**
   - 检查C++程序的字符编码设置
   - VS2008等旧编译器可能不支持UTF-8，请使用GB2312编码的数据文件

2. **数据结构解析错误**
   - 确保C++程序与Python脚本中的数据类型定义一致
   - 检查内存对齐设置（程序中已添加 `#pragma pack(1)`）
   - 验证字节序（程序默认使用小端序/little-endian）

3. **文件大小不匹配警告**
   - 验证结构体定义是否正确
   - 检查是否有填充字节影响 